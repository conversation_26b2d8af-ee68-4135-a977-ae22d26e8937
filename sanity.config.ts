import { assist } from '@sanity/assist'
import { documentInternationalization } from '@sanity/document-internationalization'
import { visionTool } from '@sanity/vision'
import { defineConfig } from 'sanity'
import { media } from 'sanity-plugin-media'
import { structureTool } from 'sanity/structure'
import { languages } from './languages'
import { schemaTypes } from './schemaTypes'
import { structure } from './structure'
import {imageAssetPickerPlugin} from 'sanity-plugin-image-asset-picker';

export default defineConfig({
  name: 'default',
  // title: 'Avon Safaris',

  projectId: 'dtgxqbm7',
  dataset: 'production',
  studioHost: 'avonsafaris',

  plugins: [
    structureTool({ structure }),
    visionTool(),
    media(),
    imageAssetPickerPlugin(),
    documentInternationalization({
      supportedLanguages: languages,
      schemaTypes: [
        'safariPackage',
        'safariPackageCategory',
        'post',
        'destination',
        'termsAndConditions',
        'cookiePolicy',
        'qualityAssurance',
        'privacyPolicy',
        'accommodation',
      ],
    }),
    assist({
      translate: {
        document: {
          languageField: 'language',
        },
      },
    }),
  ],

  schema: {
    types: schemaTypes,
  },
})
