{"version": 3, "file": "stegaEncodeSourceMap-BSIC-ILz.js", "sources": ["../../../../../node_modules/@sanity/client/dist/_chunks-es/stegaEncodeSourceMap.js"], "sourcesContent": ["import { C } from \"./stegaClean.js\";\nconst reKeySegment = /_key\\s*==\\s*['\"](.*)['\"]/;\nfunction isKeySegment(segment) {\n  return typeof segment == \"string\" ? reKeySegment.test(segment.trim()) : typeof segment == \"object\" && \"_key\" in segment;\n}\nfunction toString(path) {\n  if (!Array.isArray(path))\n    throw new Error(\"Path is not an array\");\n  return path.reduce((target, segment, i) => {\n    const segmentType = typeof segment;\n    if (segmentType === \"number\")\n      return `${target}[${segment}]`;\n    if (segmentType === \"string\")\n      return `${target}${i === 0 ? \"\" : \".\"}${segment}`;\n    if (isKeySegment(segment) && segment._key)\n      return `${target}[_key==\"${segment._key}\"]`;\n    if (Array.isArray(segment)) {\n      const [from, to] = segment;\n      return `${target}[${from}:${to}]`;\n    }\n    throw new Error(`Unsupported path segment \\`${JSON.stringify(segment)}\\``);\n  }, \"\");\n}\nconst ESCAPE = {\n  \"\\f\": \"\\\\f\",\n  \"\\n\": \"\\\\n\",\n  \"\\r\": \"\\\\r\",\n  \"\t\": \"\\\\t\",\n  \"'\": \"\\\\'\",\n  \"\\\\\": \"\\\\\\\\\"\n}, UNESCAPE = {\n  \"\\\\f\": \"\\f\",\n  \"\\\\n\": `\n`,\n  \"\\\\r\": \"\\r\",\n  \"\\\\t\": \"\t\",\n  \"\\\\'\": \"'\",\n  \"\\\\\\\\\": \"\\\\\"\n};\nfunction jsonPath(path) {\n  return `$${path.map((segment) => typeof segment == \"string\" ? `['${segment.replace(/[\\f\\n\\r\\t'\\\\]/g, (match) => ESCAPE[match])}']` : typeof segment == \"number\" ? `[${segment}]` : segment._key !== \"\" ? `[?(@._key=='${segment._key.replace(/['\\\\]/g, (match) => ESCAPE[match])}')]` : `[${segment._index}]`).join(\"\")}`;\n}\nfunction parseJsonPath(path) {\n  const parsed = [], parseRe = /\\['(.*?)'\\]|\\[(\\d+)\\]|\\[\\?\\(@\\._key=='(.*?)'\\)\\]/g;\n  let match;\n  for (; (match = parseRe.exec(path)) !== null; ) {\n    if (match[1] !== void 0) {\n      const key = match[1].replace(/\\\\(\\\\|f|n|r|t|')/g, (m) => UNESCAPE[m]);\n      parsed.push(key);\n      continue;\n    }\n    if (match[2] !== void 0) {\n      parsed.push(parseInt(match[2], 10));\n      continue;\n    }\n    if (match[3] !== void 0) {\n      const _key = match[3].replace(/\\\\(\\\\')/g, (m) => UNESCAPE[m]);\n      parsed.push({\n        _key,\n        _index: -1\n      });\n      continue;\n    }\n  }\n  return parsed;\n}\nfunction jsonPathToStudioPath(path) {\n  return path.map((segment) => {\n    if (typeof segment == \"string\" || typeof segment == \"number\")\n      return segment;\n    if (segment._key !== \"\")\n      return { _key: segment._key };\n    if (segment._index !== -1)\n      return segment._index;\n    throw new Error(`invalid segment:${JSON.stringify(segment)}`);\n  });\n}\nfunction jsonPathToMappingPath(path) {\n  return path.map((segment) => {\n    if (typeof segment == \"string\" || typeof segment == \"number\")\n      return segment;\n    if (segment._index !== -1)\n      return segment._index;\n    throw new Error(`invalid segment:${JSON.stringify(segment)}`);\n  });\n}\nfunction resolveMapping(resultPath, csm) {\n  if (!csm?.mappings)\n    return;\n  const resultMappingPath = jsonPath(jsonPathToMappingPath(resultPath));\n  if (csm.mappings[resultMappingPath] !== void 0)\n    return {\n      mapping: csm.mappings[resultMappingPath],\n      matchedPath: resultMappingPath,\n      pathSuffix: \"\"\n    };\n  const mappings = Object.entries(csm.mappings).filter(([key]) => resultMappingPath.startsWith(key)).sort(([key1], [key2]) => key2.length - key1.length);\n  if (mappings.length == 0)\n    return;\n  const [matchedPath, mapping] = mappings[0], pathSuffix = resultMappingPath.substring(matchedPath.length);\n  return { mapping, matchedPath, pathSuffix };\n}\nfunction isArray(value) {\n  return value !== null && Array.isArray(value);\n}\nfunction isRecord(value) {\n  return typeof value == \"object\" && value !== null;\n}\nfunction walkMap(value, mappingFn, path = []) {\n  if (isArray(value))\n    return value.map((v, idx) => {\n      if (isRecord(v)) {\n        const _key = v._key;\n        if (typeof _key == \"string\")\n          return walkMap(v, mappingFn, path.concat({ _key, _index: idx }));\n      }\n      return walkMap(v, mappingFn, path.concat(idx));\n    });\n  if (isRecord(value)) {\n    if (value._type === \"block\" || value._type === \"span\") {\n      const result = { ...value };\n      return value._type === \"block\" ? result.children = walkMap(value.children, mappingFn, path.concat(\"children\")) : value._type === \"span\" && (result.text = walkMap(value.text, mappingFn, path.concat(\"text\"))), result;\n    }\n    return Object.fromEntries(\n      Object.entries(value).map(([k, v]) => [k, walkMap(v, mappingFn, path.concat(k))])\n    );\n  }\n  return mappingFn(value, path);\n}\nfunction encodeIntoResult(result, csm, encoder) {\n  return walkMap(result, (value, path) => {\n    if (typeof value != \"string\")\n      return value;\n    const resolveMappingResult = resolveMapping(path, csm);\n    if (!resolveMappingResult)\n      return value;\n    const { mapping, matchedPath } = resolveMappingResult;\n    if (mapping.type !== \"value\" || mapping.source.type !== \"documentValue\")\n      return value;\n    const sourceDocument = csm.documents[mapping.source.document], sourcePath = csm.paths[mapping.source.path], matchPathSegments = parseJsonPath(matchedPath), fullSourceSegments = parseJsonPath(sourcePath).concat(path.slice(matchPathSegments.length));\n    return encoder({\n      sourcePath: fullSourceSegments,\n      sourceDocument,\n      resultPath: path,\n      value\n    });\n  });\n}\nconst DRAFTS_FOLDER = \"drafts\", VERSION_FOLDER = \"versions\", PATH_SEPARATOR = \".\", DRAFTS_PREFIX = `${DRAFTS_FOLDER}${PATH_SEPARATOR}`, VERSION_PREFIX = `${VERSION_FOLDER}${PATH_SEPARATOR}`;\nfunction isDraftId(id) {\n  return id.startsWith(DRAFTS_PREFIX);\n}\nfunction isVersionId(id) {\n  return id.startsWith(VERSION_PREFIX);\n}\nfunction isPublishedId(id) {\n  return !isDraftId(id) && !isVersionId(id);\n}\nfunction getVersionFromId(id) {\n  if (!isVersionId(id)) return;\n  const [_versionPrefix, versionId, ..._publishedId] = id.split(PATH_SEPARATOR);\n  return versionId;\n}\nfunction getPublishedId(id) {\n  return isVersionId(id) ? id.split(PATH_SEPARATOR).slice(2).join(PATH_SEPARATOR) : isDraftId(id) ? id.slice(DRAFTS_PREFIX.length) : id;\n}\nfunction createEditUrl(options) {\n  const {\n    baseUrl,\n    workspace: _workspace = \"default\",\n    tool: _tool = \"default\",\n    id: _id,\n    type,\n    path,\n    projectId,\n    dataset\n  } = options;\n  if (!baseUrl)\n    throw new Error(\"baseUrl is required\");\n  if (!path)\n    throw new Error(\"path is required\");\n  if (!_id)\n    throw new Error(\"id is required\");\n  if (baseUrl !== \"/\" && baseUrl.endsWith(\"/\"))\n    throw new Error(\"baseUrl must not end with a slash\");\n  const workspace = _workspace === \"default\" ? void 0 : _workspace, tool = _tool === \"default\" ? void 0 : _tool, id = getPublishedId(_id), stringifiedPath = Array.isArray(path) ? toString(jsonPathToStudioPath(path)) : path, searchParams = new URLSearchParams({\n    baseUrl,\n    id,\n    type,\n    path: stringifiedPath\n  });\n  if (workspace && searchParams.set(\"workspace\", workspace), tool && searchParams.set(\"tool\", tool), projectId && searchParams.set(\"projectId\", projectId), dataset && searchParams.set(\"dataset\", dataset), isPublishedId(_id))\n    searchParams.set(\"perspective\", \"published\");\n  else if (isVersionId(_id)) {\n    const versionId = getVersionFromId(_id);\n    searchParams.set(\"perspective\", versionId);\n  }\n  const segments = [baseUrl === \"/\" ? \"\" : baseUrl];\n  workspace && segments.push(workspace);\n  const routerParams = [\n    \"mode=presentation\",\n    `id=${id}`,\n    `type=${type}`,\n    `path=${encodeURIComponent(stringifiedPath)}`\n  ];\n  return tool && routerParams.push(`tool=${tool}`), segments.push(\"intent\", \"edit\", `${routerParams.join(\";\")}?${searchParams}`), segments.join(\"/\");\n}\nfunction resolveStudioBaseRoute(studioUrl) {\n  let baseUrl = typeof studioUrl == \"string\" ? studioUrl : studioUrl.baseUrl;\n  return baseUrl !== \"/\" && (baseUrl = baseUrl.replace(/\\/$/, \"\")), typeof studioUrl == \"string\" ? { baseUrl } : { ...studioUrl, baseUrl };\n}\nconst filterDefault = ({ sourcePath, resultPath, value }) => {\n  if (isValidDate(value) || isValidURL(value))\n    return !1;\n  const endPath = sourcePath.at(-1);\n  return !(sourcePath.at(-2) === \"slug\" && endPath === \"current\" || typeof endPath == \"string\" && (endPath.startsWith(\"_\") || endPath.endsWith(\"Id\")) || sourcePath.some(\n    (path) => path === \"meta\" || path === \"metadata\" || path === \"openGraph\" || path === \"seo\"\n  ) || hasTypeLike(sourcePath) || hasTypeLike(resultPath) || typeof endPath == \"string\" && denylist.has(endPath));\n}, denylist = /* @__PURE__ */ new Set([\n  \"color\",\n  \"colour\",\n  \"currency\",\n  \"email\",\n  \"format\",\n  \"gid\",\n  \"hex\",\n  \"href\",\n  \"hsl\",\n  \"hsla\",\n  \"icon\",\n  \"id\",\n  \"index\",\n  \"key\",\n  \"language\",\n  \"layout\",\n  \"link\",\n  \"linkAction\",\n  \"locale\",\n  \"lqip\",\n  \"page\",\n  \"path\",\n  \"ref\",\n  \"rgb\",\n  \"rgba\",\n  \"route\",\n  \"secret\",\n  \"slug\",\n  \"status\",\n  \"tag\",\n  \"template\",\n  \"theme\",\n  \"type\",\n  \"textTheme\",\n  \"unit\",\n  \"url\",\n  \"username\",\n  \"variant\",\n  \"website\"\n]);\nfunction isValidDate(dateString) {\n  return /^\\d{4}-\\d{2}-\\d{2}/.test(dateString) ? !!Date.parse(dateString) : !1;\n}\nfunction isValidURL(url) {\n  try {\n    new URL(url, url.startsWith(\"/\") ? \"https://acme.com\" : void 0);\n  } catch {\n    return !1;\n  }\n  return !0;\n}\nfunction hasTypeLike(path) {\n  return path.some((segment) => typeof segment == \"string\" && segment.match(/type/i) !== null);\n}\nconst TRUNCATE_LENGTH = 20;\nfunction stegaEncodeSourceMap(result, resultSourceMap, config) {\n  const { filter, logger, enabled } = config;\n  if (!enabled) {\n    const msg = \"config.enabled must be true, don't call this function otherwise\";\n    throw logger?.error?.(`[@sanity/client]: ${msg}`, { result, resultSourceMap, config }), new TypeError(msg);\n  }\n  if (!resultSourceMap)\n    return logger?.error?.(\"[@sanity/client]: Missing Content Source Map from response body\", {\n      result,\n      resultSourceMap,\n      config\n    }), result;\n  if (!config.studioUrl) {\n    const msg = \"config.studioUrl must be defined\";\n    throw logger?.error?.(`[@sanity/client]: ${msg}`, { result, resultSourceMap, config }), new TypeError(msg);\n  }\n  const report = {\n    encoded: [],\n    skipped: []\n  }, resultWithStega = encodeIntoResult(\n    result,\n    resultSourceMap,\n    ({ sourcePath, sourceDocument, resultPath, value }) => {\n      if ((typeof filter == \"function\" ? filter({ sourcePath, resultPath, filterDefault, sourceDocument, value }) : filterDefault({ sourcePath, resultPath, value })) === !1)\n        return logger && report.skipped.push({\n          path: prettyPathForLogging(sourcePath),\n          value: `${value.slice(0, TRUNCATE_LENGTH)}${value.length > TRUNCATE_LENGTH ? \"...\" : \"\"}`,\n          length: value.length\n        }), value;\n      logger && report.encoded.push({\n        path: prettyPathForLogging(sourcePath),\n        value: `${value.slice(0, TRUNCATE_LENGTH)}${value.length > TRUNCATE_LENGTH ? \"...\" : \"\"}`,\n        length: value.length\n      });\n      const { baseUrl, workspace, tool } = resolveStudioBaseRoute(\n        typeof config.studioUrl == \"function\" ? config.studioUrl(sourceDocument) : config.studioUrl\n      );\n      if (!baseUrl) return value;\n      const { _id: id, _type: type, _projectId: projectId, _dataset: dataset } = sourceDocument;\n      return C(\n        value,\n        {\n          origin: \"sanity.io\",\n          href: createEditUrl({\n            baseUrl,\n            workspace,\n            tool,\n            id,\n            type,\n            path: sourcePath,\n            ...!config.omitCrossDatasetReferenceData && { dataset, projectId }\n          })\n        },\n        // We use custom logic to determine if we should skip encoding\n        !1\n      );\n    }\n  );\n  if (logger) {\n    const isSkipping = report.skipped.length, isEncoding = report.encoded.length;\n    if ((isSkipping || isEncoding) && ((logger?.groupCollapsed || logger.log)?.(\"[@sanity/client]: Encoding source map into result\"), logger.log?.(\n      `[@sanity/client]: Paths encoded: ${report.encoded.length}, skipped: ${report.skipped.length}`\n    )), report.encoded.length > 0 && (logger?.log?.(\"[@sanity/client]: Table of encoded paths\"), (logger?.table || logger.log)?.(report.encoded)), report.skipped.length > 0) {\n      const skipped = /* @__PURE__ */ new Set();\n      for (const { path } of report.skipped)\n        skipped.add(path.replace(reKeySegment, \"0\").replace(/\\[\\d+\\]/g, \"[]\"));\n      logger?.log?.(\"[@sanity/client]: List of skipped paths\", [...skipped.values()]);\n    }\n    (isSkipping || isEncoding) && logger?.groupEnd?.();\n  }\n  return resultWithStega;\n}\nfunction prettyPathForLogging(path) {\n  return toString(jsonPathToStudioPath(path));\n}\nvar stegaEncodeSourceMap$1 = /* @__PURE__ */ Object.freeze({\n  __proto__: null,\n  stegaEncodeSourceMap\n});\nexport {\n  encodeIntoResult,\n  stegaEncodeSourceMap,\n  stegaEncodeSourceMap$1\n};\n//# sourceMappingURL=stegaEncodeSourceMap.js.map\n"], "names": [], "mappings": ";;AACA,MAAM,eAAe;AACrB,SAAS,aAAa,SAAS;AAC7B,SAAO,OAAO,WAAW,WAAW,aAAa,KAAK,QAAQ,KAAI,CAAE,IAAI,OAAO,WAAW,YAAY,UAAU;AAClH;AACA,SAAS,SAAS,MAAM;AACtB,MAAI,CAAC,MAAM,QAAQ,IAAI;AACrB,UAAM,IAAI,MAAM,sBAAsB;AACxC,SAAO,KAAK,OAAO,CAAC,QAAQ,SAAS,MAAM;AACzC,UAAM,cAAc,OAAO;AAC3B,QAAI,gBAAgB;AAClB,aAAO,GAAG,MAAM,IAAI,OAAO;AAC7B,QAAI,gBAAgB;AAClB,aAAO,GAAG,MAAM,GAAG,MAAM,IAAI,KAAK,GAAG,GAAG,OAAO;AACjD,QAAI,aAAa,OAAO,KAAK,QAAQ;AACnC,aAAO,GAAG,MAAM,WAAW,QAAQ,IAAI;AACzC,QAAI,MAAM,QAAQ,OAAO,GAAG;AAC1B,YAAM,CAAC,MAAM,EAAE,IAAI;AACnB,aAAO,GAAG,MAAM,IAAI,IAAI,IAAI,EAAE;AAAA,IACpC;AACI,UAAM,IAAI,MAAM,8BAA8B,KAAK,UAAU,OAAO,CAAC,IAAI;AAAA,EAC1E,GAAE,EAAE;AACP;AACA,MAAM,SAAS;AAAA,EACb,MAAM;AAAA,EACN,MAAM;AAAA,EACN,MAAM;AAAA,EACN,KAAK;AAAA,EACL,KAAK;AAAA,EACL,MAAM;AACR,GAAG,WAAW;AAAA,EACZ,OAAO;AAAA,EACP,OAAO;AAAA;AAAA,EAEP,OAAO;AAAA,EACP,OAAO;AAAA,EACP,OAAO;AAAA,EACP,QAAQ;AACV;AACA,SAAS,SAAS,MAAM;AACtB,SAAO,IAAI,KAAK,IAAI,CAAC,YAAY,OAAO,WAAW,WAAW,KAAK,QAAQ,QAAQ,kBAAkB,CAAC,UAAU,OAAO,KAAK,CAAC,CAAC,OAAO,OAAO,WAAW,WAAW,IAAI,OAAO,MAAM,QAAQ,SAAS,KAAK,eAAe,QAAQ,KAAK,QAAQ,UAAU,CAAC,UAAU,OAAO,KAAK,CAAC,CAAC,QAAQ,IAAI,QAAQ,MAAM,GAAG,EAAE,KAAK,EAAE,CAAC;AACzT;AACA,SAAS,cAAc,MAAM;AAC3B,QAAM,SAAS,IAAI,UAAU;AAC7B,MAAI;AACJ,UAAQ,QAAQ,QAAQ,KAAK,IAAI,OAAO,QAAQ;AAC9C,QAAI,MAAM,CAAC,MAAM,QAAQ;AACvB,YAAM,MAAM,MAAM,CAAC,EAAE,QAAQ,qBAAqB,CAAC,MAAM,SAAS,CAAC,CAAC;AACpE,aAAO,KAAK,GAAG;AACf;AAAA,IACN;AACI,QAAI,MAAM,CAAC,MAAM,QAAQ;AACvB,aAAO,KAAK,SAAS,MAAM,CAAC,GAAG,EAAE,CAAC;AAClC;AAAA,IACN;AACI,QAAI,MAAM,CAAC,MAAM,QAAQ;AACvB,YAAM,OAAO,MAAM,CAAC,EAAE,QAAQ,YAAY,CAAC,MAAM,SAAS,CAAC,CAAC;AAC5D,aAAO,KAAK;AAAA,QACV;AAAA,QACA,QAAQ;AAAA,MAChB,CAAO;AACD;AAAA,IACN;AAAA,EACA;AACE,SAAO;AACT;AACA,SAAS,qBAAqB,MAAM;AAClC,SAAO,KAAK,IAAI,CAAC,YAAY;AAC3B,QAAI,OAAO,WAAW,YAAY,OAAO,WAAW;AAClD,aAAO;AACT,QAAI,QAAQ,SAAS;AACnB,aAAO,EAAE,MAAM,QAAQ,KAAM;AAC/B,QAAI,QAAQ,WAAW;AACrB,aAAO,QAAQ;AACjB,UAAM,IAAI,MAAM,mBAAmB,KAAK,UAAU,OAAO,CAAC,EAAE;AAAA,EAChE,CAAG;AACH;AACA,SAAS,sBAAsB,MAAM;AACnC,SAAO,KAAK,IAAI,CAAC,YAAY;AAC3B,QAAI,OAAO,WAAW,YAAY,OAAO,WAAW;AAClD,aAAO;AACT,QAAI,QAAQ,WAAW;AACrB,aAAO,QAAQ;AACjB,UAAM,IAAI,MAAM,mBAAmB,KAAK,UAAU,OAAO,CAAC,EAAE;AAAA,EAChE,CAAG;AACH;AACA,SAAS,eAAe,YAAY,KAAK;AACvC,MAAI,CAAC,KAAK;AACR;AACF,QAAM,oBAAoB,SAAS,sBAAsB,UAAU,CAAC;AACpE,MAAI,IAAI,SAAS,iBAAiB,MAAM;AACtC,WAAO;AAAA,MACL,SAAS,IAAI,SAAS,iBAAiB;AAAA,MACvC,aAAa;AAAA,MACb,YAAY;AAAA,IACb;AACH,QAAM,WAAW,OAAO,QAAQ,IAAI,QAAQ,EAAE,OAAO,CAAC,CAAC,GAAG,MAAM,kBAAkB,WAAW,GAAG,CAAC,EAAE,KAAK,CAAC,CAAC,IAAI,GAAG,CAAC,IAAI,MAAM,KAAK,SAAS,KAAK,MAAM;AACrJ,MAAI,SAAS,UAAU;AACrB;AACF,QAAM,CAAC,aAAa,OAAO,IAAI,SAAS,CAAC,GAAG,aAAa,kBAAkB,UAAU,YAAY,MAAM;AACvG,SAAO,EAAE,SAAS,aAAa,WAAY;AAC7C;AACA,SAAS,QAAQ,OAAO;AACtB,SAAO,UAAU,QAAQ,MAAM,QAAQ,KAAK;AAC9C;AACA,SAAS,SAAS,OAAO;AACvB,SAAO,OAAO,SAAS,YAAY,UAAU;AAC/C;AACA,SAAS,QAAQ,OAAO,WAAW,OAAO,CAAA,GAAI;AAC5C,MAAI,QAAQ,KAAK;AACf,WAAO,MAAM,IAAI,CAAC,GAAG,QAAQ;AAC3B,UAAI,SAAS,CAAC,GAAG;AACf,cAAM,OAAO,EAAE;AACf,YAAI,OAAO,QAAQ;AACjB,iBAAO,QAAQ,GAAG,WAAW,KAAK,OAAO,EAAE,MAAM,QAAQ,IAAG,CAAE,CAAC;AAAA,MACzE;AACM,aAAO,QAAQ,GAAG,WAAW,KAAK,OAAO,GAAG,CAAC;AAAA,IACnD,CAAK;AACH,MAAI,SAAS,KAAK,GAAG;AACnB,QAAI,MAAM,UAAU,WAAW,MAAM,UAAU,QAAQ;AACrD,YAAM,SAAS,EAAE,GAAG,MAAO;AAC3B,aAAO,MAAM,UAAU,UAAU,OAAO,WAAW,QAAQ,MAAM,UAAU,WAAW,KAAK,OAAO,UAAU,CAAC,IAAI,MAAM,UAAU,WAAW,OAAO,OAAO,QAAQ,MAAM,MAAM,WAAW,KAAK,OAAO,MAAM,CAAC,IAAI;AAAA,IACtN;AACI,WAAO,OAAO;AAAA,MACZ,OAAO,QAAQ,KAAK,EAAE,IAAI,CAAC,CAAC,GAAG,CAAC,MAAM,CAAC,GAAG,QAAQ,GAAG,WAAW,KAAK,OAAO,CAAC,CAAC,CAAC,CAAC;AAAA,IACjF;AAAA,EACL;AACE,SAAO,UAAU,OAAO,IAAI;AAC9B;AACA,SAAS,iBAAiB,QAAQ,KAAK,SAAS;AAC9C,SAAO,QAAQ,QAAQ,CAAC,OAAO,SAAS;AACtC,QAAI,OAAO,SAAS;AAClB,aAAO;AACT,UAAM,uBAAuB,eAAe,MAAM,GAAG;AACrD,QAAI,CAAC;AACH,aAAO;AACT,UAAM,EAAE,SAAS,YAAW,IAAK;AACjC,QAAI,QAAQ,SAAS,WAAW,QAAQ,OAAO,SAAS;AACtD,aAAO;AACT,UAAM,iBAAiB,IAAI,UAAU,QAAQ,OAAO,QAAQ,GAAG,aAAa,IAAI,MAAM,QAAQ,OAAO,IAAI,GAAG,oBAAoB,cAAc,WAAW,GAAG,qBAAqB,cAAc,UAAU,EAAE,OAAO,KAAK,MAAM,kBAAkB,MAAM,CAAC;AACtP,WAAO,QAAQ;AAAA,MACb,YAAY;AAAA,MACZ;AAAA,MACA,YAAY;AAAA,MACZ;AAAA,IACN,CAAK;AAAA,EACL,CAAG;AACH;AACA,MAAM,gBAAgB,UAAU,iBAAiB,YAAY,iBAAiB,KAAK,gBAAgB,GAAG,aAAa,GAAG,cAAc,IAAI,iBAAiB,GAAG,cAAc,GAAG,cAAc;AAC3L,SAAS,UAAU,IAAI;AACrB,SAAO,GAAG,WAAW,aAAa;AACpC;AACA,SAAS,YAAY,IAAI;AACvB,SAAO,GAAG,WAAW,cAAc;AACrC;AACA,SAAS,cAAc,IAAI;AACzB,SAAO,CAAC,UAAU,EAAE,KAAK,CAAC,YAAY,EAAE;AAC1C;AACA,SAAS,iBAAiB,IAAI;AAC5B,MAAI,CAAC,YAAY,EAAE,EAAG;AACtB,QAAM,CAAC,gBAAgB,WAAW,GAAG,YAAY,IAAI,GAAG,MAAM,cAAc;AAC5E,SAAO;AACT;AACA,SAAS,eAAe,IAAI;AAC1B,SAAO,YAAY,EAAE,IAAI,GAAG,MAAM,cAAc,EAAE,MAAM,CAAC,EAAE,KAAK,cAAc,IAAI,UAAU,EAAE,IAAI,GAAG,MAAM,cAAc,MAAM,IAAI;AACrI;AACA,SAAS,cAAc,SAAS;AAC9B,QAAM;AAAA,IACJ;AAAA,IACA,WAAW,aAAa;AAAA,IACxB,MAAM,QAAQ;AAAA,IACd,IAAI;AAAA,IACJ;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,EACJ,IAAM;AACJ,MAAI,CAAC;AACH,UAAM,IAAI,MAAM,qBAAqB;AACvC,MAAI,CAAC;AACH,UAAM,IAAI,MAAM,kBAAkB;AACpC,MAAI,CAAC;AACH,UAAM,IAAI,MAAM,gBAAgB;AAClC,MAAI,YAAY,OAAO,QAAQ,SAAS,GAAG;AACzC,UAAM,IAAI,MAAM,mCAAmC;AACrD,QAAM,YAAY,eAAe,YAAY,SAAS,YAAY,OAAO,UAAU,YAAY,SAAS,OAAO,KAAK,eAAe,GAAG,GAAG,kBAAkB,MAAM,QAAQ,IAAI,IAAI,SAAS,qBAAqB,IAAI,CAAC,IAAI,MAAM,eAAe,IAAI,gBAAgB;AAAA,IAC/P;AAAA,IACA;AAAA,IACA;AAAA,IACA,MAAM;AAAA,EACV,CAAG;AACD,MAAI,aAAa,aAAa,IAAI,aAAa,SAAS,GAAG,QAAQ,aAAa,IAAI,QAAQ,IAAI,GAAG,aAAa,aAAa,IAAI,aAAa,SAAS,GAAG,WAAW,aAAa,IAAI,WAAW,OAAO,GAAG,cAAc,GAAG;AAC1N,iBAAa,IAAI,eAAe,WAAW;AAAA,WACpC,YAAY,GAAG,GAAG;AACzB,UAAM,YAAY,iBAAiB,GAAG;AACtC,iBAAa,IAAI,eAAe,SAAS;AAAA,EAC7C;AACE,QAAM,WAAW,CAAC,YAAY,MAAM,KAAK,OAAO;AAChD,eAAa,SAAS,KAAK,SAAS;AACpC,QAAM,eAAe;AAAA,IACnB;AAAA,IACA,MAAM,EAAE;AAAA,IACR,QAAQ,IAAI;AAAA,IACZ,QAAQ,mBAAmB,eAAe,CAAC;AAAA,EAC5C;AACD,SAAO,QAAQ,aAAa,KAAK,QAAQ,IAAI,EAAE,GAAG,SAAS,KAAK,UAAU,QAAQ,GAAG,aAAa,KAAK,GAAG,CAAC,IAAI,YAAY,EAAE,GAAG,SAAS,KAAK,GAAG;AACnJ;AACA,SAAS,uBAAuB,WAAW;AACzC,MAAI,UAAU,OAAO,aAAa,WAAW,YAAY,UAAU;AACnE,SAAO,YAAY,QAAQ,UAAU,QAAQ,QAAQ,OAAO,EAAE,IAAI,OAAO,aAAa,WAAW,EAAE,QAAS,IAAG,EAAE,GAAG,WAAW,QAAS;AAC1I;AACA,MAAM,gBAAgB,CAAC,EAAE,YAAY,YAAY,MAAK,MAAO;AAC3D,MAAI,YAAY,KAAK,KAAK,WAAW,KAAK;AACxC,WAAO;AACT,QAAM,UAAU,WAAW,GAAG,EAAE;AAChC,SAAO,EAAE,WAAW,GAAG,EAAE,MAAM,UAAU,YAAY,aAAa,OAAO,WAAW,aAAa,QAAQ,WAAW,GAAG,KAAK,QAAQ,SAAS,IAAI,MAAM,WAAW;AAAA,IAChK,CAAC,SAAS,SAAS,UAAU,SAAS,cAAc,SAAS,eAAe,SAAS;AAAA,EACtF,KAAI,YAAY,UAAU,KAAK,YAAY,UAAU,KAAK,OAAO,WAAW,YAAY,SAAS,IAAI,OAAO;AAC/G,GAAG,WAA2B,oBAAI,IAAI;AAAA,EACpC;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AACF,CAAC;AACD,SAAS,YAAY,YAAY;AAC/B,SAAO,qBAAqB,KAAK,UAAU,IAAI,CAAC,CAAC,KAAK,MAAM,UAAU,IAAI;AAC5E;AACA,SAAS,WAAW,KAAK;AACvB,MAAI;AACF,QAAI,IAAI,KAAK,IAAI,WAAW,GAAG,IAAI,qBAAqB,MAAM;AAAA,EAClE,QAAU;AACN,WAAO;AAAA,EACX;AACE,SAAO;AACT;AACA,SAAS,YAAY,MAAM;AACzB,SAAO,KAAK,KAAK,CAAC,YAAY,OAAO,WAAW,YAAY,QAAQ,MAAM,OAAO,MAAM,IAAI;AAC7F;AACA,MAAM,kBAAkB;AACxB,SAAS,qBAAqB,QAAQ,iBAAiB,QAAQ;AAC7D,QAAM,EAAE,QAAQ,QAAQ,QAAS,IAAG;AACpC,MAAI,CAAC,SAAS;AACZ,UAAM,MAAM;AACZ,UAAM,QAAQ,QAAQ,qBAAqB,GAAG,IAAI,EAAE,QAAQ,iBAAiB,OAAM,CAAE,GAAG,IAAI,UAAU,GAAG;AAAA,EAC7G;AACE,MAAI,CAAC;AACH,WAAO,QAAQ,QAAQ,mEAAmE;AAAA,MACxF;AAAA,MACA;AAAA,MACA;AAAA,IACD,CAAA,GAAG;AACN,MAAI,CAAC,OAAO,WAAW;AACrB,UAAM,MAAM;AACZ,UAAM,QAAQ,QAAQ,qBAAqB,GAAG,IAAI,EAAE,QAAQ,iBAAiB,OAAM,CAAE,GAAG,IAAI,UAAU,GAAG;AAAA,EAC7G;AACE,QAAM,SAAS;AAAA,IACb,SAAS,CAAE;AAAA,IACX,SAAS,CAAA;AAAA,EACV,GAAE,kBAAkB;AAAA,IACnB;AAAA,IACA;AAAA,IACA,CAAC,EAAE,YAAY,gBAAgB,YAAY,MAAK,MAAO;AACrD,WAAK,OAAO,UAAU,aAAa,OAAO,EAAE,YAAY,YAAY,eAAe,gBAAgB,MAAK,CAAE,IAAI,cAAc,EAAE,YAAY,YAAY,MAAO,CAAA,OAAO;AAClK,eAAO,UAAU,OAAO,QAAQ,KAAK;AAAA,UACnC,MAAM,qBAAqB,UAAU;AAAA,UACrC,OAAO,GAAG,MAAM,MAAM,GAAG,eAAe,CAAC,GAAG,MAAM,SAAS,kBAAkB,QAAQ,EAAE;AAAA,UACvF,QAAQ,MAAM;AAAA,QACf,CAAA,GAAG;AACN,gBAAU,OAAO,QAAQ,KAAK;AAAA,QAC5B,MAAM,qBAAqB,UAAU;AAAA,QACrC,OAAO,GAAG,MAAM,MAAM,GAAG,eAAe,CAAC,GAAG,MAAM,SAAS,kBAAkB,QAAQ,EAAE;AAAA,QACvF,QAAQ,MAAM;AAAA,MACtB,CAAO;AACD,YAAM,EAAE,SAAS,WAAW,KAAM,IAAG;AAAA,QACnC,OAAO,OAAO,aAAa,aAAa,OAAO,UAAU,cAAc,IAAI,OAAO;AAAA,MACnF;AACD,UAAI,CAAC,QAAS,QAAO;AACrB,YAAM,EAAE,KAAK,IAAI,OAAO,MAAM,YAAY,WAAW,UAAU,QAAO,IAAK;AAC3E,aAAO;AAAA,QACL;AAAA,QACA;AAAA,UACE,QAAQ;AAAA,UACR,MAAM,cAAc;AAAA,YAClB;AAAA,YACA;AAAA,YACA;AAAA,YACA;AAAA,YACA;AAAA,YACA,MAAM;AAAA,YACN,GAAG,CAAC,OAAO,iCAAiC,EAAE,SAAS,UAAS;AAAA,UACjE,CAAA;AAAA,QACF;AAAA;AAAA,QAED;AAAA,MACD;AAAA,IACP;AAAA,EACG;AACD,MAAI,QAAQ;AACV,UAAM,aAAa,OAAO,QAAQ,QAAQ,aAAa,OAAO,QAAQ;AACtE,SAAK,cAAc,iBAAiB,QAAQ,kBAAkB,OAAO,OAAO,mDAAmD,GAAG,OAAO;AAAA,MACvI,oCAAoC,OAAO,QAAQ,MAAM,cAAc,OAAO,QAAQ,MAAM;AAAA,IAClG,IAAQ,OAAO,QAAQ,SAAS,MAAM,QAAQ,MAAM,0CAA0C,IAAI,QAAQ,SAAS,OAAO,OAAO,OAAO,OAAO,IAAI,OAAO,QAAQ,SAAS,GAAG;AACxK,YAAM,UAA0B,oBAAI,IAAK;AACzC,iBAAW,EAAE,UAAU,OAAO;AAC5B,gBAAQ,IAAI,KAAK,QAAQ,cAAc,GAAG,EAAE,QAAQ,YAAY,IAAI,CAAC;AACvE,cAAQ,MAAM,2CAA2C,CAAC,GAAG,QAAQ,OAAM,CAAE,CAAC;AAAA,IACpF;AACI,KAAC,cAAc,eAAe,QAAQ,WAAY;AAAA,EACtD;AACE,SAAO;AACT;AACA,SAAS,qBAAqB,MAAM;AAClC,SAAO,SAAS,qBAAqB,IAAI,CAAC;AAC5C;AACG,IAAC,yBAAyC,uBAAO,OAAO;AAAA,EACzD,WAAW;AAAA,EACX;AACF,CAAC;", "x_google_ignoreList": [0]}