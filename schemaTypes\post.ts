import {FileText, Lightbulb} from 'lucide-react'
import {defineField, defineType} from 'sanity'
import ProtipDecorator from '../decorators/PropTip'

export default defineType({
  name: 'post',
  title: 'Post',
  type: 'document',
  icon: FileText,
  groups: [
    {
      name: 'content',
      title: 'Content',
    },
    {
      name: 'seo',
      title: 'SEO',
    },
    {
      name: 'settings',
      title: 'Setting<PERSON>',
    },
  ],
  fields: [
    defineField({
      name: 'title',
      title: 'Title',
      type: 'string',
      group: 'content',
      validation: (Rule) => Rule.required(),
    }),
    defineField({
      name: 'slug',
      title: 'Slug',
      type: 'slug',
      group: 'settings',
      options: {
        source: 'title',
        maxLength: 96,
        isUnique: async (slug, context) => {
          const {document, getClient} = context
          const language = document?.language

          const documents = await getClient({apiVersion: '2023-01-01'}).fetch(
            `*[
              _type == "post" && 
              slug.current == $slug && 
              language == $language && 
              !(_id in [$draftId, $publishedId])
            ]`,
            {
              slug: slug,
              language: language,
              draftId: `drafts.${document?._id}`,
              publishedId: document?._id ?? '',
            },
          )

          if (documents.length > 1) {
            return false
          }

          return true
        },
      },
      validation: (Rule) => Rule.required(),
    }),
    defineField({
      name: 'excerpt',
      title: 'Excerpt',
      type: 'text',
      group: 'content',
    }),
    defineField({
      name: 'author',
      title: 'Author',
      type: 'reference',
      group: 'settings',
      to: {type: 'author'},
    }),
    defineField({
      name: 'image',
      title: 'Image',
      type: 'image',
      group: 'settings',
      options: {
        hotspot: true,
        aiAssist: {
          translateAction: true,
          imageDescriptionField: 'alt',
        },
      },
      fields: [
        {
          name: 'alt',
          type: 'string',
          title: 'Alternative Text',
        },
      ],
    }),
    defineField({
      name: 'categories',
      title: 'Categories',
      type: 'array',
      group: 'settings',
      of: [{type: 'reference', to: {type: 'category'}}],
    }),
    defineField({
      name: 'body',
      title: 'Body',
      type: 'array',
      of: [
        {
          type: 'block',
          marks: {
            decorators: [
              {title: 'Strong', value: 'strong'},
              {title: 'Emphasis', value: 'em'},
              {title: 'Code', value: 'code'},
              {title: 'Protip', value: 'protip', icon: Lightbulb, component: ProtipDecorator},
            ],
            annotations: [
              {
                name: 'internalLink',
                type: 'object',
                title: 'Internal link',
                fields: [
                  {
                    name: 'reference',
                    type: 'reference',
                    title: 'Reference',
                    to: [
                      // types you may want to link to
                      {type: 'post'},
                      {type: 'author'},
                      {type: 'category'},
                      {type: 'safariPackage'},
                    ],
                  },
                ],
              },
              {
                name: 'link',
                type: 'object',
                title: 'External link',
                fields: [
                  {
                    name: 'href',
                    type: 'url',
                    title: 'URL',
                  },
                  {
                    title: 'Open in new tab',
                    name: 'blank',
                    description: 'Read https://css-tricks.com/use-target_blank/',
                    type: 'boolean',
                  },
                ],
              },
            ],
          },
        },
        {
          type: 'image',
          fields: [
            defineField({
              type: 'text',
              name: 'alt',
              title: 'Alternative text',
              rows: 2,
            }),
          ],
          options: {
            hotspot: true,
            aiAssist: {
              translateAction: true,
              imageDescriptionField: 'alt',
            },
          },
        },
      ],
      group: 'content',
    }),
    defineField({
      name: 'meta_title',
      title: 'Meta Title',
      type: 'string',
      group: 'seo',
    }),
    defineField({
      name: 'meta_description',
      title: 'Meta Description',
      type: 'text',
      group: 'seo',
    }),
    defineField({
      name: 'noindex',
      title: 'No Index',
      type: 'boolean',
      initialValue: false,
      group: 'seo',
    }),
    defineField({
      name: 'ogImage',
      title: 'Open Graph Image - [1200x630]',
      type: 'image',
      group: 'seo',
    }),
    defineField({
      name: 'language',
      type: 'string',
      readOnly: true,
      hidden: true,
    }),
  ],
  preview: {
    select: {
      title: 'title',
      author: 'author.name',
      media: 'image',
    },
    prepare(selection) {
      const {author} = selection
      return {...selection, subtitle: author && `by ${author}`}
    },
  },
})
