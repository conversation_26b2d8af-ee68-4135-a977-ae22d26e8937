import accommodationType from './accommodationType'
import author from './author'
import bookingType from './bookingType'
import category from './category'
import cookiePolicy from './cookiePolicy'
import customerType from './customerType'
import destinationType from './destinationType'
import post from './post'
import privacyPolicy from './privacyPolicy'
import qualityAssurance from './qualityAssurance'
import safariPackageCategory from './safariPackageCategory'
import safariPackageType from './safariPackageType'
import termsAndConditions from './termsAndConditions'

export const schemaTypes = [
  safariPackageType,
  destinationType,
  safariPackageCategory,
  bookingType,
  customerType,
  post,
  category,
  author,
  accommodationType,
  cookiePolicy,
  privacyPolicy,
  qualityAssurance,
  termsAndConditions,
]
