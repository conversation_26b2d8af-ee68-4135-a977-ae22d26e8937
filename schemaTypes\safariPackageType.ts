import {PackageCheckIcon} from 'lucide-react'
import {defineField, defineType} from 'sanity'

export default defineType({
  name: 'safariPackage',
  title: 'Safari Package',
  icon: PackageCheckIcon,
  type: 'document',
  fields: [
    defineField({
      name: 'title',
      title: 'Package Title',
      type: 'string',
      validation: (Rule) => Rule.required().min(10).max(200),
    }),
    defineField({
      name: 'slug',
      title: 'Slug',
      type: 'slug',
      options: {
        source: 'title',
        maxLength: 96,
        isUnique: async (slug, context) => {
          const {document, getClient} = context
          const language = document?.language

          const documents = await getClient({apiVersion: '2023-01-01'}).fetch(
            `*[
              _type == "safariPackage" && 
              slug.current == $slug && 
              language == $language && 
              !(_id in [$draftId, $publishedId])
            ]`,
            {
              slug: slug,
              language: language,
              draftId: `drafts.${document?._id}`,
              publishedId: document?._id ?? '',
            },
          )

          if (documents.length > 1) {
            return false
          }

          return true
        },
      },
      validation: (Rule) => Rule.required(),
    }),
    defineField({
      name: 'recommended',
      title: 'Recommended Package',
      type: 'boolean',
      description: 'Highlight this package as recommended',
      initialValue: false,
    }),
    defineField({
      name: 'safariPackageCategory',
      title: 'Safari Package Categories',
      type: 'array',
      of: [
        {
          type: 'reference',
          to: [{type: 'safariPackageCategory'}],
          weak: true,
          options: {
            filter: ({document}) => {
              return {
                filter: 'language == $language',
                params: {language: document.language},
              }
            },
          },
        },
      ],
      description: 'Select the categories that best describe this package',
    }),
    defineField({
      name: 'mainImage',
      title: 'Main Image',
      type: 'image',
      fields: [
        defineField({
          type: 'text',
          name: 'alt',
          title: 'Alternative text',
          rows: 2,
        }),
      ],
      options: {
        hotspot: true,
        aiAssist: {
          translateAction: true,
          imageDescriptionField: 'alt',
        },
      },
      validation: (Rule) => Rule.required(),
    }),
    defineField({
      name: 'galleryImages',
      title: 'Gallery Images',
      type: 'array',
      of: [
        {
          type: 'image',
          fields: [
            defineField({
              type: 'text',
              name: 'alt',
              title: 'Alternative text',
              rows: 2,
            }),
          ],
          options: {
            hotspot: true,
            aiAssist: {
              translateAction: true,
              imageDescriptionField: 'alt',
            },
          },
        },
      ],
      options: {
        layout: 'grid',
      },
    }),
    defineField({
      name: 'shortDescription',
      title: 'Short Package Description',
      type: 'text',
      validation: (Rule) => Rule.required(),
    }),
    defineField({
      name: 'description',
      title: 'Package Description',
      type: 'array',
      of: [
        {
          type: 'block',
          marks: {
            annotations: [
              {
                name: 'internalLink',
                type: 'object',
                title: 'Internal link',
                fields: [
                  {
                    name: 'reference',
                    type: 'reference',
                    title: 'Reference',
                    to: [
                      // types you may want to link to
                      {type: 'post'},
                      {type: 'author'},
                      {type: 'category'},
                      {type: 'safariPackage'},
                    ],
                  },
                ],
              },
              {
                name: 'link',
                type: 'object',
                title: 'External link',
                fields: [
                  {
                    name: 'href',
                    type: 'url',
                    title: 'URL',
                  },
                  {
                    title: 'Open in new tab',
                    name: 'blank',
                    description: 'Read https://css-tricks.com/use-target_blank/',
                    type: 'boolean',
                  },
                ],
              },
            ],
          },
        },
      ],
    }),
    defineField({
      name: 'duration',
      title: 'Duration',
      type: 'number',
      description: 'Duration in days',
      validation: (Rule) => Rule.required().min(1).max(30),
    }),
    defineField({
      name: 'price',
      title: 'Price',
      type: 'number',
      validation: (Rule) => Rule.required().min(0),
    }),
    defineField({
      name: 'capacity',
      title: 'Group Capacity',
      type: 'number',
      validation: (Rule) => Rule.required().min(1).max(10),
    }),
    defineField({
      name: 'itinerary',
      title: 'Itinerary',
      type: 'array',
      of: [
        {
          type: 'object',
          preview: {
            select: {
              title: 'title',
              day: 'day',
            },
            prepare(selection) {
              const {title, day} = selection
              return {
                title: `${day} - ${title}`,
              }
            },
          },
          fields: [
            defineField({
              name: 'day',
              title: 'Day Number',
              type: 'number',
            }),
            defineField({
              name: 'title',
              title: 'Day Title',
              type: 'string',
            }),
            defineField({
              name: 'description',
              title: 'Day Description',
              type: 'array',
              of: [
                {
                  type: 'block',
                  marks: {
                    annotations: [
                      {
                        name: 'internalLink',
                        type: 'object',
                        title: 'Internal link',
                        fields: [
                          {
                            name: 'reference',
                            type: 'reference',
                            title: 'Reference',
                            to: [
                              // types you may want to link to
                              {type: 'post'},
                              {type: 'author'},
                              {type: 'category'},
                              {type: 'safariPackage'},
                            ],
                          },
                        ],
                      },
                      {
                        name: 'link',
                        type: 'object',
                        title: 'External link',
                        fields: [
                          {
                            name: 'href',
                            type: 'url',
                            title: 'URL',
                          },
                          {
                            title: 'Open in new tab',
                            name: 'blank',
                            description: 'Read https://css-tricks.com/use-target_blank/',
                            type: 'boolean',
                          },
                        ],
                      },
                    ],
                  },
                },
              ],
            }),
          ],
        },
      ],
    }),
    defineField({
      name: 'destinations',
      title: 'Destinations',
      type: 'array',
      of: [
        {
          type: 'reference',
          to: [{type: 'destination'}],
          weak: true,
          options: {
            filter: ({document}) => {
              return {
                filter: 'language == $language',
                params: {language: document.language},
              }
            },
          },
        },
      ],
    }),
    defineField({
      name: 'includedServices',
      title: 'Included Services',
      type: 'array',
      of: [{type: 'string'}],
    }),
    defineField({
      name: 'excludedServices',
      title: 'Additional Tips',
      type: 'array',
      of: [{type: 'string'}],
    }),
    defineField({
      // should match 'languageField' plugin configuration setting, if customized
      name: 'language',
      type: 'string',
      readOnly: true,
      hidden: true,
    }),
  ],
  preview: {
    select: {
      title: 'title',
      media: 'mainImage',
      recommended: 'recommended',
      language: 'language',
    },
    prepare(selection) {
      let status = selection.recommended ? 'Recommended' : 'Not Recommended'
      return {
        title: selection.title,
        subtitle: `${status} | ${selection.language}`,
        media: selection.media,
      }
    },
  },
})
