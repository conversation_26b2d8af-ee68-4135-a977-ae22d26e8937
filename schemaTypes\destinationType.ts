import { LocateIcon } from 'lucide-react'
import { defineField, defineType } from 'sanity'

export default defineType({
  name: 'destination',
  title: 'Safari Destination',
  icon: LocateIcon,
  type: 'document',
  fields: [
    defineField({
      name: 'name',
      title: 'Destination Name',
      type: 'string',
      validation: (Rule) => Rule.required().min(2).max(100),
    }),
    defineField({
      name: 'slug',
      title: 'Slug',
      type: 'slug',
      options: {
        source: 'name',
        maxLength: 96,
        isUnique: async (slug, context) => {
          const {document, getClient} = context
          const language = document?.language

          const documents = await getClient({apiVersion: '2023-01-01'}).fetch(
            `*[
              _type == "destination" && 
              slug.current == $slug && 
              language == $language && 
              !(_id in [$draftId, $publishedId])
            ]`,
            {
              slug: slug,
              language: language,
              draftId: `drafts.${document?._id}`,
              publishedId: document?._id ?? '',
            },
          )

          if (documents.length > 1) {
            return false
          }

          return true
        },
      },
      validation: (Rule) => Rule.required(),
    }),
    defineField({
      name: 'mainImage',
      title: 'Main Image',
      type: 'image',
      options: {
        hotspot: true,
      },
      validation: (Rule) => Rule.required(),
    }),
    defineField({
      name: 'galleryImages',
      title: 'Gallery Images',
      type: 'array',
      of: [{type: 'image'}],
      options: {
        layout: 'grid',
      },
    }),
    defineField({
      name: 'description',
      title: 'Destination Description',
      type: 'text',
      validation: (Rule) => Rule.required().min(50).max(10000),
    }),
    defineField({
      name: 'metaTitle',
      title: 'Meta Title',
      type: 'string',
    }),
    defineField({
      name: 'metaDescription',
      title: 'Meta Description',
      type: 'text',
    }),
    defineField({
      name: 'content',
      title: 'Content',
      description: 'Additional content about the destination',
      type: 'array',
      of: [
        {
          type: 'block',
          styles: [
            {title: 'Normal', value: 'normal'},
            {title: 'H3', value: 'h3'},
            {title: 'H4', value: 'h4'},
          ],
          lists: [
            {title: 'Bullet', value: 'bullet'},
            {title: 'Numbered', value: 'number'},
          ],
          marks: {
            decorators: [
              {title: 'Strong', value: 'strong'},
              {title: 'Emphasis', value: 'em'},
            ],
            annotations: [
              {
                name: 'link',
                type: 'object',
                title: 'URL',
                fields: [
                  {
                    title: 'URL',
                    name: 'href',
                    type: 'url',
                    validation: (Rule) =>
                      Rule.uri({
                        scheme: ['http', 'https', 'mailto', 'tel'],
                      }),
                  },
                ],
              },
            ],
          },
        },
        {
          type: 'image',
          options: {
            hotspot: true,
          },
        },
      ],
    }),
    defineField({
      name: 'faqs',
      title: 'FAQs',
      description: 'Frequently Asked Questions about this destination',
      type: 'array',
      of: [
        {
          type: 'object',
          name: 'faq',
          fields: [
            {
              name: 'question',
              title: 'Question',
              type: 'string',
              validation: (Rule) => Rule.required(),
            },
            {
              name: 'answer',
              title: 'Answer',
              type: 'array',
              of: [
                {
                  type: 'block',
                  styles: [
                    {title: 'Normal', value: 'normal'},
                    {title: 'H4', value: 'h4'},
                  ],
                  lists: [
                    {title: 'Bullet', value: 'bullet'},
                    {title: 'Numbered', value: 'number'},
                  ],
                  marks: {
                    decorators: [
                      {title: 'Strong', value: 'strong'},
                      {title: 'Emphasis', value: 'em'},
                    ],
                    annotations: [
                      {
                        name: 'link',
                        type: 'object',
                        title: 'URL',
                        fields: [
                          {
                            title: 'URL',
                            name: 'href',
                            type: 'url',
                            validation: (Rule) =>
                              Rule.uri({
                                scheme: ['http', 'https', 'mailto', 'tel'],
                              }),
                          },
                        ],
                      },
                    ],
                  },
                },
              ],
              validation: (Rule) => Rule.required(),
            },
          ],
          preview: {
            select: {
              title: 'question',
              subtitle: 'answer',
            },
            prepare(selection) {
              // Convert the rich text answer array to plain text for preview
              const answerText = selection.subtitle
                ?.map((block: any) =>
                  block._type === 'block'
                    ? block.children?.map((child: any) => child.text).join('')
                    : ''
                )
                .join(' ')
                .trim();

              return {
                title: selection.title,
                subtitle: answerText ? `${answerText.substring(0, 100)}${answerText.length > 100 ? '...' : ''}` : 'No answer provided',
              }
            },
          },
        },
      ],
    }),
    defineField({
      name: 'language',
      type: 'string',
      readOnly: true,
      hidden: true,
    }),
  ],
  preview: {
    select: {
      title: 'name',
      media: 'mainImage',
      slug: 'slug.current',
    },
    prepare(selection) {
      return {
        title: selection.title,
        subtitle: selection.slug,
        media: selection.media,
      }
    },
  },
})
