import type {StructureResolver} from 'sanity/structure'
import {languages} from '../languages'
import {BedIcon, LibraryBigIcon, PackageCheckIcon, SailboatIcon, ShipWheelIcon} from 'lucide-react'

export const structure: StructureResolver = (S) =>
  S.list()
    .title('Content')
    .items([
      // list all document types except 'safariPackage', 'safariPackageCategory', 'post', 'destination', and 'accommodation'
      ...S.documentTypeListItems().filter(
        (item) =>
          item.getId() !== 'safariPackage' &&
          item.getId() !== 'safariPackageCategory' &&
          item.getId() !== 'post' &&
          item.getId() !== 'destination' &&
          item.getId() !== 'accommodation',
      ),
      S.divider(),
      S.listItem()
        .title('Safari Packages')
        .icon(PackageCheckIcon)
        .child(
          S.list()
            .title('Languages')
            .items(
              languages.map((lang) =>
                S.listItem()
                  .title(lang.title)
                  .child(
                    S.documentList()
                      .title(`Safari Packages (${lang.title})`)
                      .filter('_type == "safariPackage" && language == $lang')
                      .params({lang: lang.id}),
                  ),
              ),
            ),
        ),
      S.divider(),
      S.listItem()
        .title('Safari Package Categories')
        .icon(ShipWheelIcon)
        .child(
          S.list()
            .title('Languages')
            .items(
              languages.map((lang) =>
                S.listItem()
                  .title(lang.title)
                  .child(
                    S.documentList()
                      .title(`Safari Package Categories (${lang.title})`)
                      .filter('_type == "safariPackageCategory" && language == $lang')
                      .params({lang: lang.id}),
                  ),
              ),
            ),
        ),
      S.divider(),
      S.listItem()
        .title('Blog Posts')
        .icon(LibraryBigIcon)
        .child(
          S.list()
            .title('Languages')
            .items(
              languages.map((lang) =>
                S.listItem()
                  .title(lang.title)
                  .child(
                    S.documentList()
                      .title(`Posts (${lang.title})`)
                      .filter('_type == "post" && language == $lang')
                      .params({lang: lang.id}),
                  ),
              ),
            ),
        ),
      S.divider(),
      S.listItem()
        .title('Destinations')
        .icon(SailboatIcon)
        .child(
          S.list()
            .title('Languages')
            .items(
              languages.map((lang) =>
                S.listItem()
                  .title(lang.title)
                  .child(
                    S.documentList()
                      .title(`Destinations (${lang.title})`)
                      .filter('_type == "destination" && language == $lang')
                      .params({lang: lang.id}),
                  ),
              ),
            ),
        ),
      S.divider(),
      S.listItem()
        .title('Accommodations')
        .icon(BedIcon)
        .child(
          S.list()
            .title('Languages')
            .items(
              languages.map((lang) =>
                S.listItem()
                  .title(lang.title)
                  .child(
                    S.documentList()
                      .title(`Accommodations (${lang.title})`)
                      .filter('_type == "accommodation" && language == $lang')
                      .params({lang: lang.id}),
                  ),
              ),
            ),
        ),
    ])
