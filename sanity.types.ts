/**
 * ---------------------------------------------------------------------------------
 * This file has been generated by Sanity TypeGen.
 * Command: `sanity typegen generate`
 *
 * Any modifications made directly to this file will be overwritten the next time
 * the TypeScript definitions are generated. Please make changes to the Sanity
 * schema definitions and/or GROQ queries if you need to update these types.
 *
 * For more information on how to use Sanity TypeGen, visit the official documentation:
 * https://www.sanity.io/docs/sanity-typegen
 * ---------------------------------------------------------------------------------
 */

// Source: schema.json
export type SanityImagePaletteSwatch = {
  _type: 'sanity.imagePaletteSwatch'
  background?: string
  foreground?: string
  population?: number
  title?: string
}

export type SanityImagePalette = {
  _type: 'sanity.imagePalette'
  darkMuted?: SanityImagePaletteSwatch
  lightVibrant?: SanityImagePaletteSwatch
  darkVibrant?: SanityImagePaletteSwatch
  vibrant?: SanityImagePaletteSwatch
  dominant?: SanityImagePaletteSwatch
  lightMuted?: SanityImagePaletteSwatch
  muted?: SanityImagePaletteSwatch
}

export type SanityImageDimensions = {
  _type: 'sanity.imageDimensions'
  height?: number
  width?: number
  aspectRatio?: number
}

export type SanityFileAsset = {
  _id: string
  _type: 'sanity.fileAsset'
  _createdAt: string
  _updatedAt: string
  _rev: string
  originalFilename?: string
  label?: string
  title?: string
  description?: string
  altText?: string
  sha1hash?: string
  extension?: string
  mimeType?: string
  size?: number
  assetId?: string
  uploadId?: string
  path?: string
  url?: string
  source?: SanityAssetSourceData
}

export type SpecialOffer = {
  _id: string
  _type: 'specialOffer'
  _createdAt: string
  _updatedAt: string
  _rev: string
  title?: string
  description?: string
  discountPercentage?: number
  validFrom?: string
  validTo?: string
  applicablePackages?: Array<{
    _ref: string
    _type: 'reference'
    _weak?: boolean
    _key: string
    [internalGroqTypeReferenceTo]?: 'safariPackage'
  }>
}

export type Review = {
  _id: string
  _type: 'review'
  _createdAt: string
  _updatedAt: string
  _rev: string
  title?: string
  slug?: Slug
  customer?: {
    name?: string
    email?: string
    country?: string
  }
  safari?: {
    _ref: string
    _type: 'reference'
    _weak?: boolean
    [internalGroqTypeReferenceTo]?: 'safariPackage'
  }
  ratings?: {
    overallExperience?: number
    guideQuality?: number
    wildlife?: number
    accommodation?: number
    value?: number
  }
  reviewText?: string
  travelDate?: string
  verificationStatus?: {
    isVerified?: boolean
    verificationMethod?: 'booking' | 'email' | 'manual'
  }
  images?: Array<{
    asset?: {
      _ref: string
      _type: 'reference'
      _weak?: boolean
      [internalGroqTypeReferenceTo]?: 'sanity.imageAsset'
    }
    hotspot?: SanityImageHotspot
    crop?: SanityImageCrop
    _type: 'image'
    _key: string
  }>
  helpfulnessScore?: {
    upvotes?: number
    downvotes?: number
  }
  tags?: Array<string>
}

export type Guide = {
  _id: string
  _type: 'guide'
  _createdAt: string
  _updatedAt: string
  _rev: string
  name?: string
  slug?: Slug
  profileImage?: {
    asset?: {
      _ref: string
      _type: 'reference'
      _weak?: boolean
      [internalGroqTypeReferenceTo]?: 'sanity.imageAsset'
    }
    hotspot?: SanityImageHotspot
    crop?: SanityImageCrop
    _type: 'image'
  }
  personalInfo?: {
    dateOfBirth?: string
    nationality?: string
  }
  professionalDetails?: {
    yearsOfExperience?: number
    specializations?: Array<string>
    certifications?: Array<{
      certificationName?: string
      issuingOrganization?: string
      yearIssued?: number
      _key: string
    }>
  }
  languages?: Array<{
    language?: string
    proficiencyLevel?: 'native' | 'fluent' | 'intermediate' | 'basic'
    _key: string
  }>
  biography?: string
  contactInformation?: {
    email?: string
    phoneNumber?: string
  }
  associatedPackages?: Array<{
    _ref: string
    _type: 'reference'
    _weak?: boolean
    _key: string
    [internalGroqTypeReferenceTo]?: 'safariPackage'
  }>
  socialMediaLinks?: {
    linkedIn?: string
    personalWebsite?: string
  }
  testimonials?: Array<{
    testimonial?: string
    clientName?: string
    dateOfTrip?: string
    _key: string
  }>
}

export type Booking = {
  _id: string
  _type: 'booking'
  _createdAt: string
  _updatedAt: string
  _rev: string
  bookingId?: string
  customer?: {
    _ref: string
    _type: 'reference'
    _weak?: boolean
    [internalGroqTypeReferenceTo]?: 'customer'
  }
  package?: {
    _ref: string
    _type: 'reference'
    _weak?: boolean
    [internalGroqTypeReferenceTo]?: 'safariPackage'
  }
  travelers?: number
  travelDates?: {
    startDate?: string
    endDate?: string
  }
  totalPrice?: number
  status?: 'confirmed' | 'pending' | 'cancelled'
}

export type Customer = {
  _id: string
  _type: 'customer'
  _createdAt: string
  _updatedAt: string
  _rev: string
  name?: string
  email?: string
  phoneNumber?: string
  country?: string
  preferences?: Array<string>
  bookingHistory?: Array<{
    _ref: string
    _type: 'reference'
    _weak?: boolean
    _key: string
    [internalGroqTypeReferenceTo]?: 'booking'
  }>
}

export type Accommodation = {
  _id: string
  _type: 'accommodation'
  _createdAt: string
  _updatedAt: string
  _rev: string
  name?: string
  slug?: Slug
  type?: 'luxury-lodge' | 'eco-camp' | 'tented-camp' | 'safari-hotel' | 'mobile-camp' | 'homestay'
  mainImage?: {
    asset?: {
      _ref: string
      _type: 'reference'
      _weak?: boolean
      [internalGroqTypeReferenceTo]?: 'sanity.imageAsset'
    }
    hotspot?: SanityImageHotspot
    crop?: SanityImageCrop
    _type: 'image'
  }
  galleryImages?: Array<{
    asset?: {
      _ref: string
      _type: 'reference'
      _weak?: boolean
      [internalGroqTypeReferenceTo]?: 'sanity.imageAsset'
    }
    hotspot?: SanityImageHotspot
    crop?: SanityImageCrop
    _type: 'image'
    _key: string
  }>
  description?: string
  location?: {
    destination?: {
      _ref: string
      _type: 'reference'
      _weak?: boolean
      [internalGroqTypeReferenceTo]?: 'destination'
    }
    coordinates?: Geopoint
    proximityToWildlife?: 'within' | 'bordering' | 'near'
  }
  roomTypes?: Array<{
    name?: string
    capacity?: number
    pricePerNight?: number
    _key: string
  }>
  amenities?: {
    commonAmenities?: Array<string>
    safariFacilities?: Array<string>
  }
  sustainabilityRating?: {
    ecoCertification?: 'none' | 'basic' | 'certified' | 'advanced'
    sustainabilityInitiatives?: Array<string>
  }
  recommendedPackages?: Array<{
    _ref: string
    _type: 'reference'
    _weak?: boolean
    _key: string
    [internalGroqTypeReferenceTo]?: 'safariPackage'
  }>
}

export type SafariPackage = {
  _id: string
  _type: 'safariPackage'
  _createdAt: string
  _updatedAt: string
  _rev: string
  title?: string
  slug?: Slug
  mainImage?: {
    asset?: {
      _ref: string
      _type: 'reference'
      _weak?: boolean
      [internalGroqTypeReferenceTo]?: 'sanity.imageAsset'
    }
    hotspot?: SanityImageHotspot
    crop?: SanityImageCrop
    _type: 'image'
  }
  galleryImages?: Array<{
    asset?: {
      _ref: string
      _type: 'reference'
      _weak?: boolean
      [internalGroqTypeReferenceTo]?: 'sanity.imageAsset'
    }
    hotspot?: SanityImageHotspot
    crop?: SanityImageCrop
    _type: 'image'
    _key: string
  }>
  shortDescription?: string
  description?: string
  duration?: number
  difficultyLevel?: 'easy' | 'moderate' | 'challenging'
  price?: number
  capacity?: number
  includedServices?: Array<string>
  excludedServices?: Array<string>
  itinerary?: Array<{
    day?: number
    description?: string
    _key: string
  }>
  destinations?: Array<{
    _ref: string
    _type: 'reference'
    _weak?: boolean
    _key: string
    [internalGroqTypeReferenceTo]?: 'destination'
  }>
  recommendedSeason?: 'dry' | 'wet' | 'year-round'
  minimumAge?: number
  highlights?: Array<string>
  specialRequirements?: Array<string>
  environmentalImpact?: {
    carbonOffset?: boolean
    conservationContribution?: number
    sustainabilityRating?: 'low' | 'moderate' | 'high' | 'zero'
  }
  photographyOpportunities?: {
    wildlife?: boolean
    landscapeShooting?: boolean
    recommendedEquipment?: Array<string>
  }
  healthAndSafety?: {
    medicalConsiderations?: string
    emergencySupport?: {
      guidedSupport?: boolean
      evacuationCoverage?: boolean
    }
  }
  additionalGuides?: Array<{
    _ref: string
    _type: 'reference'
    _weak?: boolean
    _key: string
    [internalGroqTypeReferenceTo]?: 'guide'
  }>
  specialOffers?: {
    discountEligibility?: Array<string>
    earlyBookingDiscount?: number
  }
}

export type Destination = {
  _id: string
  _type: 'destination'
  _createdAt: string
  _updatedAt: string
  _rev: string
  name?: string
  slug?: Slug
  mainImage?: {
    asset?: {
      _ref: string
      _type: 'reference'
      _weak?: boolean
      [internalGroqTypeReferenceTo]?: 'sanity.imageAsset'
    }
    hotspot?: SanityImageHotspot
    crop?: SanityImageCrop
    _type: 'image'
  }
  galleryImages?: Array<{
    asset?: {
      _ref: string
      _type: 'reference'
      _weak?: boolean
      [internalGroqTypeReferenceTo]?: 'sanity.imageAsset'
    }
    hotspot?: SanityImageHotspot
    crop?: SanityImageCrop
    _type: 'image'
    _key: string
  }>
  description?: string
  location?: {
    country?: string
    region?: string
    coordinates?: Geopoint
  }
  climate?: {
    averageTemperature?: {
      min?: number
      max?: number
    }
    rainfallSeason?: 'dry' | 'wet' | 'year-round'
  }
  wildlifeHighlights?: Array<string>
  ecosystemTypes?: Array<string>
  travelConsiderations?: {
    bestTimeToVisit?: Array<string>
    travelRestrictions?: string
    requiredVaccinations?: Array<string>
  }
  recommendedPackages?: Array<{
    _ref: string
    _type: 'reference'
    _weak?: boolean
    _key: string
    [internalGroqTypeReferenceTo]?: 'safariPackage'
  }>
  conservationStatus?: {
    protectedArea?: boolean
    conservationEfforts?: string
  }
}

export type Geopoint = {
  _type: 'geopoint'
  lat?: number
  lng?: number
  alt?: number
}

export type SanityImageCrop = {
  _type: 'sanity.imageCrop'
  top?: number
  bottom?: number
  left?: number
  right?: number
}

export type SanityImageHotspot = {
  _type: 'sanity.imageHotspot'
  x?: number
  y?: number
  height?: number
  width?: number
}

export type SanityImageAsset = {
  _id: string
  _type: 'sanity.imageAsset'
  _createdAt: string
  _updatedAt: string
  _rev: string
  originalFilename?: string
  label?: string
  title?: string
  description?: string
  altText?: string
  sha1hash?: string
  extension?: string
  mimeType?: string
  size?: number
  assetId?: string
  uploadId?: string
  path?: string
  url?: string
  metadata?: SanityImageMetadata
  source?: SanityAssetSourceData
}

export type SanityAssetSourceData = {
  _type: 'sanity.assetSourceData'
  name?: string
  id?: string
  url?: string
}

export type SanityImageMetadata = {
  _type: 'sanity.imageMetadata'
  location?: Geopoint
  dimensions?: SanityImageDimensions
  palette?: SanityImagePalette
  lqip?: string
  blurHash?: string
  hasAlpha?: boolean
  isOpaque?: boolean
}

export type MediaTag = {
  _id: string
  _type: 'media.tag'
  _createdAt: string
  _updatedAt: string
  _rev: string
  name?: Slug
}

export type Slug = {
  _type: 'slug'
  current?: string
  source?: string
}

export type AllSanitySchemaTypes =
  | SanityImagePaletteSwatch
  | SanityImagePalette
  | SanityImageDimensions
  | SanityFileAsset
  | SpecialOffer
  | Review
  | Guide
  | Booking
  | Customer
  | Accommodation
  | SafariPackage
  | Destination
  | Geopoint
  | SanityImageCrop
  | SanityImageHotspot
  | SanityImageAsset
  | SanityAssetSourceData
  | SanityImageMetadata
  | MediaTag
  | Slug
export declare const internalGroqTypeReferenceTo: unique symbol
