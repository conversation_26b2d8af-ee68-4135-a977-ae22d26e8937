[{"name": "sanity.imagePaletteSwatch", "type": "type", "value": {"type": "object", "attributes": {"_type": {"type": "objectAttribute", "value": {"type": "string", "value": "sanity.imagePaletteSwatch"}}, "background": {"type": "objectAttribute", "value": {"type": "string"}, "optional": true}, "foreground": {"type": "objectAttribute", "value": {"type": "string"}, "optional": true}, "population": {"type": "objectAttribute", "value": {"type": "number"}, "optional": true}, "title": {"type": "objectAttribute", "value": {"type": "string"}, "optional": true}}}}, {"name": "sanity.imagePalette", "type": "type", "value": {"type": "object", "attributes": {"_type": {"type": "objectAttribute", "value": {"type": "string", "value": "sanity.imagePalette"}}, "darkMuted": {"type": "objectAttribute", "value": {"type": "inline", "name": "sanity.imagePaletteSwatch"}, "optional": true}, "lightVibrant": {"type": "objectAttribute", "value": {"type": "inline", "name": "sanity.imagePaletteSwatch"}, "optional": true}, "darkVibrant": {"type": "objectAttribute", "value": {"type": "inline", "name": "sanity.imagePaletteSwatch"}, "optional": true}, "vibrant": {"type": "objectAttribute", "value": {"type": "inline", "name": "sanity.imagePaletteSwatch"}, "optional": true}, "dominant": {"type": "objectAttribute", "value": {"type": "inline", "name": "sanity.imagePaletteSwatch"}, "optional": true}, "lightMuted": {"type": "objectAttribute", "value": {"type": "inline", "name": "sanity.imagePaletteSwatch"}, "optional": true}, "muted": {"type": "objectAttribute", "value": {"type": "inline", "name": "sanity.imagePaletteSwatch"}, "optional": true}}}}, {"name": "sanity.imageDimensions", "type": "type", "value": {"type": "object", "attributes": {"_type": {"type": "objectAttribute", "value": {"type": "string", "value": "sanity.imageDimensions"}}, "height": {"type": "objectAttribute", "value": {"type": "number"}, "optional": true}, "width": {"type": "objectAttribute", "value": {"type": "number"}, "optional": true}, "aspectRatio": {"type": "objectAttribute", "value": {"type": "number"}, "optional": true}}}}, {"name": "sanity.fileAsset", "type": "document", "attributes": {"_id": {"type": "objectAttribute", "value": {"type": "string"}}, "_type": {"type": "objectAttribute", "value": {"type": "string", "value": "sanity.fileAsset"}}, "_createdAt": {"type": "objectAttribute", "value": {"type": "string"}}, "_updatedAt": {"type": "objectAttribute", "value": {"type": "string"}}, "_rev": {"type": "objectAttribute", "value": {"type": "string"}}, "originalFilename": {"type": "objectAttribute", "value": {"type": "string"}, "optional": true}, "label": {"type": "objectAttribute", "value": {"type": "string"}, "optional": true}, "title": {"type": "objectAttribute", "value": {"type": "string"}, "optional": true}, "description": {"type": "objectAttribute", "value": {"type": "string"}, "optional": true}, "altText": {"type": "objectAttribute", "value": {"type": "string"}, "optional": true}, "sha1hash": {"type": "objectAttribute", "value": {"type": "string"}, "optional": true}, "extension": {"type": "objectAttribute", "value": {"type": "string"}, "optional": true}, "mimeType": {"type": "objectAttribute", "value": {"type": "string"}, "optional": true}, "size": {"type": "objectAttribute", "value": {"type": "number"}, "optional": true}, "assetId": {"type": "objectAttribute", "value": {"type": "string"}, "optional": true}, "uploadId": {"type": "objectAttribute", "value": {"type": "string"}, "optional": true}, "path": {"type": "objectAttribute", "value": {"type": "string"}, "optional": true}, "url": {"type": "objectAttribute", "value": {"type": "string"}, "optional": true}, "source": {"type": "objectAttribute", "value": {"type": "inline", "name": "sanity.assetSourceData"}, "optional": true}}}, {"name": "specialOffer", "type": "document", "attributes": {"_id": {"type": "objectAttribute", "value": {"type": "string"}}, "_type": {"type": "objectAttribute", "value": {"type": "string", "value": "specialOffer"}}, "_createdAt": {"type": "objectAttribute", "value": {"type": "string"}}, "_updatedAt": {"type": "objectAttribute", "value": {"type": "string"}}, "_rev": {"type": "objectAttribute", "value": {"type": "string"}}, "title": {"type": "objectAttribute", "value": {"type": "string"}, "optional": true}, "description": {"type": "objectAttribute", "value": {"type": "string"}, "optional": true}, "discountPercentage": {"type": "objectAttribute", "value": {"type": "number"}, "optional": true}, "validFrom": {"type": "objectAttribute", "value": {"type": "string"}, "optional": true}, "validTo": {"type": "objectAttribute", "value": {"type": "string"}, "optional": true}, "applicablePackages": {"type": "objectAttribute", "value": {"type": "array", "of": {"type": "object", "attributes": {"_ref": {"type": "objectAttribute", "value": {"type": "string"}}, "_type": {"type": "objectAttribute", "value": {"type": "string", "value": "reference"}}, "_weak": {"type": "objectAttribute", "value": {"type": "boolean"}, "optional": true}}, "dereferencesTo": "safariPackage", "rest": {"type": "object", "attributes": {"_key": {"type": "objectAttribute", "value": {"type": "string"}}}}}}, "optional": true}}}, {"name": "review", "type": "document", "attributes": {"_id": {"type": "objectAttribute", "value": {"type": "string"}}, "_type": {"type": "objectAttribute", "value": {"type": "string", "value": "review"}}, "_createdAt": {"type": "objectAttribute", "value": {"type": "string"}}, "_updatedAt": {"type": "objectAttribute", "value": {"type": "string"}}, "_rev": {"type": "objectAttribute", "value": {"type": "string"}}, "title": {"type": "objectAttribute", "value": {"type": "string"}, "optional": true}, "slug": {"type": "objectAttribute", "value": {"type": "inline", "name": "slug"}, "optional": true}, "customer": {"type": "objectAttribute", "value": {"type": "object", "attributes": {"name": {"type": "objectAttribute", "value": {"type": "string"}, "optional": true}, "email": {"type": "objectAttribute", "value": {"type": "string"}, "optional": true}, "country": {"type": "objectAttribute", "value": {"type": "string"}, "optional": true}}}, "optional": true}, "safari": {"type": "objectAttribute", "value": {"type": "object", "attributes": {"_ref": {"type": "objectAttribute", "value": {"type": "string"}}, "_type": {"type": "objectAttribute", "value": {"type": "string", "value": "reference"}}, "_weak": {"type": "objectAttribute", "value": {"type": "boolean"}, "optional": true}}, "dereferencesTo": "safariPackage"}, "optional": true}, "ratings": {"type": "objectAttribute", "value": {"type": "object", "attributes": {"overallExperience": {"type": "objectAttribute", "value": {"type": "number"}, "optional": true}, "guideQuality": {"type": "objectAttribute", "value": {"type": "number"}, "optional": true}, "wildlife": {"type": "objectAttribute", "value": {"type": "number"}, "optional": true}, "accommodation": {"type": "objectAttribute", "value": {"type": "number"}, "optional": true}, "value": {"type": "objectAttribute", "value": {"type": "number"}, "optional": true}}}, "optional": true}, "reviewText": {"type": "objectAttribute", "value": {"type": "string"}, "optional": true}, "travelDate": {"type": "objectAttribute", "value": {"type": "string"}, "optional": true}, "verificationStatus": {"type": "objectAttribute", "value": {"type": "object", "attributes": {"isVerified": {"type": "objectAttribute", "value": {"type": "boolean"}, "optional": true}, "verificationMethod": {"type": "objectAttribute", "value": {"type": "union", "of": [{"type": "string", "value": "booking"}, {"type": "string", "value": "email"}, {"type": "string", "value": "manual"}]}, "optional": true}}}, "optional": true}, "images": {"type": "objectAttribute", "value": {"type": "array", "of": {"type": "object", "attributes": {"asset": {"type": "objectAttribute", "value": {"type": "object", "attributes": {"_ref": {"type": "objectAttribute", "value": {"type": "string"}}, "_type": {"type": "objectAttribute", "value": {"type": "string", "value": "reference"}}, "_weak": {"type": "objectAttribute", "value": {"type": "boolean"}, "optional": true}}, "dereferencesTo": "sanity.imageAsset"}, "optional": true}, "hotspot": {"type": "objectAttribute", "value": {"type": "inline", "name": "sanity.imageHotspot"}, "optional": true}, "crop": {"type": "objectAttribute", "value": {"type": "inline", "name": "sanity.imageCrop"}, "optional": true}, "_type": {"type": "objectAttribute", "value": {"type": "string", "value": "image"}}}, "rest": {"type": "object", "attributes": {"_key": {"type": "objectAttribute", "value": {"type": "string"}}}}}}, "optional": true}, "helpfulnessScore": {"type": "objectAttribute", "value": {"type": "object", "attributes": {"upvotes": {"type": "objectAttribute", "value": {"type": "number"}, "optional": true}, "downvotes": {"type": "objectAttribute", "value": {"type": "number"}, "optional": true}}}, "optional": true}, "tags": {"type": "objectAttribute", "value": {"type": "array", "of": {"type": "string"}}, "optional": true}}}, {"name": "guide", "type": "document", "attributes": {"_id": {"type": "objectAttribute", "value": {"type": "string"}}, "_type": {"type": "objectAttribute", "value": {"type": "string", "value": "guide"}}, "_createdAt": {"type": "objectAttribute", "value": {"type": "string"}}, "_updatedAt": {"type": "objectAttribute", "value": {"type": "string"}}, "_rev": {"type": "objectAttribute", "value": {"type": "string"}}, "name": {"type": "objectAttribute", "value": {"type": "string"}, "optional": true}, "slug": {"type": "objectAttribute", "value": {"type": "inline", "name": "slug"}, "optional": true}, "profileImage": {"type": "objectAttribute", "value": {"type": "object", "attributes": {"asset": {"type": "objectAttribute", "value": {"type": "object", "attributes": {"_ref": {"type": "objectAttribute", "value": {"type": "string"}}, "_type": {"type": "objectAttribute", "value": {"type": "string", "value": "reference"}}, "_weak": {"type": "objectAttribute", "value": {"type": "boolean"}, "optional": true}}, "dereferencesTo": "sanity.imageAsset"}, "optional": true}, "hotspot": {"type": "objectAttribute", "value": {"type": "inline", "name": "sanity.imageHotspot"}, "optional": true}, "crop": {"type": "objectAttribute", "value": {"type": "inline", "name": "sanity.imageCrop"}, "optional": true}, "_type": {"type": "objectAttribute", "value": {"type": "string", "value": "image"}}}}, "optional": true}, "personalInfo": {"type": "objectAttribute", "value": {"type": "object", "attributes": {"dateOfBirth": {"type": "objectAttribute", "value": {"type": "string"}, "optional": true}, "nationality": {"type": "objectAttribute", "value": {"type": "string"}, "optional": true}}}, "optional": true}, "professionalDetails": {"type": "objectAttribute", "value": {"type": "object", "attributes": {"yearsOfExperience": {"type": "objectAttribute", "value": {"type": "number"}, "optional": true}, "specializations": {"type": "objectAttribute", "value": {"type": "array", "of": {"type": "string"}}, "optional": true}, "certifications": {"type": "objectAttribute", "value": {"type": "array", "of": {"type": "object", "attributes": {"certificationName": {"type": "objectAttribute", "value": {"type": "string"}, "optional": true}, "issuingOrganization": {"type": "objectAttribute", "value": {"type": "string"}, "optional": true}, "yearIssued": {"type": "objectAttribute", "value": {"type": "number"}, "optional": true}}, "rest": {"type": "object", "attributes": {"_key": {"type": "objectAttribute", "value": {"type": "string"}}}}}}, "optional": true}}}, "optional": true}, "languages": {"type": "objectAttribute", "value": {"type": "array", "of": {"type": "object", "attributes": {"language": {"type": "objectAttribute", "value": {"type": "string"}, "optional": true}, "proficiencyLevel": {"type": "objectAttribute", "value": {"type": "union", "of": [{"type": "string", "value": "native"}, {"type": "string", "value": "fluent"}, {"type": "string", "value": "intermediate"}, {"type": "string", "value": "basic"}]}, "optional": true}}, "rest": {"type": "object", "attributes": {"_key": {"type": "objectAttribute", "value": {"type": "string"}}}}}}, "optional": true}, "biography": {"type": "objectAttribute", "value": {"type": "string"}, "optional": true}, "contactInformation": {"type": "objectAttribute", "value": {"type": "object", "attributes": {"email": {"type": "objectAttribute", "value": {"type": "string"}, "optional": true}, "phoneNumber": {"type": "objectAttribute", "value": {"type": "string"}, "optional": true}}}, "optional": true}, "associatedPackages": {"type": "objectAttribute", "value": {"type": "array", "of": {"type": "object", "attributes": {"_ref": {"type": "objectAttribute", "value": {"type": "string"}}, "_type": {"type": "objectAttribute", "value": {"type": "string", "value": "reference"}}, "_weak": {"type": "objectAttribute", "value": {"type": "boolean"}, "optional": true}}, "dereferencesTo": "safariPackage", "rest": {"type": "object", "attributes": {"_key": {"type": "objectAttribute", "value": {"type": "string"}}}}}}, "optional": true}, "socialMediaLinks": {"type": "objectAttribute", "value": {"type": "object", "attributes": {"linkedIn": {"type": "objectAttribute", "value": {"type": "string"}, "optional": true}, "personalWebsite": {"type": "objectAttribute", "value": {"type": "string"}, "optional": true}}}, "optional": true}, "testimonials": {"type": "objectAttribute", "value": {"type": "array", "of": {"type": "object", "attributes": {"testimonial": {"type": "objectAttribute", "value": {"type": "string"}, "optional": true}, "clientName": {"type": "objectAttribute", "value": {"type": "string"}, "optional": true}, "dateOfTrip": {"type": "objectAttribute", "value": {"type": "string"}, "optional": true}}, "rest": {"type": "object", "attributes": {"_key": {"type": "objectAttribute", "value": {"type": "string"}}}}}}, "optional": true}}}, {"name": "booking", "type": "document", "attributes": {"_id": {"type": "objectAttribute", "value": {"type": "string"}}, "_type": {"type": "objectAttribute", "value": {"type": "string", "value": "booking"}}, "_createdAt": {"type": "objectAttribute", "value": {"type": "string"}}, "_updatedAt": {"type": "objectAttribute", "value": {"type": "string"}}, "_rev": {"type": "objectAttribute", "value": {"type": "string"}}, "bookingId": {"type": "objectAttribute", "value": {"type": "string"}, "optional": true}, "customer": {"type": "objectAttribute", "value": {"type": "object", "attributes": {"_ref": {"type": "objectAttribute", "value": {"type": "string"}}, "_type": {"type": "objectAttribute", "value": {"type": "string", "value": "reference"}}, "_weak": {"type": "objectAttribute", "value": {"type": "boolean"}, "optional": true}}, "dereferencesTo": "customer"}, "optional": true}, "package": {"type": "objectAttribute", "value": {"type": "object", "attributes": {"_ref": {"type": "objectAttribute", "value": {"type": "string"}}, "_type": {"type": "objectAttribute", "value": {"type": "string", "value": "reference"}}, "_weak": {"type": "objectAttribute", "value": {"type": "boolean"}, "optional": true}}, "dereferencesTo": "safariPackage"}, "optional": true}, "travelers": {"type": "objectAttribute", "value": {"type": "number"}, "optional": true}, "travelDates": {"type": "objectAttribute", "value": {"type": "object", "attributes": {"startDate": {"type": "objectAttribute", "value": {"type": "string"}, "optional": true}, "endDate": {"type": "objectAttribute", "value": {"type": "string"}, "optional": true}}}, "optional": true}, "totalPrice": {"type": "objectAttribute", "value": {"type": "number"}, "optional": true}, "status": {"type": "objectAttribute", "value": {"type": "union", "of": [{"type": "string", "value": "confirmed"}, {"type": "string", "value": "pending"}, {"type": "string", "value": "cancelled"}]}, "optional": true}}}, {"name": "customer", "type": "document", "attributes": {"_id": {"type": "objectAttribute", "value": {"type": "string"}}, "_type": {"type": "objectAttribute", "value": {"type": "string", "value": "customer"}}, "_createdAt": {"type": "objectAttribute", "value": {"type": "string"}}, "_updatedAt": {"type": "objectAttribute", "value": {"type": "string"}}, "_rev": {"type": "objectAttribute", "value": {"type": "string"}}, "name": {"type": "objectAttribute", "value": {"type": "string"}, "optional": true}, "email": {"type": "objectAttribute", "value": {"type": "string"}, "optional": true}, "phoneNumber": {"type": "objectAttribute", "value": {"type": "string"}, "optional": true}, "country": {"type": "objectAttribute", "value": {"type": "string"}, "optional": true}, "preferences": {"type": "objectAttribute", "value": {"type": "array", "of": {"type": "string"}}, "optional": true}, "bookingHistory": {"type": "objectAttribute", "value": {"type": "array", "of": {"type": "object", "attributes": {"_ref": {"type": "objectAttribute", "value": {"type": "string"}}, "_type": {"type": "objectAttribute", "value": {"type": "string", "value": "reference"}}, "_weak": {"type": "objectAttribute", "value": {"type": "boolean"}, "optional": true}}, "dereferencesTo": "booking", "rest": {"type": "object", "attributes": {"_key": {"type": "objectAttribute", "value": {"type": "string"}}}}}}, "optional": true}}}, {"name": "accommodation", "type": "document", "attributes": {"_id": {"type": "objectAttribute", "value": {"type": "string"}}, "_type": {"type": "objectAttribute", "value": {"type": "string", "value": "accommodation"}}, "_createdAt": {"type": "objectAttribute", "value": {"type": "string"}}, "_updatedAt": {"type": "objectAttribute", "value": {"type": "string"}}, "_rev": {"type": "objectAttribute", "value": {"type": "string"}}, "name": {"type": "objectAttribute", "value": {"type": "string"}, "optional": true}, "slug": {"type": "objectAttribute", "value": {"type": "inline", "name": "slug"}, "optional": true}, "type": {"type": "objectAttribute", "value": {"type": "union", "of": [{"type": "string", "value": "luxury-lodge"}, {"type": "string", "value": "eco-camp"}, {"type": "string", "value": "tented-camp"}, {"type": "string", "value": "safari-hotel"}, {"type": "string", "value": "mobile-camp"}, {"type": "string", "value": "homestay"}]}, "optional": true}, "mainImage": {"type": "objectAttribute", "value": {"type": "object", "attributes": {"asset": {"type": "objectAttribute", "value": {"type": "object", "attributes": {"_ref": {"type": "objectAttribute", "value": {"type": "string"}}, "_type": {"type": "objectAttribute", "value": {"type": "string", "value": "reference"}}, "_weak": {"type": "objectAttribute", "value": {"type": "boolean"}, "optional": true}}, "dereferencesTo": "sanity.imageAsset"}, "optional": true}, "hotspot": {"type": "objectAttribute", "value": {"type": "inline", "name": "sanity.imageHotspot"}, "optional": true}, "crop": {"type": "objectAttribute", "value": {"type": "inline", "name": "sanity.imageCrop"}, "optional": true}, "_type": {"type": "objectAttribute", "value": {"type": "string", "value": "image"}}}}, "optional": true}, "galleryImages": {"type": "objectAttribute", "value": {"type": "array", "of": {"type": "object", "attributes": {"asset": {"type": "objectAttribute", "value": {"type": "object", "attributes": {"_ref": {"type": "objectAttribute", "value": {"type": "string"}}, "_type": {"type": "objectAttribute", "value": {"type": "string", "value": "reference"}}, "_weak": {"type": "objectAttribute", "value": {"type": "boolean"}, "optional": true}}, "dereferencesTo": "sanity.imageAsset"}, "optional": true}, "hotspot": {"type": "objectAttribute", "value": {"type": "inline", "name": "sanity.imageHotspot"}, "optional": true}, "crop": {"type": "objectAttribute", "value": {"type": "inline", "name": "sanity.imageCrop"}, "optional": true}, "_type": {"type": "objectAttribute", "value": {"type": "string", "value": "image"}}}, "rest": {"type": "object", "attributes": {"_key": {"type": "objectAttribute", "value": {"type": "string"}}}}}}, "optional": true}, "description": {"type": "objectAttribute", "value": {"type": "string"}, "optional": true}, "location": {"type": "objectAttribute", "value": {"type": "object", "attributes": {"destination": {"type": "objectAttribute", "value": {"type": "object", "attributes": {"_ref": {"type": "objectAttribute", "value": {"type": "string"}}, "_type": {"type": "objectAttribute", "value": {"type": "string", "value": "reference"}}, "_weak": {"type": "objectAttribute", "value": {"type": "boolean"}, "optional": true}}, "dereferencesTo": "destination"}, "optional": true}, "coordinates": {"type": "objectAttribute", "value": {"type": "inline", "name": "geopoint"}, "optional": true}, "proximityToWildlife": {"type": "objectAttribute", "value": {"type": "union", "of": [{"type": "string", "value": "within"}, {"type": "string", "value": "bordering"}, {"type": "string", "value": "near"}]}, "optional": true}}}, "optional": true}, "roomTypes": {"type": "objectAttribute", "value": {"type": "array", "of": {"type": "object", "attributes": {"name": {"type": "objectAttribute", "value": {"type": "string"}, "optional": true}, "capacity": {"type": "objectAttribute", "value": {"type": "number"}, "optional": true}, "pricePerNight": {"type": "objectAttribute", "value": {"type": "number"}, "optional": true}}, "rest": {"type": "object", "attributes": {"_key": {"type": "objectAttribute", "value": {"type": "string"}}}}}}, "optional": true}, "amenities": {"type": "objectAttribute", "value": {"type": "object", "attributes": {"commonAmenities": {"type": "objectAttribute", "value": {"type": "array", "of": {"type": "string"}}, "optional": true}, "safariFacilities": {"type": "objectAttribute", "value": {"type": "array", "of": {"type": "string"}}, "optional": true}}}, "optional": true}, "sustainabilityRating": {"type": "objectAttribute", "value": {"type": "object", "attributes": {"ecoCertification": {"type": "objectAttribute", "value": {"type": "union", "of": [{"type": "string", "value": "none"}, {"type": "string", "value": "basic"}, {"type": "string", "value": "certified"}, {"type": "string", "value": "advanced"}]}, "optional": true}, "sustainabilityInitiatives": {"type": "objectAttribute", "value": {"type": "array", "of": {"type": "string"}}, "optional": true}}}, "optional": true}, "recommendedPackages": {"type": "objectAttribute", "value": {"type": "array", "of": {"type": "object", "attributes": {"_ref": {"type": "objectAttribute", "value": {"type": "string"}}, "_type": {"type": "objectAttribute", "value": {"type": "string", "value": "reference"}}, "_weak": {"type": "objectAttribute", "value": {"type": "boolean"}, "optional": true}}, "dereferencesTo": "safariPackage", "rest": {"type": "object", "attributes": {"_key": {"type": "objectAttribute", "value": {"type": "string"}}}}}}, "optional": true}}}, {"name": "safariPackage", "type": "document", "attributes": {"_id": {"type": "objectAttribute", "value": {"type": "string"}}, "_type": {"type": "objectAttribute", "value": {"type": "string", "value": "safariPackage"}}, "_createdAt": {"type": "objectAttribute", "value": {"type": "string"}}, "_updatedAt": {"type": "objectAttribute", "value": {"type": "string"}}, "_rev": {"type": "objectAttribute", "value": {"type": "string"}}, "title": {"type": "objectAttribute", "value": {"type": "string"}, "optional": true}, "slug": {"type": "objectAttribute", "value": {"type": "inline", "name": "slug"}, "optional": true}, "mainImage": {"type": "objectAttribute", "value": {"type": "object", "attributes": {"asset": {"type": "objectAttribute", "value": {"type": "object", "attributes": {"_ref": {"type": "objectAttribute", "value": {"type": "string"}}, "_type": {"type": "objectAttribute", "value": {"type": "string", "value": "reference"}}, "_weak": {"type": "objectAttribute", "value": {"type": "boolean"}, "optional": true}}, "dereferencesTo": "sanity.imageAsset"}, "optional": true}, "hotspot": {"type": "objectAttribute", "value": {"type": "inline", "name": "sanity.imageHotspot"}, "optional": true}, "crop": {"type": "objectAttribute", "value": {"type": "inline", "name": "sanity.imageCrop"}, "optional": true}, "_type": {"type": "objectAttribute", "value": {"type": "string", "value": "image"}}}}, "optional": true}, "galleryImages": {"type": "objectAttribute", "value": {"type": "array", "of": {"type": "object", "attributes": {"asset": {"type": "objectAttribute", "value": {"type": "object", "attributes": {"_ref": {"type": "objectAttribute", "value": {"type": "string"}}, "_type": {"type": "objectAttribute", "value": {"type": "string", "value": "reference"}}, "_weak": {"type": "objectAttribute", "value": {"type": "boolean"}, "optional": true}}, "dereferencesTo": "sanity.imageAsset"}, "optional": true}, "hotspot": {"type": "objectAttribute", "value": {"type": "inline", "name": "sanity.imageHotspot"}, "optional": true}, "crop": {"type": "objectAttribute", "value": {"type": "inline", "name": "sanity.imageCrop"}, "optional": true}, "_type": {"type": "objectAttribute", "value": {"type": "string", "value": "image"}}}, "rest": {"type": "object", "attributes": {"_key": {"type": "objectAttribute", "value": {"type": "string"}}}}}}, "optional": true}, "shortDescription": {"type": "objectAttribute", "value": {"type": "string"}, "optional": true}, "description": {"type": "objectAttribute", "value": {"type": "string"}, "optional": true}, "duration": {"type": "objectAttribute", "value": {"type": "number"}, "optional": true}, "difficultyLevel": {"type": "objectAttribute", "value": {"type": "union", "of": [{"type": "string", "value": "easy"}, {"type": "string", "value": "moderate"}, {"type": "string", "value": "challenging"}]}, "optional": true}, "price": {"type": "objectAttribute", "value": {"type": "number"}, "optional": true}, "capacity": {"type": "objectAttribute", "value": {"type": "number"}, "optional": true}, "includedServices": {"type": "objectAttribute", "value": {"type": "array", "of": {"type": "string"}}, "optional": true}, "excludedServices": {"type": "objectAttribute", "value": {"type": "array", "of": {"type": "string"}}, "optional": true}, "itinerary": {"type": "objectAttribute", "value": {"type": "array", "of": {"type": "object", "attributes": {"day": {"type": "objectAttribute", "value": {"type": "number"}, "optional": true}, "description": {"type": "objectAttribute", "value": {"type": "string"}, "optional": true}}, "rest": {"type": "object", "attributes": {"_key": {"type": "objectAttribute", "value": {"type": "string"}}}}}}, "optional": true}, "destinations": {"type": "objectAttribute", "value": {"type": "array", "of": {"type": "object", "attributes": {"_ref": {"type": "objectAttribute", "value": {"type": "string"}}, "_type": {"type": "objectAttribute", "value": {"type": "string", "value": "reference"}}, "_weak": {"type": "objectAttribute", "value": {"type": "boolean"}, "optional": true}}, "dereferencesTo": "destination", "rest": {"type": "object", "attributes": {"_key": {"type": "objectAttribute", "value": {"type": "string"}}}}}}, "optional": true}, "recommendedSeason": {"type": "objectAttribute", "value": {"type": "union", "of": [{"type": "string", "value": "dry"}, {"type": "string", "value": "wet"}, {"type": "string", "value": "year-round"}]}, "optional": true}, "minimumAge": {"type": "objectAttribute", "value": {"type": "number"}, "optional": true}, "highlights": {"type": "objectAttribute", "value": {"type": "array", "of": {"type": "string"}}, "optional": true}, "specialRequirements": {"type": "objectAttribute", "value": {"type": "array", "of": {"type": "string"}}, "optional": true}, "environmentalImpact": {"type": "objectAttribute", "value": {"type": "object", "attributes": {"carbonOffset": {"type": "objectAttribute", "value": {"type": "boolean"}, "optional": true}, "conservationContribution": {"type": "objectAttribute", "value": {"type": "number"}, "optional": true}, "sustainabilityRating": {"type": "objectAttribute", "value": {"type": "union", "of": [{"type": "string", "value": "low"}, {"type": "string", "value": "moderate"}, {"type": "string", "value": "high"}, {"type": "string", "value": "zero"}]}, "optional": true}}}, "optional": true}, "photographyOpportunities": {"type": "objectAttribute", "value": {"type": "object", "attributes": {"wildlife": {"type": "objectAttribute", "value": {"type": "boolean"}, "optional": true}, "landscapeShooting": {"type": "objectAttribute", "value": {"type": "boolean"}, "optional": true}, "recommendedEquipment": {"type": "objectAttribute", "value": {"type": "array", "of": {"type": "string"}}, "optional": true}}}, "optional": true}, "healthAndSafety": {"type": "objectAttribute", "value": {"type": "object", "attributes": {"medicalConsiderations": {"type": "objectAttribute", "value": {"type": "string"}, "optional": true}, "emergencySupport": {"type": "objectAttribute", "value": {"type": "object", "attributes": {"guidedSupport": {"type": "objectAttribute", "value": {"type": "boolean"}, "optional": true}, "evacuationCoverage": {"type": "objectAttribute", "value": {"type": "boolean"}, "optional": true}}}, "optional": true}}}, "optional": true}, "additionalGuides": {"type": "objectAttribute", "value": {"type": "array", "of": {"type": "object", "attributes": {"_ref": {"type": "objectAttribute", "value": {"type": "string"}}, "_type": {"type": "objectAttribute", "value": {"type": "string", "value": "reference"}}, "_weak": {"type": "objectAttribute", "value": {"type": "boolean"}, "optional": true}}, "dereferencesTo": "guide", "rest": {"type": "object", "attributes": {"_key": {"type": "objectAttribute", "value": {"type": "string"}}}}}}, "optional": true}, "specialOffers": {"type": "objectAttribute", "value": {"type": "object", "attributes": {"discountEligibility": {"type": "objectAttribute", "value": {"type": "array", "of": {"type": "string"}}, "optional": true}, "earlyBookingDiscount": {"type": "objectAttribute", "value": {"type": "number"}, "optional": true}}}, "optional": true}}}, {"name": "destination", "type": "document", "attributes": {"_id": {"type": "objectAttribute", "value": {"type": "string"}}, "_type": {"type": "objectAttribute", "value": {"type": "string", "value": "destination"}}, "_createdAt": {"type": "objectAttribute", "value": {"type": "string"}}, "_updatedAt": {"type": "objectAttribute", "value": {"type": "string"}}, "_rev": {"type": "objectAttribute", "value": {"type": "string"}}, "name": {"type": "objectAttribute", "value": {"type": "string"}, "optional": true}, "slug": {"type": "objectAttribute", "value": {"type": "inline", "name": "slug"}, "optional": true}, "mainImage": {"type": "objectAttribute", "value": {"type": "object", "attributes": {"asset": {"type": "objectAttribute", "value": {"type": "object", "attributes": {"_ref": {"type": "objectAttribute", "value": {"type": "string"}}, "_type": {"type": "objectAttribute", "value": {"type": "string", "value": "reference"}}, "_weak": {"type": "objectAttribute", "value": {"type": "boolean"}, "optional": true}}, "dereferencesTo": "sanity.imageAsset"}, "optional": true}, "hotspot": {"type": "objectAttribute", "value": {"type": "inline", "name": "sanity.imageHotspot"}, "optional": true}, "crop": {"type": "objectAttribute", "value": {"type": "inline", "name": "sanity.imageCrop"}, "optional": true}, "_type": {"type": "objectAttribute", "value": {"type": "string", "value": "image"}}}}, "optional": true}, "galleryImages": {"type": "objectAttribute", "value": {"type": "array", "of": {"type": "object", "attributes": {"asset": {"type": "objectAttribute", "value": {"type": "object", "attributes": {"_ref": {"type": "objectAttribute", "value": {"type": "string"}}, "_type": {"type": "objectAttribute", "value": {"type": "string", "value": "reference"}}, "_weak": {"type": "objectAttribute", "value": {"type": "boolean"}, "optional": true}}, "dereferencesTo": "sanity.imageAsset"}, "optional": true}, "hotspot": {"type": "objectAttribute", "value": {"type": "inline", "name": "sanity.imageHotspot"}, "optional": true}, "crop": {"type": "objectAttribute", "value": {"type": "inline", "name": "sanity.imageCrop"}, "optional": true}, "_type": {"type": "objectAttribute", "value": {"type": "string", "value": "image"}}}, "rest": {"type": "object", "attributes": {"_key": {"type": "objectAttribute", "value": {"type": "string"}}}}}}, "optional": true}, "description": {"type": "objectAttribute", "value": {"type": "string"}, "optional": true}, "location": {"type": "objectAttribute", "value": {"type": "object", "attributes": {"country": {"type": "objectAttribute", "value": {"type": "string"}, "optional": true}, "region": {"type": "objectAttribute", "value": {"type": "string"}, "optional": true}, "coordinates": {"type": "objectAttribute", "value": {"type": "inline", "name": "geopoint"}, "optional": true}}}, "optional": true}, "climate": {"type": "objectAttribute", "value": {"type": "object", "attributes": {"averageTemperature": {"type": "objectAttribute", "value": {"type": "object", "attributes": {"min": {"type": "objectAttribute", "value": {"type": "number"}, "optional": true}, "max": {"type": "objectAttribute", "value": {"type": "number"}, "optional": true}}}, "optional": true}, "rainfallSeason": {"type": "objectAttribute", "value": {"type": "union", "of": [{"type": "string", "value": "dry"}, {"type": "string", "value": "wet"}, {"type": "string", "value": "year-round"}]}, "optional": true}}}, "optional": true}, "wildlifeHighlights": {"type": "objectAttribute", "value": {"type": "array", "of": {"type": "string"}}, "optional": true}, "ecosystemTypes": {"type": "objectAttribute", "value": {"type": "array", "of": {"type": "string"}}, "optional": true}, "travelConsiderations": {"type": "objectAttribute", "value": {"type": "object", "attributes": {"bestTimeToVisit": {"type": "objectAttribute", "value": {"type": "array", "of": {"type": "string"}}, "optional": true}, "travelRestrictions": {"type": "objectAttribute", "value": {"type": "string"}, "optional": true}, "requiredVaccinations": {"type": "objectAttribute", "value": {"type": "array", "of": {"type": "string"}}, "optional": true}}}, "optional": true}, "recommendedPackages": {"type": "objectAttribute", "value": {"type": "array", "of": {"type": "object", "attributes": {"_ref": {"type": "objectAttribute", "value": {"type": "string"}}, "_type": {"type": "objectAttribute", "value": {"type": "string", "value": "reference"}}, "_weak": {"type": "objectAttribute", "value": {"type": "boolean"}, "optional": true}}, "dereferencesTo": "safariPackage", "rest": {"type": "object", "attributes": {"_key": {"type": "objectAttribute", "value": {"type": "string"}}}}}}, "optional": true}, "conservationStatus": {"type": "objectAttribute", "value": {"type": "object", "attributes": {"protectedArea": {"type": "objectAttribute", "value": {"type": "boolean"}, "optional": true}, "conservationEfforts": {"type": "objectAttribute", "value": {"type": "string"}, "optional": true}}}, "optional": true}}}, {"name": "geopoint", "type": "type", "value": {"type": "object", "attributes": {"_type": {"type": "objectAttribute", "value": {"type": "string", "value": "geopoint"}}, "lat": {"type": "objectAttribute", "value": {"type": "number"}, "optional": true}, "lng": {"type": "objectAttribute", "value": {"type": "number"}, "optional": true}, "alt": {"type": "objectAttribute", "value": {"type": "number"}, "optional": true}}}}, {"name": "sanity.imageCrop", "type": "type", "value": {"type": "object", "attributes": {"_type": {"type": "objectAttribute", "value": {"type": "string", "value": "sanity.imageCrop"}}, "top": {"type": "objectAttribute", "value": {"type": "number"}, "optional": true}, "bottom": {"type": "objectAttribute", "value": {"type": "number"}, "optional": true}, "left": {"type": "objectAttribute", "value": {"type": "number"}, "optional": true}, "right": {"type": "objectAttribute", "value": {"type": "number"}, "optional": true}}}}, {"name": "sanity.imageHotspot", "type": "type", "value": {"type": "object", "attributes": {"_type": {"type": "objectAttribute", "value": {"type": "string", "value": "sanity.imageHotspot"}}, "x": {"type": "objectAttribute", "value": {"type": "number"}, "optional": true}, "y": {"type": "objectAttribute", "value": {"type": "number"}, "optional": true}, "height": {"type": "objectAttribute", "value": {"type": "number"}, "optional": true}, "width": {"type": "objectAttribute", "value": {"type": "number"}, "optional": true}}}}, {"name": "sanity.imageAsset", "type": "document", "attributes": {"_id": {"type": "objectAttribute", "value": {"type": "string"}}, "_type": {"type": "objectAttribute", "value": {"type": "string", "value": "sanity.imageAsset"}}, "_createdAt": {"type": "objectAttribute", "value": {"type": "string"}}, "_updatedAt": {"type": "objectAttribute", "value": {"type": "string"}}, "_rev": {"type": "objectAttribute", "value": {"type": "string"}}, "originalFilename": {"type": "objectAttribute", "value": {"type": "string"}, "optional": true}, "label": {"type": "objectAttribute", "value": {"type": "string"}, "optional": true}, "title": {"type": "objectAttribute", "value": {"type": "string"}, "optional": true}, "description": {"type": "objectAttribute", "value": {"type": "string"}, "optional": true}, "altText": {"type": "objectAttribute", "value": {"type": "string"}, "optional": true}, "sha1hash": {"type": "objectAttribute", "value": {"type": "string"}, "optional": true}, "extension": {"type": "objectAttribute", "value": {"type": "string"}, "optional": true}, "mimeType": {"type": "objectAttribute", "value": {"type": "string"}, "optional": true}, "size": {"type": "objectAttribute", "value": {"type": "number"}, "optional": true}, "assetId": {"type": "objectAttribute", "value": {"type": "string"}, "optional": true}, "uploadId": {"type": "objectAttribute", "value": {"type": "string"}, "optional": true}, "path": {"type": "objectAttribute", "value": {"type": "string"}, "optional": true}, "url": {"type": "objectAttribute", "value": {"type": "string"}, "optional": true}, "metadata": {"type": "objectAttribute", "value": {"type": "inline", "name": "sanity.imageMetadata"}, "optional": true}, "source": {"type": "objectAttribute", "value": {"type": "inline", "name": "sanity.assetSourceData"}, "optional": true}}}, {"name": "sanity.assetSourceData", "type": "type", "value": {"type": "object", "attributes": {"_type": {"type": "objectAttribute", "value": {"type": "string", "value": "sanity.assetSourceData"}}, "name": {"type": "objectAttribute", "value": {"type": "string"}, "optional": true}, "id": {"type": "objectAttribute", "value": {"type": "string"}, "optional": true}, "url": {"type": "objectAttribute", "value": {"type": "string"}, "optional": true}}}}, {"name": "sanity.imageMetadata", "type": "type", "value": {"type": "object", "attributes": {"_type": {"type": "objectAttribute", "value": {"type": "string", "value": "sanity.imageMetadata"}}, "location": {"type": "objectAttribute", "value": {"type": "inline", "name": "geopoint"}, "optional": true}, "dimensions": {"type": "objectAttribute", "value": {"type": "inline", "name": "sanity.imageDimensions"}, "optional": true}, "palette": {"type": "objectAttribute", "value": {"type": "inline", "name": "sanity.imagePalette"}, "optional": true}, "lqip": {"type": "objectAttribute", "value": {"type": "string"}, "optional": true}, "blurHash": {"type": "objectAttribute", "value": {"type": "string"}, "optional": true}, "hasAlpha": {"type": "objectAttribute", "value": {"type": "boolean"}, "optional": true}, "isOpaque": {"type": "objectAttribute", "value": {"type": "boolean"}, "optional": true}}}}, {"name": "media.tag", "type": "document", "attributes": {"_id": {"type": "objectAttribute", "value": {"type": "string"}}, "_type": {"type": "objectAttribute", "value": {"type": "string", "value": "media.tag"}}, "_createdAt": {"type": "objectAttribute", "value": {"type": "string"}}, "_updatedAt": {"type": "objectAttribute", "value": {"type": "string"}}, "_rev": {"type": "objectAttribute", "value": {"type": "string"}}, "name": {"type": "objectAttribute", "value": {"type": "inline", "name": "slug"}, "optional": true}}}, {"name": "slug", "type": "type", "value": {"type": "object", "attributes": {"_type": {"type": "objectAttribute", "value": {"type": "string", "value": "slug"}}, "current": {"type": "objectAttribute", "value": {"type": "string"}, "optional": true}, "source": {"type": "objectAttribute", "value": {"type": "string"}, "optional": true}}}}]