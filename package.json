{"name": "avon-safaris", "private": true, "version": "1.0.0", "main": "package.json", "license": "UNLICENSED", "scripts": {"dev": "sanity dev", "start": "sanity start", "build": "sanity build", "deploy": "sanity deploy", "deploy-graphql": "sanity graphql deploy"}, "keywords": ["sanity"], "dependencies": {"@sanity/assist": "^4.4.7", "@sanity/cli": "^4.3.0", "@sanity/client": "^7.8.2", "@sanity/document-internationalization": "^4.0.0", "@sanity/functions": "^1.0.3", "@sanity/orderable-document-list": "^1.4.0", "@sanity/vision": "^4.3.0", "lucide-react": "^0.536.0", "react": "^19.1.1", "react-dom": "^19.1.1", "sanity": "^4.3.0", "sanity-plugin-image-asset-picker": "^1.0.1", "sanity-plugin-internationalized-array": "^3.1.4", "sanity-plugin-media": "^4.0.0", "styled-components": "^6.1.19", "vite": "^7.0.6"}, "devDependencies": {"@sanity/eslint-config-studio": "^5.0.2", "@types/react": "^19.1.9", "eslint": "^9.32.0", "prettier": "^3.6.2", "typescript": "^5.9.2"}, "prettier": {"semi": false, "printWidth": 100, "bracketSpacing": false, "singleQuote": true}}