import { Bed } from 'lucide-react'
import { defineField, defineType } from 'sanity'

export default defineType({
  name: 'accommodation',
  title: 'Safari Accommodation',
  icon: Bed,
  type: 'document',
  fields: [
    defineField({
      name: 'name',
      title: 'Accommodation Name',
      type: 'string',
      validation: (Rule) => Rule.required().min(2).max(100),
    }),
    defineField({
      name: 'slug',
      title: 'Slug',
      type: 'slug',
      options: {
        source: 'name',
        maxLength: 96,
        isUnique: async (slug, context) => {
          const {document, getClient} = context
          const language = document?.language

          const documents = await getClient({apiVersion: '2023-01-01'}).fetch(
            `*[
              _type == "accommodation" && 
              slug.current == $slug && 
              language == $language && 
              !(_id in [$draftId, $publishedId])
            ]`,
            {
              slug: slug,
              language: language,
              draftId: `drafts.${document?._id}`,
              publishedId: document?._id ?? '',
            },
          )

          if (documents.length > 1) {
            return false
          }

          return true
        },
      },
      validation: (Rule) => Rule.required(),
    }),
    defineField({
      name: 'metaTitle',
      title: 'Meta Title',
      type: 'string',
      description: 'Title used for SEO (defaults to accommodation name if empty)',
    }),
    defineField({
      name: 'metaDescription',
      title: 'Meta Description',
      type: 'text',
      description: 'Description used for SEO and social sharing',
    }),
    defineField({
      name: 'type',
      title: 'Accommodation Type',
      type: 'string',
      options: {
        list: [
          {title: 'Luxury Lodge', value: 'luxury-lodge'},
          {title: 'Eco-Camp', value: 'eco-camp'},
          {title: 'Tented Camp', value: 'tented-camp'},
          {title: 'Safari Hotel', value: 'safari-hotel'},
          {title: 'Mobile Camp', value: 'mobile-camp'},
          {title: 'Homestay', value: 'homestay'},
        ],
        aiAssist: {exclude: true},
      },
      validation: (Rule) => Rule.required(),
    }),
    defineField({
      name: 'pricingTier',
      title: 'Pricing Tier',
      type: 'string',
      options: {
        list: [
          {title: 'Budget', value: 'budget'},
          {title: 'Luxury', value: 'luxury'},
        ],
        layout: 'radio',
        direction: 'horizontal',
        aiAssist: {exclude: true},
      },
      validation: (Rule) => Rule.required(),
    }),
    defineField({
      name: 'recommended',
      title: 'Recommended Accommodation',
      type: 'boolean',
      description: 'Highlight this accommodation as recommended',
      initialValue: false,
    }),
    defineField({
      name: 'mainImage',
      title: 'Main Image',
      type: 'image',
      options: {
        hotspot: true,
      },
      validation: (Rule) => Rule.required(),
    }),
    defineField({
      name: 'galleryImages',
      title: 'Gallery Images',
      type: 'array',
      of: [
        {
          type: 'image',
          options: {
            hotspot: true,
          },
        },
      ],
      options: {
        layout: 'grid',
      },
      validation: (Rule) => Rule.unique(),
    }),
    defineField({
      name: 'description',
      title: 'Accommodation Description',
      type: 'array',
      of: [
        {
          type: 'block',
          styles: [
            {title: 'Normal', value: 'normal'},
            {title: 'H3', value: 'h3'},
            {title: 'H4', value: 'h4'},
          ],
          lists: [
            {title: 'Bullet', value: 'bullet'},
            {title: 'Numbered', value: 'number'},
          ],
          marks: {
            decorators: [
              {title: 'Strong', value: 'strong'},
              {title: 'Emphasis', value: 'em'},
            ],
            annotations: [
              {
                name: 'link',
                type: 'object',
                title: 'URL',
                fields: [
                  {
                    title: 'URL',
                    name: 'href',
                    type: 'url',
                    validation: (Rule) =>
                      Rule.uri({
                        scheme: ['http', 'https', 'mailto', 'tel'],
                      }),
                  },
                ],
              },
            ],
          },
        },
      ],
      validation: (Rule) => Rule.required(),
    }),
    defineField({
      name: 'location',
      title: 'Location Details',
      type: 'object',
      fields: [
        defineField({
          name: 'destination',
          title: 'Destination',
          type: 'reference',
          to: [{type: 'destination'}],
          validation: (Rule) => Rule.required(),
          options: {
            filter: ({document}) => {
              return {
                filter: 'language == $language',
                params: {language: document.language},
              }
            },
          },
        }),
        defineField({
          name: 'coordinates',
          title: 'GPS Coordinates',
          type: 'geopoint',
          options: {
            aiAssist: {exclude: true},
          },
        }),
        defineField({
          name: 'proximityToWildlife',
          title: 'Proximity to Wildlife Areas',
          type: 'string',
          options: {
            list: [
              {title: 'Within Wildlife Area', value: 'within'},
              {title: 'Bordering Wildlife Area', value: 'bordering'},
              {title: 'Near Wildlife Area', value: 'near'},
            ],
          },
        }),
      ],
    }),
    defineField({
      name: 'importantInformation',
      title: 'Important Information',
      description: 'Key details guests should know before booking',
      type: 'array',
      of: [
        {
          type: 'block',
          styles: [
            {title: 'Normal', value: 'normal'},
            {title: 'H3', value: 'h3'},
            {title: 'H4', value: 'h4'},
          ],
          lists: [
            {title: 'Bullet', value: 'bullet'},
            {title: 'Numbered', value: 'number'},
          ],
          marks: {
            decorators: [
              {title: 'Strong', value: 'strong'},
              {title: 'Emphasis', value: 'em'},
            ],
            annotations: [
              {
                name: 'link',
                type: 'object',
                title: 'URL',
                fields: [
                  {
                    title: 'URL',
                    name: 'href',
                    type: 'url',
                    validation: (Rule) =>
                      Rule.uri({
                        scheme: ['http', 'https', 'mailto', 'tel'],
                      }),
                  },
                ],
              },
            ],
          },
        },
      ],
    }),
    defineField({
      name: 'language',
      type: 'string',
      readOnly: true,
      hidden: true,
    }),
    defineField({
      name: 'starRating',
      title: 'Star Rating',
      type: 'number',
      description: 'Official star rating of the accommodation (1-5)',
      validation: (Rule) =>
        Rule.required().min(1).max(5).precision(1).error('Star rating must be between 1 and 5'),
    }),
    defineField({
      name: 'guestReviews',
      title: 'Guest Reviews',
      type: 'array',
      of: [
        {
          type: 'object',
          fields: [
            {
              name: 'guestName',
              title: 'Guest Name',
              type: 'string',
              validation: (Rule) => Rule.required(),
            },
            {
              name: 'dateVisited',
              title: 'Date Visited',
              type: 'string',
              validation: (Rule) => Rule.required(),
            },
            {
              name: 'rating',
              title: 'Rating',
              type: 'number',
              validation: (Rule) =>
                Rule.required().min(1).max(5).precision(1).error('Rating must be between 1 and 5'),
            },
            {
              name: 'review',
              title: 'Review',
              type: 'text',
              validation: (Rule) => Rule.required(),
            },
          ],
          preview: {
            select: {
              title: 'guestName',
              rating: 'rating',
              review: 'review',
              dateVisited: 'dateVisited',
            },
            prepare(selection) {
              const stars = '★'.repeat(selection.rating || 0) + '☆'.repeat(5 - (selection.rating || 0));
              const reviewPreview = selection.review
                ? `${selection.review.substring(0, 80)}${selection.review.length > 80 ? '...' : ''}`
                : 'No review text';

              return {
                title: `${selection.title} (${selection.dateVisited})`,
                subtitle: `${stars} - ${reviewPreview}`,
              }
            },
          },
        },
      ],
    }),
    defineField({
      name: 'faqs',
      title: 'FAQs',
      description: 'Frequently Asked Questions',
      type: 'array',
      of: [
        {
          type: 'object',
          fields: [
            {
              name: 'question',
              title: 'Question',
              type: 'string',
              validation: (Rule) => Rule.required(),
            },
            {
              name: 'answer',
              title: 'Answer',
              type: 'array',
              of: [
                {
                  type: 'block',
                  styles: [
                    {title: 'Normal', value: 'normal'},
                    {title: 'H4', value: 'h4'},
                  ],
                  lists: [
                    {title: 'Bullet', value: 'bullet'},
                    {title: 'Numbered', value: 'number'},
                  ],
                  marks: {
                    decorators: [
                      {title: 'Strong', value: 'strong'},
                      {title: 'Emphasis', value: 'em'},
                    ],
                    annotations: [
                      {
                        name: 'link',
                        type: 'object',
                        title: 'URL',
                        fields: [
                          {
                            title: 'URL',
                            name: 'href',
                            type: 'url',
                            validation: (Rule) =>
                              Rule.uri({
                                scheme: ['http', 'https', 'mailto', 'tel'],
                              }),
                          },
                        ],
                      },
                    ],
                  },
                },
              ],
              validation: (Rule) => Rule.required(),
            },
          ],
          preview: {
            select: {
              title: 'question',
              subtitle: 'answer',
            },
            prepare(selection) {
              // Convert the rich text answer array to plain text for preview
              const answerText = selection.subtitle
                ?.map((block: any) =>
                  block._type === 'block'
                    ? block.children?.map((child: any) => child.text).join('')
                    : ''
                )
                .join(' ')
                .trim();

              return {
                title: selection.title,
                subtitle: answerText ? `${answerText.substring(0, 100)}${answerText.length > 100 ? '...' : ''}` : 'No answer provided',
              }
            },
          },
        },
      ],
    }),
  ],
  preview: {
    select: {
      title: 'name',
      type: 'type',
      media: 'mainImage',
      destination: 'location.destination.name',
    },
    prepare(selection) {
      return {
        title: selection.title,
        subtitle: `${selection.type} at ${selection.destination}`,
        media: selection.media,
      }
    },
  },
})
