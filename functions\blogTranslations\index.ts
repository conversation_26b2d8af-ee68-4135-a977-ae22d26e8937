import {documentEvent<PERSON>andler} from '@sanity/functions'
import {createClient} from '@sanity/client'
import {languages} from '../../languages'

const token = process.env.SANITY_API_TOKEN

const client = createClient({
  projectId: 'dtgxqbm7',
  dataset: 'production',
  useCdn: false,
  apiVersion: 'vX',
  token: token,
})

// In a Sanity Function triggered on publish
export const handler = documentEventHandler(async ({context, event}) => {
  // Only translate posts
  // if (event.data._type !== 'post') return

   console.log("Context: ", context)
  console.log("Event: ", event)

  const filteredLanguages = languages.filter(lang => lang.id !== 'en')

  for (const language of filteredLanguages) {
    await client.agent.action.translate({
      schemaId: '_.schemas.default',
      documentId: event.data._id,
      targetDocument: {operation: 'create'},
      fromLanguage: {id: 'en', title: 'English'},
      toLanguage: language,
      styleGuide: 'Preserve tone and technical accuracy.',
    })

    console.log(`Translated post ${event.data._id} to ${language.title}`)
  }
})
