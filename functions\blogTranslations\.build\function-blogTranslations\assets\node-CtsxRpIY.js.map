{"version": 3, "file": "node-CtsxRpIY.js", "sources": ["../../../../../node_modules/eventsource/lib/eventsource.js", "../../../../../node_modules/@sanity/eventsource/node.js"], "sourcesContent": ["var parse = require('url').parse\nvar events = require('events')\nvar https = require('https')\nvar http = require('http')\nvar util = require('util')\n\nvar httpsOptions = [\n  'pfx', 'key', 'passphrase', 'cert', 'ca', 'ciphers',\n  'rejectUnauthorized', 'secureProtocol', 'servername', 'checkServerIdentity'\n]\n\nvar bom = [239, 187, 191]\nvar colon = 58\nvar space = 32\nvar lineFeed = 10\nvar carriageReturn = 13\n// Beyond 256KB we could not observe any gain in performance\nvar maxBufferAheadAllocation = 1024 * 256\n// Headers matching the pattern should be removed when redirecting to different origin\nvar reUnsafeHeader = /^(cookie|authorization)$/i\n\nfunction hasBom (buf) {\n  return bom.every(function (charCode, index) {\n    return buf[index] === charCode\n  })\n}\n\n/**\n * Creates a new EventSource object\n *\n * @param {String} url the URL to which to connect\n * @param {Object} [eventSourceInitDict] extra init params. See README for details.\n * @api public\n **/\nfunction EventSource (url, eventSourceInitDict) {\n  var readyState = EventSource.CONNECTING\n  var headers = eventSourceInitDict && eventSourceInitDict.headers\n  var hasNewOrigin = false\n  Object.defineProperty(this, 'readyState', {\n    get: function () {\n      return readyState\n    }\n  })\n\n  Object.defineProperty(this, 'url', {\n    get: function () {\n      return url\n    }\n  })\n\n  var self = this\n  self.reconnectInterval = 1000\n  self.connectionInProgress = false\n\n  function onConnectionClosed (message) {\n    if (readyState === EventSource.CLOSED) return\n    readyState = EventSource.CONNECTING\n    _emit('error', new Event('error', {message: message}))\n\n    // The url may have been changed by a temporary redirect. If that's the case,\n    // revert it now, and flag that we are no longer pointing to a new origin\n    if (reconnectUrl) {\n      url = reconnectUrl\n      reconnectUrl = null\n      hasNewOrigin = false\n    }\n    setTimeout(function () {\n      if (readyState !== EventSource.CONNECTING || self.connectionInProgress) {\n        return\n      }\n      self.connectionInProgress = true\n      connect()\n    }, self.reconnectInterval)\n  }\n\n  var req\n  var lastEventId = ''\n  if (headers && headers['Last-Event-ID']) {\n    lastEventId = headers['Last-Event-ID']\n    delete headers['Last-Event-ID']\n  }\n\n  var discardTrailingNewline = false\n  var data = ''\n  var eventName = ''\n\n  var reconnectUrl = null\n\n  function connect () {\n    var options = parse(url)\n    var isSecure = options.protocol === 'https:'\n    options.headers = { 'Cache-Control': 'no-cache', 'Accept': 'text/event-stream' }\n    if (lastEventId) options.headers['Last-Event-ID'] = lastEventId\n    if (headers) {\n      var reqHeaders = hasNewOrigin ? removeUnsafeHeaders(headers) : headers\n      for (var i in reqHeaders) {\n        var header = reqHeaders[i]\n        if (header) {\n          options.headers[i] = header\n        }\n      }\n    }\n\n    // Legacy: this should be specified as `eventSourceInitDict.https.rejectUnauthorized`,\n    // but for now exists as a backwards-compatibility layer\n    options.rejectUnauthorized = !(eventSourceInitDict && !eventSourceInitDict.rejectUnauthorized)\n\n    if (eventSourceInitDict && eventSourceInitDict.createConnection !== undefined) {\n      options.createConnection = eventSourceInitDict.createConnection\n    }\n\n    // If specify http proxy, make the request to sent to the proxy server,\n    // and include the original url in path and Host headers\n    var useProxy = eventSourceInitDict && eventSourceInitDict.proxy\n    if (useProxy) {\n      var proxy = parse(eventSourceInitDict.proxy)\n      isSecure = proxy.protocol === 'https:'\n\n      options.protocol = isSecure ? 'https:' : 'http:'\n      options.path = url\n      options.headers.Host = options.host\n      options.hostname = proxy.hostname\n      options.host = proxy.host\n      options.port = proxy.port\n    }\n\n    // If https options are specified, merge them into the request options\n    if (eventSourceInitDict && eventSourceInitDict.https) {\n      for (var optName in eventSourceInitDict.https) {\n        if (httpsOptions.indexOf(optName) === -1) {\n          continue\n        }\n\n        var option = eventSourceInitDict.https[optName]\n        if (option !== undefined) {\n          options[optName] = option\n        }\n      }\n    }\n\n    // Pass this on to the XHR\n    if (eventSourceInitDict && eventSourceInitDict.withCredentials !== undefined) {\n      options.withCredentials = eventSourceInitDict.withCredentials\n    }\n\n    req = (isSecure ? https : http).request(options, function (res) {\n      self.connectionInProgress = false\n      // Handle HTTP errors\n      if (res.statusCode === 500 || res.statusCode === 502 || res.statusCode === 503 || res.statusCode === 504) {\n        _emit('error', new Event('error', {status: res.statusCode, message: res.statusMessage}))\n        onConnectionClosed()\n        return\n      }\n\n      // Handle HTTP redirects\n      if (res.statusCode === 301 || res.statusCode === 302 || res.statusCode === 307) {\n        var location = res.headers.location\n        if (!location) {\n          // Server sent redirect response without Location header.\n          _emit('error', new Event('error', {status: res.statusCode, message: res.statusMessage}))\n          return\n        }\n        var prevOrigin = new URL(url).origin\n        var nextOrigin = new URL(location).origin\n        hasNewOrigin = prevOrigin !== nextOrigin\n        if (res.statusCode === 307) reconnectUrl = url\n        url = location\n        process.nextTick(connect)\n        return\n      }\n\n      if (res.statusCode !== 200) {\n        _emit('error', new Event('error', {status: res.statusCode, message: res.statusMessage}))\n        return self.close()\n      }\n\n      readyState = EventSource.OPEN\n      res.on('close', function () {\n        res.removeAllListeners('close')\n        res.removeAllListeners('end')\n        onConnectionClosed()\n      })\n\n      res.on('end', function () {\n        res.removeAllListeners('close')\n        res.removeAllListeners('end')\n        onConnectionClosed()\n      })\n      _emit('open', new Event('open'))\n\n      // text/event-stream parser adapted from webkit's\n      // Source/WebCore/page/EventSource.cpp\n      var buf\n      var newBuffer\n      var startingPos = 0\n      var startingFieldLength = -1\n      var newBufferSize = 0\n      var bytesUsed = 0\n\n      res.on('data', function (chunk) {\n        if (!buf) {\n          buf = chunk\n          if (hasBom(buf)) {\n            buf = buf.slice(bom.length)\n          }\n          bytesUsed = buf.length\n        } else {\n          if (chunk.length > buf.length - bytesUsed) {\n            newBufferSize = (buf.length * 2) + chunk.length\n            if (newBufferSize > maxBufferAheadAllocation) {\n              newBufferSize = buf.length + chunk.length + maxBufferAheadAllocation\n            }\n            newBuffer = Buffer.alloc(newBufferSize)\n            buf.copy(newBuffer, 0, 0, bytesUsed)\n            buf = newBuffer\n          }\n          chunk.copy(buf, bytesUsed)\n          bytesUsed += chunk.length\n        }\n\n        var pos = 0\n        var length = bytesUsed\n\n        while (pos < length) {\n          if (discardTrailingNewline) {\n            if (buf[pos] === lineFeed) {\n              ++pos\n            }\n            discardTrailingNewline = false\n          }\n\n          var lineLength = -1\n          var fieldLength = startingFieldLength\n          var c\n\n          for (var i = startingPos; lineLength < 0 && i < length; ++i) {\n            c = buf[i]\n            if (c === colon) {\n              if (fieldLength < 0) {\n                fieldLength = i - pos\n              }\n            } else if (c === carriageReturn) {\n              discardTrailingNewline = true\n              lineLength = i - pos\n            } else if (c === lineFeed) {\n              lineLength = i - pos\n            }\n          }\n\n          if (lineLength < 0) {\n            startingPos = length - pos\n            startingFieldLength = fieldLength\n            break\n          } else {\n            startingPos = 0\n            startingFieldLength = -1\n          }\n\n          parseEventStreamLine(buf, pos, fieldLength, lineLength)\n\n          pos += lineLength + 1\n        }\n\n        if (pos === length) {\n          buf = void 0\n          bytesUsed = 0\n        } else if (pos > 0) {\n          buf = buf.slice(pos, bytesUsed)\n          bytesUsed = buf.length\n        }\n      })\n    })\n\n    req.on('error', function (err) {\n      self.connectionInProgress = false\n      onConnectionClosed(err.message)\n    })\n\n    if (req.setNoDelay) req.setNoDelay(true)\n    req.end()\n  }\n\n  connect()\n\n  function _emit () {\n    if (self.listeners(arguments[0]).length > 0) {\n      self.emit.apply(self, arguments)\n    }\n  }\n\n  this._close = function () {\n    if (readyState === EventSource.CLOSED) return\n    readyState = EventSource.CLOSED\n    if (req.abort) req.abort()\n    if (req.xhr && req.xhr.abort) req.xhr.abort()\n  }\n\n  function parseEventStreamLine (buf, pos, fieldLength, lineLength) {\n    if (lineLength === 0) {\n      if (data.length > 0) {\n        var type = eventName || 'message'\n        _emit(type, new MessageEvent(type, {\n          data: data.slice(0, -1), // remove trailing newline\n          lastEventId: lastEventId,\n          origin: new URL(url).origin\n        }))\n        data = ''\n      }\n      eventName = void 0\n    } else if (fieldLength > 0) {\n      var noValue = fieldLength < 0\n      var step = 0\n      var field = buf.slice(pos, pos + (noValue ? lineLength : fieldLength)).toString()\n\n      if (noValue) {\n        step = lineLength\n      } else if (buf[pos + fieldLength + 1] !== space) {\n        step = fieldLength + 1\n      } else {\n        step = fieldLength + 2\n      }\n      pos += step\n\n      var valueLength = lineLength - step\n      var value = buf.slice(pos, pos + valueLength).toString()\n\n      if (field === 'data') {\n        data += value + '\\n'\n      } else if (field === 'event') {\n        eventName = value\n      } else if (field === 'id') {\n        lastEventId = value\n      } else if (field === 'retry') {\n        var retry = parseInt(value, 10)\n        if (!Number.isNaN(retry)) {\n          self.reconnectInterval = retry\n        }\n      }\n    }\n  }\n}\n\nmodule.exports = EventSource\n\nutil.inherits(EventSource, events.EventEmitter)\nEventSource.prototype.constructor = EventSource; // make stacktraces readable\n\n['open', 'error', 'message'].forEach(function (method) {\n  Object.defineProperty(EventSource.prototype, 'on' + method, {\n    /**\n     * Returns the current listener\n     *\n     * @return {Mixed} the set function or undefined\n     * @api private\n     */\n    get: function get () {\n      var listener = this.listeners(method)[0]\n      return listener ? (listener._listener ? listener._listener : listener) : undefined\n    },\n\n    /**\n     * Start listening for events\n     *\n     * @param {Function} listener the listener\n     * @return {Mixed} the set function or undefined\n     * @api private\n     */\n    set: function set (listener) {\n      this.removeAllListeners(method)\n      this.addEventListener(method, listener)\n    }\n  })\n})\n\n/**\n * Ready states\n */\nObject.defineProperty(EventSource, 'CONNECTING', {enumerable: true, value: 0})\nObject.defineProperty(EventSource, 'OPEN', {enumerable: true, value: 1})\nObject.defineProperty(EventSource, 'CLOSED', {enumerable: true, value: 2})\n\nEventSource.prototype.CONNECTING = 0\nEventSource.prototype.OPEN = 1\nEventSource.prototype.CLOSED = 2\n\n/**\n * Closes the connection, if one is made, and sets the readyState attribute to 2 (closed)\n *\n * @see https://developer.mozilla.org/en-US/docs/Web/API/EventSource/close\n * @api public\n */\nEventSource.prototype.close = function () {\n  this._close()\n}\n\n/**\n * Emulates the W3C Browser based WebSocket interface using addEventListener.\n *\n * @param {String} type A string representing the event type to listen out for\n * @param {Function} listener callback\n * @see https://developer.mozilla.org/en/DOM/element.addEventListener\n * @see http://dev.w3.org/html5/websockets/#the-websocket-interface\n * @api public\n */\nEventSource.prototype.addEventListener = function addEventListener (type, listener) {\n  if (typeof listener === 'function') {\n    // store a reference so we can return the original function again\n    listener._listener = listener\n    this.on(type, listener)\n  }\n}\n\n/**\n * Emulates the W3C Browser based WebSocket interface using dispatchEvent.\n *\n * @param {Event} event An event to be dispatched\n * @see https://developer.mozilla.org/en-US/docs/Web/API/EventTarget/dispatchEvent\n * @api public\n */\nEventSource.prototype.dispatchEvent = function dispatchEvent (event) {\n  if (!event.type) {\n    throw new Error('UNSPECIFIED_EVENT_TYPE_ERR')\n  }\n  // if event is instance of an CustomEvent (or has 'details' property),\n  // send the detail object as the payload for the event\n  this.emit(event.type, event.detail)\n}\n\n/**\n * Emulates the W3C Browser based WebSocket interface using removeEventListener.\n *\n * @param {String} type A string representing the event type to remove\n * @param {Function} listener callback\n * @see https://developer.mozilla.org/en/DOM/element.removeEventListener\n * @see http://dev.w3.org/html5/websockets/#the-websocket-interface\n * @api public\n */\nEventSource.prototype.removeEventListener = function removeEventListener (type, listener) {\n  if (typeof listener === 'function') {\n    listener._listener = undefined\n    this.removeListener(type, listener)\n  }\n}\n\n/**\n * W3C Event\n *\n * @see http://www.w3.org/TR/DOM-Level-3-Events/#interface-Event\n * @api private\n */\nfunction Event (type, optionalProperties) {\n  Object.defineProperty(this, 'type', { writable: false, value: type, enumerable: true })\n  if (optionalProperties) {\n    for (var f in optionalProperties) {\n      if (optionalProperties.hasOwnProperty(f)) {\n        Object.defineProperty(this, f, { writable: false, value: optionalProperties[f], enumerable: true })\n      }\n    }\n  }\n}\n\n/**\n * W3C MessageEvent\n *\n * @see http://www.w3.org/TR/webmessaging/#event-definitions\n * @api private\n */\nfunction MessageEvent (type, eventInitDict) {\n  Object.defineProperty(this, 'type', { writable: false, value: type, enumerable: true })\n  for (var f in eventInitDict) {\n    if (eventInitDict.hasOwnProperty(f)) {\n      Object.defineProperty(this, f, { writable: false, value: eventInitDict[f], enumerable: true })\n    }\n  }\n}\n\n/**\n * Returns a new object of headers that does not include any authorization and cookie headers\n *\n * @param {Object} headers An object of headers ({[headerName]: headerValue})\n * @return {Object} a new object of headers\n * @api private\n */\nfunction removeUnsafeHeaders (headers) {\n  var safe = {}\n  for (var key in headers) {\n    if (reUnsafeHeader.test(key)) {\n      continue\n    }\n\n    safe[key] = headers[key]\n  }\n\n  return safe\n}\n", "module.exports = require('eventsource')\n"], "names": ["i", "node", "require$$0"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAAA,MAAI,QAAQ,WAAe;AAC3B,MAAI,SAAS;AACb,MAAI,QAAQ;AACZ,MAAI,OAAO;AACX,MAAI,OAAO;AAEX,MAAI,eAAe;AAAA,IACjB;AAAA,IAAO;AAAA,IAAO;AAAA,IAAc;AAAA,IAAQ;AAAA,IAAM;AAAA,IAC1C;AAAA,IAAsB;AAAA,IAAkB;AAAA,IAAc;AAAA,EACxD;AAEA,MAAI,MAAM,CAAC,KAAK,KAAK,GAAG;AACxB,MAAI,QAAQ;AACZ,MAAI,QAAQ;AACZ,MAAI,WAAW;AACf,MAAI,iBAAiB;AAErB,MAAI,2BAA2B,OAAO;AAEtC,MAAI,iBAAiB;AAErB,WAAS,OAAQ,KAAK;AACpB,WAAO,IAAI,MAAM,SAAU,UAAU,OAAO;AAC1C,aAAO,IAAI,KAAK,MAAM;AAAA,IACvB,CAAA;AAAA,EACH;AASA,WAAS,YAAa,KAAK,qBAAqB;AAC9C,QAAI,aAAa,YAAY;AAC7B,QAAI,UAAU,uBAAuB,oBAAoB;AACzD,QAAI,eAAe;AACnB,WAAO,eAAe,MAAM,cAAc;AAAA,MACxC,KAAK,WAAY;AACf,eAAO;AAAA,MACb;AAAA,IACG,CAAA;AAED,WAAO,eAAe,MAAM,OAAO;AAAA,MACjC,KAAK,WAAY;AACf,eAAO;AAAA,MACb;AAAA,IACG,CAAA;AAED,QAAI,OAAO;AACX,SAAK,oBAAoB;AACzB,SAAK,uBAAuB;AAE5B,aAAS,mBAAoB,SAAS;AACpC,UAAI,eAAe,YAAY,OAAQ;AACvC,mBAAa,YAAY;AACzB,YAAM,SAAS,IAAI,MAAM,SAAS,EAAC,QAAgB,CAAC,CAAC;AAIrD,UAAI,cAAc;AAChB,cAAM;AACN,uBAAe;AACf,uBAAe;AAAA,MACrB;AACI,iBAAW,WAAY;AACrB,YAAI,eAAe,YAAY,cAAc,KAAK,sBAAsB;AACtE;AAAA,QACR;AACM,aAAK,uBAAuB;AAC5B,gBAAO;AAAA,MACR,GAAE,KAAK,iBAAiB;AAAA,IAC7B;AAEE,QAAI;AACJ,QAAI,cAAc;AAClB,QAAI,WAAW,QAAQ,eAAe,GAAG;AACvC,oBAAc,QAAQ,eAAe;AACrC,aAAO,QAAQ,eAAe;AAAA,IAClC;AAEE,QAAI,yBAAyB;AAC7B,QAAI,OAAO;AACX,QAAI,YAAY;AAEhB,QAAI,eAAe;AAEnB,aAAS,UAAW;AAClB,UAAI,UAAU,MAAM,GAAG;AACvB,UAAI,WAAW,QAAQ,aAAa;AACpC,cAAQ,UAAU,EAAE,iBAAiB,YAAY,UAAU,oBAAmB;AAC9E,UAAI,YAAa,SAAQ,QAAQ,eAAe,IAAI;AACpD,UAAI,SAAS;AACX,YAAI,aAAa,eAAe,oBAAoB,OAAO,IAAI;AAC/D,iBAAS,KAAK,YAAY;AACxB,cAAI,SAAS,WAAW,CAAC;AACzB,cAAI,QAAQ;AACV,oBAAQ,QAAQ,CAAC,IAAI;AAAA,UAC/B;AAAA,QACA;AAAA,MACA;AAII,cAAQ,qBAAqB,EAAE,uBAAuB,CAAC,oBAAoB;AAE3E,UAAI,uBAAuB,oBAAoB,qBAAqB,QAAW;AAC7E,gBAAQ,mBAAmB,oBAAoB;AAAA,MACrD;AAII,UAAI,WAAW,uBAAuB,oBAAoB;AAC1D,UAAI,UAAU;AACZ,YAAI,QAAQ,MAAM,oBAAoB,KAAK;AAC3C,mBAAW,MAAM,aAAa;AAE9B,gBAAQ,WAAW,WAAW,WAAW;AACzC,gBAAQ,OAAO;AACf,gBAAQ,QAAQ,OAAO,QAAQ;AAC/B,gBAAQ,WAAW,MAAM;AACzB,gBAAQ,OAAO,MAAM;AACrB,gBAAQ,OAAO,MAAM;AAAA,MAC3B;AAGI,UAAI,uBAAuB,oBAAoB,OAAO;AACpD,iBAAS,WAAW,oBAAoB,OAAO;AAC7C,cAAI,aAAa,QAAQ,OAAO,MAAM,IAAI;AACxC;AAAA,UACV;AAEQ,cAAI,SAAS,oBAAoB,MAAM,OAAO;AAC9C,cAAI,WAAW,QAAW;AACxB,oBAAQ,OAAO,IAAI;AAAA,UAC7B;AAAA,QACA;AAAA,MACA;AAGI,UAAI,uBAAuB,oBAAoB,oBAAoB,QAAW;AAC5E,gBAAQ,kBAAkB,oBAAoB;AAAA,MACpD;AAEI,aAAO,WAAW,QAAQ,MAAM,QAAQ,SAAS,SAAU,KAAK;AAC9D,aAAK,uBAAuB;AAE5B,YAAI,IAAI,eAAe,OAAO,IAAI,eAAe,OAAO,IAAI,eAAe,OAAO,IAAI,eAAe,KAAK;AACxG,gBAAM,SAAS,IAAI,MAAM,SAAS,EAAC,QAAQ,IAAI,YAAY,SAAS,IAAI,cAAa,CAAC,CAAC;AACvF,6BAAkB;AAClB;AAAA,QACR;AAGM,YAAI,IAAI,eAAe,OAAO,IAAI,eAAe,OAAO,IAAI,eAAe,KAAK;AAC9E,cAAI,WAAW,IAAI,QAAQ;AAC3B,cAAI,CAAC,UAAU;AAEb,kBAAM,SAAS,IAAI,MAAM,SAAS,EAAC,QAAQ,IAAI,YAAY,SAAS,IAAI,cAAa,CAAC,CAAC;AACvF;AAAA,UACV;AACQ,cAAI,aAAa,IAAI,IAAI,GAAG,EAAE;AAC9B,cAAI,aAAa,IAAI,IAAI,QAAQ,EAAE;AACnC,yBAAe,eAAe;AAC9B,cAAI,IAAI,eAAe,IAAK,gBAAe;AAC3C,gBAAM;AACN,kBAAQ,SAAS,OAAO;AACxB;AAAA,QACR;AAEM,YAAI,IAAI,eAAe,KAAK;AAC1B,gBAAM,SAAS,IAAI,MAAM,SAAS,EAAC,QAAQ,IAAI,YAAY,SAAS,IAAI,cAAa,CAAC,CAAC;AACvF,iBAAO,KAAK,MAAK;AAAA,QACzB;AAEM,qBAAa,YAAY;AACzB,YAAI,GAAG,SAAS,WAAY;AAC1B,cAAI,mBAAmB,OAAO;AAC9B,cAAI,mBAAmB,KAAK;AAC5B,6BAAkB;AAAA,QACnB,CAAA;AAED,YAAI,GAAG,OAAO,WAAY;AACxB,cAAI,mBAAmB,OAAO;AAC9B,cAAI,mBAAmB,KAAK;AAC5B,6BAAkB;AAAA,QACnB,CAAA;AACD,cAAM,QAAQ,IAAI,MAAM,MAAM,CAAC;AAI/B,YAAI;AACJ,YAAI;AACJ,YAAI,cAAc;AAClB,YAAI,sBAAsB;AAC1B,YAAI,gBAAgB;AACpB,YAAI,YAAY;AAEhB,YAAI,GAAG,QAAQ,SAAU,OAAO;AAC9B,cAAI,CAAC,KAAK;AACR,kBAAM;AACN,gBAAI,OAAO,GAAG,GAAG;AACf,oBAAM,IAAI,MAAM,IAAI,MAAM;AAAA,YACtC;AACU,wBAAY,IAAI;AAAA,UAC1B,OAAe;AACL,gBAAI,MAAM,SAAS,IAAI,SAAS,WAAW;AACzC,8BAAiB,IAAI,SAAS,IAAK,MAAM;AACzC,kBAAI,gBAAgB,0BAA0B;AAC5C,gCAAgB,IAAI,SAAS,MAAM,SAAS;AAAA,cAC1D;AACY,0BAAY,OAAO,MAAM,aAAa;AACtC,kBAAI,KAAK,WAAW,GAAG,GAAG,SAAS;AACnC,oBAAM;AAAA,YAClB;AACU,kBAAM,KAAK,KAAK,SAAS;AACzB,yBAAa,MAAM;AAAA,UAC7B;AAEQ,cAAI,MAAM;AACV,cAAI,SAAS;AAEb,iBAAO,MAAM,QAAQ;AACnB,gBAAI,wBAAwB;AAC1B,kBAAI,IAAI,GAAG,MAAM,UAAU;AACzB,kBAAE;AAAA,cAChB;AACY,uCAAyB;AAAA,YACrC;AAEU,gBAAI,aAAa;AACjB,gBAAI,cAAc;AAClB,gBAAI;AAEJ,qBAASA,KAAI,aAAa,aAAa,KAAKA,KAAI,QAAQ,EAAEA,IAAG;AAC3D,kBAAI,IAAIA,EAAC;AACT,kBAAI,MAAM,OAAO;AACf,oBAAI,cAAc,GAAG;AACnB,gCAAcA,KAAI;AAAA,gBAClC;AAAA,cACA,WAAuB,MAAM,gBAAgB;AAC/B,yCAAyB;AACzB,6BAAaA,KAAI;AAAA,cAC/B,WAAuB,MAAM,UAAU;AACzB,6BAAaA,KAAI;AAAA,cAC/B;AAAA,YACA;AAEU,gBAAI,aAAa,GAAG;AAClB,4BAAc,SAAS;AACvB,oCAAsB;AACtB;AAAA,YACZ,OAAiB;AACL,4BAAc;AACd,oCAAsB;AAAA,YAClC;AAEU,iCAAqB,KAAK,KAAK,aAAa,UAAU;AAEtD,mBAAO,aAAa;AAAA,UAC9B;AAEQ,cAAI,QAAQ,QAAQ;AAClB,kBAAM;AACN,wBAAY;AAAA,UACtB,WAAmB,MAAM,GAAG;AAClB,kBAAM,IAAI,MAAM,KAAK,SAAS;AAC9B,wBAAY,IAAI;AAAA,UAC1B;AAAA,QACO,CAAA;AAAA,MACF,CAAA;AAED,UAAI,GAAG,SAAS,SAAU,KAAK;AAC7B,aAAK,uBAAuB;AAC5B,2BAAmB,IAAI,OAAO;AAAA,MAC/B,CAAA;AAED,UAAI,IAAI,WAAY,KAAI,WAAW,IAAI;AACvC,UAAI,IAAG;AAAA,IACX;AAEE,YAAO;AAEP,aAAS,QAAS;AAChB,UAAI,KAAK,UAAU,UAAU,CAAC,CAAC,EAAE,SAAS,GAAG;AAC3C,aAAK,KAAK,MAAM,MAAM,SAAS;AAAA,MACrC;AAAA,IACA;AAEE,SAAK,SAAS,WAAY;AACxB,UAAI,eAAe,YAAY,OAAQ;AACvC,mBAAa,YAAY;AACzB,UAAI,IAAI,MAAO,KAAI,MAAK;AACxB,UAAI,IAAI,OAAO,IAAI,IAAI,MAAO,KAAI,IAAI,MAAK;AAAA,IAC/C;AAEE,aAAS,qBAAsB,KAAK,KAAK,aAAa,YAAY;AAChE,UAAI,eAAe,GAAG;AACpB,YAAI,KAAK,SAAS,GAAG;AACnB,cAAI,OAAO,aAAa;AACxB,gBAAM,MAAM,IAAI,aAAa,MAAM;AAAA,YACjC,MAAM,KAAK,MAAM,GAAG,EAAE;AAAA;AAAA,YACtB;AAAA,YACA,QAAQ,IAAI,IAAI,GAAG,EAAE;AAAA,UAC/B,CAAS,CAAC;AACF,iBAAO;AAAA,QACf;AACM,oBAAY;AAAA,MAClB,WAAe,cAAc,GAAG;AAC1B,YAAI,UAAU,cAAc;AAC5B,YAAI,OAAO;AACX,YAAI,QAAQ,IAAI,MAAM,KAAK,OAAO,UAAU,aAAa,YAAY,EAAE,SAAQ;AAE/E,YAAI,SAAS;AACX,iBAAO;AAAA,QACf,WAAiB,IAAI,MAAM,cAAc,CAAC,MAAM,OAAO;AAC/C,iBAAO,cAAc;AAAA,QAC7B,OAAa;AACL,iBAAO,cAAc;AAAA,QAC7B;AACM,eAAO;AAEP,YAAI,cAAc,aAAa;AAC/B,YAAI,QAAQ,IAAI,MAAM,KAAK,MAAM,WAAW,EAAE,SAAQ;AAEtD,YAAI,UAAU,QAAQ;AACpB,kBAAQ,QAAQ;AAAA,QACxB,WAAiB,UAAU,SAAS;AAC5B,sBAAY;AAAA,QACpB,WAAiB,UAAU,MAAM;AACzB,wBAAc;AAAA,QACtB,WAAiB,UAAU,SAAS;AAC5B,cAAI,QAAQ,SAAS,OAAO,EAAE;AAC9B,cAAI,CAAC,OAAO,MAAM,KAAK,GAAG;AACxB,iBAAK,oBAAoB;AAAA,UACnC;AAAA,QACA;AAAA,MACA;AAAA,IACA;AAAA,EACA;AAEA,gBAAiB;AAEjB,OAAK,SAAS,aAAa,OAAO,YAAY;AAC9C,cAAY,UAAU,cAAc;AAEpC,GAAC,QAAQ,SAAS,SAAS,EAAE,QAAQ,SAAU,QAAQ;AACrD,WAAO,eAAe,YAAY,WAAW,OAAO,QAAQ;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,MAO1D,KAAK,SAAS,MAAO;AACnB,YAAI,WAAW,KAAK,UAAU,MAAM,EAAE,CAAC;AACvC,eAAO,WAAY,SAAS,YAAY,SAAS,YAAY,WAAY;AAAA,MAC1E;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,MASD,KAAK,SAAS,IAAK,UAAU;AAC3B,aAAK,mBAAmB,MAAM;AAC9B,aAAK,iBAAiB,QAAQ,QAAQ;AAAA,MAC5C;AAAA,IACG,CAAA;AAAA,EACF,CAAA;AAKD,SAAO,eAAe,aAAa,cAAc,EAAC,YAAY,MAAM,OAAO,EAAC,CAAC;AAC7E,SAAO,eAAe,aAAa,QAAQ,EAAC,YAAY,MAAM,OAAO,EAAC,CAAC;AACvE,SAAO,eAAe,aAAa,UAAU,EAAC,YAAY,MAAM,OAAO,EAAC,CAAC;AAEzE,cAAY,UAAU,aAAa;AACnC,cAAY,UAAU,OAAO;AAC7B,cAAY,UAAU,SAAS;AAQ/B,cAAY,UAAU,QAAQ,WAAY;AACxC,SAAK,OAAM;AAAA,EACb;AAWA,cAAY,UAAU,mBAAmB,SAAS,iBAAkB,MAAM,UAAU;AAClF,QAAI,OAAO,aAAa,YAAY;AAElC,eAAS,YAAY;AACrB,WAAK,GAAG,MAAM,QAAQ;AAAA,IAC1B;AAAA,EACA;AASA,cAAY,UAAU,gBAAgB,SAAS,cAAe,OAAO;AACnE,QAAI,CAAC,MAAM,MAAM;AACf,YAAM,IAAI,MAAM,4BAA4B;AAAA,IAChD;AAGE,SAAK,KAAK,MAAM,MAAM,MAAM,MAAM;AAAA,EACpC;AAWA,cAAY,UAAU,sBAAsB,SAAS,oBAAqB,MAAM,UAAU;AACxF,QAAI,OAAO,aAAa,YAAY;AAClC,eAAS,YAAY;AACrB,WAAK,eAAe,MAAM,QAAQ;AAAA,IACtC;AAAA,EACA;AAQA,WAAS,MAAO,MAAM,oBAAoB;AACxC,WAAO,eAAe,MAAM,QAAQ,EAAE,UAAU,OAAO,OAAO,MAAM,YAAY,KAAM,CAAA;AACtF,QAAI,oBAAoB;AACtB,eAAS,KAAK,oBAAoB;AAChC,YAAI,mBAAmB,eAAe,CAAC,GAAG;AACxC,iBAAO,eAAe,MAAM,GAAG,EAAE,UAAU,OAAO,OAAO,mBAAmB,CAAC,GAAG,YAAY,KAAM,CAAA;AAAA,QAC1G;AAAA,MACA;AAAA,IACA;AAAA,EACA;AAQA,WAAS,aAAc,MAAM,eAAe;AAC1C,WAAO,eAAe,MAAM,QAAQ,EAAE,UAAU,OAAO,OAAO,MAAM,YAAY,KAAM,CAAA;AACtF,aAAS,KAAK,eAAe;AAC3B,UAAI,cAAc,eAAe,CAAC,GAAG;AACnC,eAAO,eAAe,MAAM,GAAG,EAAE,UAAU,OAAO,OAAO,cAAc,CAAC,GAAG,YAAY,KAAM,CAAA;AAAA,MACnG;AAAA,IACA;AAAA,EACA;AASA,WAAS,oBAAqB,SAAS;AACrC,QAAI,OAAO,CAAA;AACX,aAAS,OAAO,SAAS;AACvB,UAAI,eAAe,KAAK,GAAG,GAAG;AAC5B;AAAA,MACN;AAEI,WAAK,GAAG,IAAI,QAAQ,GAAG;AAAA,IAC3B;AAEE,WAAO;AAAA,EACT;;;;;;;;AC9eAC,WAAiBC,mBAAA;;;;;;;;;", "x_google_ignoreList": [0, 1]}