{"version": 3, "file": "index.js", "sources": ["..\\..\\node_modules\\@sanity\\functions\\dist\\definers.js", "..\\..\\node_modules\\get-it\\dist\\_chunks-es\\_commonjsHelpers.js", "..\\..\\node_modules\\get-it\\dist\\index.browser.js", "..\\..\\node_modules\\get-it\\dist\\middleware.browser.js", "..\\..\\node_modules\\tslib\\tslib.es6.mjs", "..\\..\\node_modules\\rxjs\\dist\\esm5\\internal\\util\\isFunction.js", "..\\..\\node_modules\\rxjs\\dist\\esm5\\internal\\util\\createErrorClass.js", "..\\..\\node_modules\\rxjs\\dist\\esm5\\internal\\util\\UnsubscriptionError.js", "..\\..\\node_modules\\rxjs\\dist\\esm5\\internal\\util\\arrRemove.js", "..\\..\\node_modules\\rxjs\\dist\\esm5\\internal\\Subscription.js", "..\\..\\node_modules\\rxjs\\dist\\esm5\\internal\\config.js", "..\\..\\node_modules\\rxjs\\dist\\esm5\\internal\\scheduler\\timeoutProvider.js", "..\\..\\node_modules\\rxjs\\dist\\esm5\\internal\\util\\reportUnhandledError.js", "..\\..\\node_modules\\rxjs\\dist\\esm5\\internal\\util\\noop.js", "..\\..\\node_modules\\rxjs\\dist\\esm5\\internal\\util\\errorContext.js", "..\\..\\node_modules\\rxjs\\dist\\esm5\\internal\\Subscriber.js", "..\\..\\node_modules\\rxjs\\dist\\esm5\\internal\\symbol\\observable.js", "..\\..\\node_modules\\rxjs\\dist\\esm5\\internal\\util\\identity.js", "..\\..\\node_modules\\rxjs\\dist\\esm5\\internal\\util\\pipe.js", "..\\..\\node_modules\\rxjs\\dist\\esm5\\internal\\Observable.js", "..\\..\\node_modules\\rxjs\\dist\\esm5\\internal\\util\\lift.js", "..\\..\\node_modules\\rxjs\\dist\\esm5\\internal\\operators\\OperatorSubscriber.js", "..\\..\\node_modules\\rxjs\\dist\\esm5\\internal\\util\\ObjectUnsubscribedError.js", "..\\..\\node_modules\\rxjs\\dist\\esm5\\internal\\Subject.js", "..\\..\\node_modules\\rxjs\\dist\\esm5\\internal\\scheduler\\dateTimestampProvider.js", "..\\..\\node_modules\\rxjs\\dist\\esm5\\internal\\ReplaySubject.js", "..\\..\\node_modules\\rxjs\\dist\\esm5\\internal\\scheduler\\Action.js", "..\\..\\node_modules\\rxjs\\dist\\esm5\\internal\\scheduler\\intervalProvider.js", "..\\..\\node_modules\\rxjs\\dist\\esm5\\internal\\scheduler\\AsyncAction.js", "..\\..\\node_modules\\rxjs\\dist\\esm5\\internal\\Scheduler.js", "..\\..\\node_modules\\rxjs\\dist\\esm5\\internal\\scheduler\\AsyncScheduler.js", "..\\..\\node_modules\\rxjs\\dist\\esm5\\internal\\scheduler\\async.js", "..\\..\\node_modules\\rxjs\\dist\\esm5\\internal\\observable\\empty.js", "..\\..\\node_modules\\rxjs\\dist\\esm5\\internal\\util\\isScheduler.js", "..\\..\\node_modules\\rxjs\\dist\\esm5\\internal\\util\\args.js", "..\\..\\node_modules\\rxjs\\dist\\esm5\\internal\\util\\isArrayLike.js", "..\\..\\node_modules\\rxjs\\dist\\esm5\\internal\\util\\isPromise.js", "..\\..\\node_modules\\rxjs\\dist\\esm5\\internal\\util\\isInteropObservable.js", "..\\..\\node_modules\\rxjs\\dist\\esm5\\internal\\util\\isAsyncIterable.js", "..\\..\\node_modules\\rxjs\\dist\\esm5\\internal\\util\\throwUnobservableError.js", "..\\..\\node_modules\\rxjs\\dist\\esm5\\internal\\symbol\\iterator.js", "..\\..\\node_modules\\rxjs\\dist\\esm5\\internal\\util\\isIterable.js", "..\\..\\node_modules\\rxjs\\dist\\esm5\\internal\\util\\isReadableStreamLike.js", "..\\..\\node_modules\\rxjs\\dist\\esm5\\internal\\observable\\innerFrom.js", "..\\..\\node_modules\\rxjs\\dist\\esm5\\internal\\util\\executeSchedule.js", "..\\..\\node_modules\\rxjs\\dist\\esm5\\internal\\operators\\observeOn.js", "..\\..\\node_modules\\rxjs\\dist\\esm5\\internal\\operators\\subscribeOn.js", "..\\..\\node_modules\\rxjs\\dist\\esm5\\internal\\scheduled\\scheduleObservable.js", "..\\..\\node_modules\\rxjs\\dist\\esm5\\internal\\scheduled\\schedulePromise.js", "..\\..\\node_modules\\rxjs\\dist\\esm5\\internal\\scheduled\\scheduleArray.js", "..\\..\\node_modules\\rxjs\\dist\\esm5\\internal\\scheduled\\scheduleIterable.js", "..\\..\\node_modules\\rxjs\\dist\\esm5\\internal\\scheduled\\scheduleAsyncIterable.js", "..\\..\\node_modules\\rxjs\\dist\\esm5\\internal\\scheduled\\scheduleReadableStreamLike.js", "..\\..\\node_modules\\rxjs\\dist\\esm5\\internal\\scheduled\\scheduled.js", "..\\..\\node_modules\\rxjs\\dist\\esm5\\internal\\observable\\from.js", "..\\..\\node_modules\\rxjs\\dist\\esm5\\internal\\observable\\of.js", "..\\..\\node_modules\\rxjs\\dist\\esm5\\internal\\observable\\throwError.js", "..\\..\\node_modules\\rxjs\\dist\\esm5\\internal\\util\\isObservable.js", "..\\..\\node_modules\\rxjs\\dist\\esm5\\internal\\util\\EmptyError.js", "..\\..\\node_modules\\rxjs\\dist\\esm5\\internal\\lastValueFrom.js", "..\\..\\node_modules\\rxjs\\dist\\esm5\\internal\\firstValueFrom.js", "..\\..\\node_modules\\rxjs\\dist\\esm5\\internal\\util\\isDate.js", "..\\..\\node_modules\\rxjs\\dist\\esm5\\internal\\operators\\map.js", "..\\..\\node_modules\\rxjs\\dist\\esm5\\internal\\util\\mapOneOrManyArgs.js", "..\\..\\node_modules\\rxjs\\dist\\esm5\\internal\\observable\\combineLatest.js", "..\\..\\node_modules\\rxjs\\dist\\esm5\\internal\\operators\\mergeInternals.js", "..\\..\\node_modules\\rxjs\\dist\\esm5\\internal\\operators\\mergeMap.js", "..\\..\\node_modules\\rxjs\\dist\\esm5\\internal\\operators\\mergeAll.js", "..\\..\\node_modules\\rxjs\\dist\\esm5\\internal\\operators\\concatAll.js", "..\\..\\node_modules\\rxjs\\dist\\esm5\\internal\\observable\\concat.js", "..\\..\\node_modules\\rxjs\\dist\\esm5\\internal\\observable\\defer.js", "..\\..\\node_modules\\rxjs\\dist\\esm5\\internal\\observable\\timer.js", "..\\..\\node_modules\\rxjs\\dist\\esm5\\internal\\observable\\merge.js", "..\\..\\node_modules\\rxjs\\dist\\esm5\\internal\\util\\argsOrArgArray.js", "..\\..\\node_modules\\rxjs\\dist\\esm5\\internal\\operators\\filter.js", "..\\..\\node_modules\\rxjs\\dist\\esm5\\internal\\operators\\catchError.js", "..\\..\\node_modules\\rxjs\\dist\\esm5\\internal\\operators\\combineLatest.js", "..\\..\\node_modules\\rxjs\\dist\\esm5\\internal\\operators\\combineLatestWith.js", "..\\..\\node_modules\\rxjs\\dist\\esm5\\internal\\operators\\finalize.js", "..\\..\\node_modules\\rxjs\\dist\\esm5\\internal\\operators\\share.js", "..\\..\\node_modules\\rxjs\\dist\\esm5\\internal\\operators\\shareReplay.js", "..\\..\\node_modules\\rxjs\\dist\\esm5\\internal\\operators\\tap.js", "..\\..\\node_modules\\@sanity\\client\\dist\\_chunks-es\\stegaClean.js", "..\\..\\node_modules\\@sanity\\client\\dist\\_chunks-es\\resolveEditInfo.js", "..\\..\\node_modules\\nanoid\\index.js", "..\\..\\node_modules\\@sanity\\client\\dist\\index.browser.js", "..\\..\\languages\\index.ts", "index.ts"], "sourcesContent": ["/**\n * Defines a \"document event\" function handler.\n * Returns the handler function as-is, only providing the types and doing basic validation.\n *\n * @param handler - The event handler function to use.\n * @returns The handler function, unmodified.\n */\nexport function documentEventHandler(handler) {\n    if (typeof handler !== 'function')\n        throw new TypeError('`handler` must be a function');\n    return handler;\n}\n//# sourceMappingURL=definers.js.map", "const e=!(typeof navigator>\"u\")&&\"ReactNative\"===navigator.product,t={timeout:e?6e4:12e4},r=function(r){const a={...t,...\"string\"==typeof r?{url:r}:r};if(a.timeout=n(a.timeout),a.query){const{url:t,searchParams:r}=function(t){const r=t.indexOf(\"?\");if(-1===r)return{url:t,searchParams:new URLSearchParams};const n=t.slice(0,r),a=t.slice(r+1);if(!e)return{url:n,searchParams:new URLSearchParams(a)};if(\"function\"!=typeof decodeURIComponent)throw new Error(\"Broken `URLSearchParams` implementation, and `decodeURIComponent` is not defined\");const s=new URLSearchParams;for(const e of a.split(\"&\")){const[t,r]=e.split(\"=\");t&&s.append(o(t),o(r||\"\"))}return{url:n,searchParams:s}}(a.url);for(const[e,o]of Object.entries(a.query)){if(void 0!==o)if(Array.isArray(o))for(const t of o)r.append(e,t);else r.append(e,o);const n=r.toString();n&&(a.url=`${t}?${n}`)}}return a.method=a.body&&!a.method?\"POST\":(a.method||\"GET\").toUpperCase(),a};function o(e){return decodeURIComponent(e.replace(/\\+/g,\" \"))}function n(e){if(!1===e||0===e)return!1;if(e.connect||e.socket)return e;const r=Number(e);return isNaN(r)?n(t.timeout):{connect:r,socket:r}}const a=/^https?:\\/\\//i,s=function(e){if(!a.test(e.url))throw new Error(`\"${e.url}\" is not a valid URL`)};function c(e){return e&&e.__esModule&&Object.prototype.hasOwnProperty.call(e,\"default\")?e.default:e}export{c as g,r as p,s as v};//# sourceMappingURL=_commonjsHelpers.js.map\n", "import{v as e,p as t,g as r}from\"./_chunks-es/_commonjsHelpers.js\";const o=[\"request\",\"response\",\"progress\",\"error\",\"abort\"],n=[\"processOptions\",\"validateOptions\",\"interceptRequest\",\"finalizeOptions\",\"onRequest\",\"onResponse\",\"onError\",\"onReturn\",\"onHeaders\"];function s(r,a){const i=[],u=n.reduce(((e,t)=>(e[t]=e[t]||[],e)),{processOptions:[t],validateOptions:[e]});function l(e){const t=o.reduce(((e,t)=>(e[t]=function(){const e=/* @__PURE__ */Object.create(null);let t=0;return{publish:function(t){for(const r in e)e[r](t)},subscribe:function(r){const o=t++;return e[o]=r,function(){delete e[o]}}}}(),e)),{}),r=(e=>function(t,r,...o){const n=\"onError\"===t;let s=r;for(let r=0;r<e[t].length&&(s=(0,e[t][r])(s,...o),!n||s);r++);return s})(u),n=r(\"processOptions\",e);r(\"validateOptions\",n);const s={options:n,channels:t,applyMiddleware:r};let i;const l=t.request.subscribe((e=>{i=a(e,((o,n)=>((e,o,n)=>{let s=e,a=o;if(!s)try{a=r(\"onResponse\",o,n)}catch(e){a=null,s=e}s=s&&r(\"onError\",s,n),s?t.error.publish(s):a&&t.response.publish(a)})(o,n,e)))}));t.abort.subscribe((()=>{l(),i&&i.abort()}));const c=r(\"onReturn\",t,s);return c===t&&t.request.publish(s),c}return l.use=function(e){if(!e)throw new Error(\"Tried to add middleware that resolved to falsey value\");if(\"function\"==typeof e)throw new Error(\"Tried to add middleware that was a function. It probably expects you to pass options to it.\");if(e.onReturn&&u.onReturn.length>0)throw new Error(\"Tried to add new middleware with `onReturn` handler, but another handler has already been registered for this event\");return n.forEach((t=>{e[t]&&u[t].push(e[t])})),i.push(e),l},l.clone=()=>s(i,a),r.forEach(l.use),l}var a,i,u=/* @__PURE__ */r(function(){if(i)return a;i=1;var e=function(e){return e.replace(/^\\s+|\\s+$/g,\"\")};return a=function(t){if(!t)return{};for(var r={},o=e(t).split(\"\\n\"),n=0;n<o.length;n++){var s=o[n],a=s.indexOf(\":\"),i=e(s.slice(0,a)).toLowerCase(),u=e(s.slice(a+1));typeof r[i]>\"u\"?r[i]=u:(l=r[i],\"[object Array]\"===Object.prototype.toString.call(l)?r[i].push(u):r[i]=[r[i],u])}var l;return r}}());class l{onabort;onerror;onreadystatechange;ontimeout;readyState=0;response;responseText=\"\";responseType=\"\";status;statusText;withCredentials;#e;#t;#r;#o={};#n;#s={};#a;open(e,t,r){this.#e=e,this.#t=t,this.#r=\"\",this.readyState=1,this.onreadystatechange?.(),this.#n=void 0}abort(){this.#n&&this.#n.abort()}getAllResponseHeaders(){return this.#r}setRequestHeader(e,t){this.#o[e]=t}setInit(e,t=!0){this.#s=e,this.#a=t}send(e){const t=\"arraybuffer\"!==this.responseType,r={...this.#s,method:this.#e,headers:this.#o,body:e};\"function\"==typeof AbortController&&this.#a&&(this.#n=new AbortController,typeof EventTarget<\"u\"&&this.#n.signal instanceof EventTarget&&(r.signal=this.#n.signal)),typeof document<\"u\"&&(r.credentials=this.withCredentials?\"include\":\"omit\"),fetch(this.#t,r).then((e=>(e.headers.forEach(((e,t)=>{this.#r+=`${t}: ${e}\\r\\n`})),this.status=e.status,this.statusText=e.statusText,this.readyState=3,this.onreadystatechange?.(),t?e.text():e.arrayBuffer()))).then((e=>{\"string\"==typeof e?this.responseText=e:this.response=e,this.readyState=4,this.onreadystatechange?.()})).catch((e=>{\"AbortError\"!==e.name?this.onerror?.(e):this.onabort?.()}))}}const c=\"function\"==typeof XMLHttpRequest?\"xhr\":\"fetch\",h=\"xhr\"===c?XMLHttpRequest:l,d=(e,t)=>{const r=e.options,o=e.applyMiddleware(\"finalizeOptions\",r),n={},s=e.applyMiddleware(\"interceptRequest\",void 0,{adapter:c,context:e});if(s){const e=setTimeout(t,0,null,s);return{abort:()=>clearTimeout(e)}}let a=new h;a instanceof l&&\"object\"==typeof o.fetch&&a.setInit(o.fetch,o.useAbortSignal??!0);const i=o.headers,d=o.timeout;let p=!1,f=!1,b=!1;if(a.onerror=e=>{m(a instanceof l?e instanceof Error?e:new Error(`Request error while attempting to reach is ${o.url}`,{cause:e}):new Error(`Request error while attempting to reach is ${o.url}${e.lengthComputable?`(${e.loaded} of ${e.total} bytes transferred)`:\"\"}`))},a.ontimeout=e=>{m(new Error(`Request timeout while attempting to reach ${o.url}${e.lengthComputable?`(${e.loaded} of ${e.total} bytes transferred)`:\"\"}`))},a.onabort=()=>{w(!0),p=!0},a.onreadystatechange=function(){d&&(w(),n.socket=setTimeout((()=>y(\"ESOCKETTIMEDOUT\")),d.socket)),!p&&a&&4===a.readyState&&0!==a.status&&function(){if(!(p||f||b)){if(0===a.status)return void m(new Error(\"Unknown XHR error\"));w(),f=!0,t(null,{body:a.response||(\"\"===a.responseType||\"text\"===a.responseType?a.responseText:\"\"),url:o.url,method:o.method,headers:u(a.getAllResponseHeaders()),statusCode:a.status,statusMessage:a.statusText})}}()},a.open(o.method,o.url,!0),a.withCredentials=!!o.withCredentials,i&&a.setRequestHeader)for(const e in i)i.hasOwnProperty(e)&&a.setRequestHeader(e,i[e]);return o.rawBody&&(a.responseType=\"arraybuffer\"),e.applyMiddleware(\"onRequest\",{options:o,adapter:c,request:a,context:e}),a.send(o.body||null),d&&(n.connect=setTimeout((()=>y(\"ETIMEDOUT\")),d.connect)),{abort:function(){p=!0,a&&a.abort()}};function y(t){b=!0,a.abort();const r=new Error(\"ESOCKETTIMEDOUT\"===t?`Socket timed out on request to ${o.url}`:`Connection timed out on request to ${o.url}`);r.code=t,e.channels.error.publish(r)}function w(e){(e||p||a&&a.readyState>=2&&n.connect)&&clearTimeout(n.connect),n.socket&&clearTimeout(n.socket)}function m(e){if(f)return;w(!0),f=!0,a=null;const r=e||new Error(`Network error while attempting to reach ${o.url}`);r.isNetworkError=!0,r.request=o,t(r)}},p=(e=[],t=d)=>s(e,t),f=\"browser\";export{c as adapter,f as environment,p as getIt};//# sourceMappingURL=index.browser.js.map\n", "import{g as e}from\"./_chunks-es/_commonjsHelpers.js\";import{p as t,v as s}from\"./_chunks-es/_commonjsHelpers.js\";function n(e){return{}}const r=/^\\//,o=/\\/$/;function i(e){const t=e.replace(o,\"\");return{processOptions:e=>{if(/^https?:\\/\\//i.test(e.url))return e;const s=[t,e.url.replace(r,\"\")].join(\"/\");return Object.assign({},e,{url:s})}}}var a,c,u,l,p,d={exports:{}},f=/* @__PURE__ */e((p||(p=1,function(e,t){t.formatArgs=function(t){if(t[0]=(this.useColors?\"%c\":\"\")+this.namespace+(this.useColors?\" %c\":\" \")+t[0]+(this.useColors?\"%c \":\" \")+\"+\"+e.exports.humanize(this.diff),!this.useColors)return;const s=\"color: \"+this.color;t.splice(1,0,s,\"color: inherit\");let n=0,r=0;t[0].replace(/%[a-zA-Z%]/g,(e=>{\"%%\"!==e&&(n++,\"%c\"===e&&(r=n))})),t.splice(r,0,s)},t.save=function(e){try{e?t.storage.setItem(\"debug\",e):t.storage.removeItem(\"debug\")}catch{}},t.load=function(){let e;try{e=t.storage.getItem(\"debug\")}catch{}return!e&&typeof process<\"u\"&&\"env\"in process&&(e=process.env.DEBUG),e},t.useColors=function(){if(typeof window<\"u\"&&window.process&&(\"renderer\"===window.process.type||window.process.__nwjs))return!0;if(typeof navigator<\"u\"&&navigator.userAgent&&navigator.userAgent.toLowerCase().match(/(edge|trident)\\/(\\d+)/))return!1;let e;return typeof document<\"u\"&&document.documentElement&&document.documentElement.style&&document.documentElement.style.WebkitAppearance||typeof window<\"u\"&&window.console&&(window.console.firebug||window.console.exception&&window.console.table)||typeof navigator<\"u\"&&navigator.userAgent&&(e=navigator.userAgent.toLowerCase().match(/firefox\\/(\\d+)/))&&parseInt(e[1],10)>=31||typeof navigator<\"u\"&&navigator.userAgent&&navigator.userAgent.toLowerCase().match(/applewebkit\\/(\\d+)/)},t.storage=function(){try{return localStorage}catch{}}(),t.destroy=/* @__PURE__ */(()=>{let e=!1;return()=>{e||(e=!0,console.warn(\"Instance method `debug.destroy()` is deprecated and no longer does anything. It will be removed in the next major version of `debug`.\"))}})(),t.colors=[\"#0000CC\",\"#0000FF\",\"#0033CC\",\"#0033FF\",\"#0066CC\",\"#0066FF\",\"#0099CC\",\"#0099FF\",\"#00CC00\",\"#00CC33\",\"#00CC66\",\"#00CC99\",\"#00CCCC\",\"#00CCFF\",\"#3300CC\",\"#3300FF\",\"#3333CC\",\"#3333FF\",\"#3366CC\",\"#3366FF\",\"#3399CC\",\"#3399FF\",\"#33CC00\",\"#33CC33\",\"#33CC66\",\"#33CC99\",\"#33CCCC\",\"#33CCFF\",\"#6600CC\",\"#6600FF\",\"#6633CC\",\"#6633FF\",\"#66CC00\",\"#66CC33\",\"#9900CC\",\"#9900FF\",\"#9933CC\",\"#9933FF\",\"#99CC00\",\"#99CC33\",\"#CC0000\",\"#CC0033\",\"#CC0066\",\"#CC0099\",\"#CC00CC\",\"#CC00FF\",\"#CC3300\",\"#CC3333\",\"#CC3366\",\"#CC3399\",\"#CC33CC\",\"#CC33FF\",\"#CC6600\",\"#CC6633\",\"#CC9900\",\"#CC9933\",\"#CCCC00\",\"#CCCC33\",\"#FF0000\",\"#FF0033\",\"#FF0066\",\"#FF0099\",\"#FF00CC\",\"#FF00FF\",\"#FF3300\",\"#FF3333\",\"#FF3366\",\"#FF3399\",\"#FF33CC\",\"#FF33FF\",\"#FF6600\",\"#FF6633\",\"#FF9900\",\"#FF9933\",\"#FFCC00\",\"#FFCC33\"],t.log=console.debug||console.log||(()=>{}),e.exports=(l?u:(l=1,u=function(e){function t(e){let n,r,o,i=null;function a(...e){if(!a.enabled)return;const s=a,r=Number(/* @__PURE__ */new Date),o=r-(n||r);s.diff=o,s.prev=n,s.curr=r,n=r,e[0]=t.coerce(e[0]),\"string\"!=typeof e[0]&&e.unshift(\"%O\");let i=0;e[0]=e[0].replace(/%([a-zA-Z%])/g,((n,r)=>{if(\"%%\"===n)return\"%\";i++;const o=t.formatters[r];if(\"function\"==typeof o){const t=e[i];n=o.call(s,t),e.splice(i,1),i--}return n})),t.formatArgs.call(s,e),(s.log||t.log).apply(s,e)}return a.namespace=e,a.useColors=t.useColors(),a.color=t.selectColor(e),a.extend=s,a.destroy=t.destroy,Object.defineProperty(a,\"enabled\",{enumerable:!0,configurable:!1,get:()=>null!==i?i:(r!==t.namespaces&&(r=t.namespaces,o=t.enabled(e)),o),set:e=>{i=e}}),\"function\"==typeof t.init&&t.init(a),a}function s(e,s){const n=t(this.namespace+(typeof s>\"u\"?\":\":s)+e);return n.log=this.log,n}function n(e,t){let s=0,n=0,r=-1,o=0;for(;s<e.length;)if(n<t.length&&(t[n]===e[s]||\"*\"===t[n]))\"*\"===t[n]?(r=n,o=s,n++):(s++,n++);else{if(-1===r)return!1;n=r+1,o++,s=o}for(;n<t.length&&\"*\"===t[n];)n++;return n===t.length}return t.debug=t,t.default=t,t.coerce=function(e){return e instanceof Error?e.stack||e.message:e},t.disable=function(){const e=[...t.names,...t.skips.map((e=>\"-\"+e))].join(\",\");return t.enable(\"\"),e},t.enable=function(e){t.save(e),t.namespaces=e,t.names=[],t.skips=[];const s=(\"string\"==typeof e?e:\"\").trim().replace(\" \",\",\").split(\",\").filter(Boolean);for(const e of s)\"-\"===e[0]?t.skips.push(e.slice(1)):t.names.push(e)},t.enabled=function(e){for(const s of t.skips)if(n(e,s))return!1;for(const s of t.names)if(n(e,s))return!0;return!1},t.humanize=function(){if(c)return a;c=1;var e=1e3,t=60*e,s=60*t,n=24*s,r=7*n;function o(e,t,s,n){var r=t>=1.5*s;return Math.round(e/s)+\" \"+n+(r?\"s\":\"\")}return a=function(i,a){a=a||{};var c,u,l=typeof i;if(\"string\"===l&&i.length>0)return function(o){if(!((o=String(o)).length>100)){var i=/^(-?(?:\\d+)?\\.?\\d+) *(milliseconds?|msecs?|ms|seconds?|secs?|s|minutes?|mins?|m|hours?|hrs?|h|days?|d|weeks?|w|years?|yrs?|y)?$/i.exec(o);if(i){var a=parseFloat(i[1]);switch((i[2]||\"ms\").toLowerCase()){case\"years\":case\"year\":case\"yrs\":case\"yr\":case\"y\":return 315576e5*a;case\"weeks\":case\"week\":case\"w\":return a*r;case\"days\":case\"day\":case\"d\":return a*n;case\"hours\":case\"hour\":case\"hrs\":case\"hr\":case\"h\":return a*s;case\"minutes\":case\"minute\":case\"mins\":case\"min\":case\"m\":return a*t;case\"seconds\":case\"second\":case\"secs\":case\"sec\":case\"s\":return a*e;case\"milliseconds\":case\"millisecond\":case\"msecs\":case\"msec\":case\"ms\":return a;default:return}}}}(i);if(\"number\"===l&&isFinite(i))return a.long?(c=i,(u=Math.abs(c))>=n?o(c,u,n,\"day\"):u>=s?o(c,u,s,\"hour\"):u>=t?o(c,u,t,\"minute\"):u>=e?o(c,u,e,\"second\"):c+\" ms\"):function(r){var o=Math.abs(r);return o>=n?Math.round(r/n)+\"d\":o>=s?Math.round(r/s)+\"h\":o>=t?Math.round(r/t)+\"m\":o>=e?Math.round(r/e)+\"s\":r+\"ms\"}(i);throw new Error(\"val is not a non-empty string or a valid number. val=\"+JSON.stringify(i))}}(),t.destroy=function(){console.warn(\"Instance method `debug.destroy()` is deprecated and no longer does anything. It will be removed in the next major version of `debug`.\")},Object.keys(e).forEach((s=>{t[s]=e[s]})),t.names=[],t.skips=[],t.formatters={},t.selectColor=function(e){let s=0;for(let t=0;t<e.length;t++)s=(s<<5)-s+e.charCodeAt(t),s|=0;return t.colors[Math.abs(s)%t.colors.length]},t.enable(t.load()),t}))(t);const{formatters:s}=e.exports;s.j=function(e){try{return JSON.stringify(e)}catch(e){return\"[UnexpectedJSONParseError]: \"+e.message}}}(d,d.exports)),d.exports));const m=[\"cookie\",\"authorization\"],C=Object.prototype.hasOwnProperty;function h(e={}){const t=e.verbose,s=e.namespace||\"get-it\",n=f(s),r=e.log||n,o=r===n&&!f.enabled(s);let i=0;return{processOptions:e=>(e.debug=r,e.requestId=e.requestId||++i,e),onRequest:s=>{if(o||!s)return s;const n=s.options;if(r(\"[%s] HTTP %s %s\",n.requestId,n.method,n.url),t&&n.body&&\"string\"==typeof n.body&&r(\"[%s] Request body: %s\",n.requestId,n.body),t&&n.headers){const t=!1===e.redactSensitiveHeaders?n.headers:((e,t)=>{const s={};for(const n in e)C.call(e,n)&&(s[n]=t.indexOf(n.toLowerCase())>-1?\"<redacted>\":e[n]);return s})(n.headers,m);r(\"[%s] Request headers: %s\",n.requestId,JSON.stringify(t,null,2))}return s},onResponse:(e,s)=>{if(o||!e)return e;const n=s.options.requestId;return r(\"[%s] Response code: %s %s\",n,e.statusCode,e.statusMessage),t&&e.body&&r(\"[%s] Response body: %s\",n,function(e){return-1!==(e.headers[\"content-type\"]||\"\").toLowerCase().indexOf(\"application/json\")?function(e){try{const t=\"string\"==typeof e?JSON.parse(e):e;return JSON.stringify(t,null,2)}catch{return e}}(e.body):e.body}(e)),e},onError:(e,t)=>{const s=t.options.requestId;return e?(r(\"[%s] ERROR: %s\",s,e.message),e):(r(\"[%s] Error encountered, but handled by an earlier middleware\",s),e)}}}function g(e,t={}){return{processOptions:s=>{const n=s.headers||{};return s.headers=t.override?Object.assign({},n,e):Object.assign({},e,n),s}}}class b extends Error{response;request;constructor(e,t){super();const s=e.url.length>400?`${e.url.slice(0,399)}…`:e.url;let n=`${e.method}-request to ${s} resulted in `;n+=`HTTP ${e.statusCode} ${e.statusMessage}`,this.message=n.trim(),this.response=e,this.request=t.options}}function y(){return{onResponse:(e,t)=>{if(!(e.statusCode>=400))return e;throw new b(e,t)}}}function w(e={}){if(\"function\"!=typeof e.inject)throw new Error(\"`injectResponse` middleware requires a `inject` function\");return{interceptRequest:function(t,s){const n=e.inject(s,t);if(!n)return t;const r=s.context.options;return{body:\"\",url:r.url,method:r.method,headers:{},statusCode:200,statusMessage:\"OK\",...n}}}}const F=typeof Buffer>\"u\"?()=>!1:e=>Buffer.isBuffer(e);function O(e){return\"[object Object]\"===Object.prototype.toString.call(e)}function j(e){if(!1===O(e))return!1;const t=e.constructor;if(void 0===t)return!0;const s=t.prototype;return!(!1===O(s)||!1===s.hasOwnProperty(\"isPrototypeOf\"))}const v=[\"boolean\",\"string\",\"number\"];function x(){return{processOptions:e=>{const t=e.body;return!t||\"function\"==typeof t.pipe||F(t)||-1===v.indexOf(typeof t)&&!Array.isArray(t)&&!j(t)?e:Object.assign({},e,{body:JSON.stringify(e.body),headers:Object.assign({},e.headers,{\"Content-Type\":\"application/json\"})})}}}function E(e){return{onResponse:s=>{const n=s.headers[\"content-type\"]||\"\",r=e&&e.force||-1!==n.indexOf(\"application/json\");return s.body&&n&&r?Object.assign({},s,{body:t(s.body)}):s},processOptions:e=>Object.assign({},e,{headers:Object.assign({Accept:\"application/json\"},e.headers)})};function t(e){try{return JSON.parse(e)}catch(e){throw e.message=`Failed to parsed response body as JSON: ${e.message}`,e}}}function k(e={}){if(!e.ca)throw new Error('Required mtls option \"ca\" is missing');if(!e.cert)throw new Error('Required mtls option \"cert\" is missing');if(!e.key)throw new Error('Required mtls option \"key\" is missing');return{finalizeOptions:t=>{if(function(e){return\"object\"==typeof e&&null!==e&&!(\"protocol\"in e)}(t))return t;const s={cert:e.cert,key:e.key,ca:e.ca};return Object.assign({},t,s)}}}let R={};typeof globalThis<\"u\"?R=globalThis:typeof window<\"u\"?R=window:typeof global<\"u\"?R=global:typeof self<\"u\"&&(R=self);var q=R;function A(e={}){const t=e.implementation||q.Observable;if(!t)throw new Error(\"`Observable` is not available in global scope, and no implementation was passed\");return{onReturn:(e,s)=>new t((t=>(e.error.subscribe((e=>t.error(e))),e.progress.subscribe((e=>t.next(Object.assign({type:\"progress\"},e)))),e.response.subscribe((e=>{t.next(Object.assign({type:\"response\"},e)),t.complete()})),e.request.publish(s),()=>e.abort.publish())))}}function S(){return{onRequest:e=>{if(\"xhr\"!==e.adapter)return;const t=e.request,s=e.context;function n(e){return t=>{const n=t.lengthComputable?t.loaded/t.total*100:-1;s.channels.progress.publish({stage:e,percent:n,total:t.total,loaded:t.loaded,lengthComputable:t.lengthComputable})}}\"upload\"in t&&\"onprogress\"in t.upload&&(t.upload.onprogress=n(\"upload\")),\"onprogress\"in t&&(t.onprogress=n(\"download\"))}}}const N=(e={})=>{const t=e.implementation||Promise;if(!t)throw new Error(\"`Promise` is not available in global scope, and no implementation was passed\");return{onReturn:(s,n)=>new t(((t,r)=>{const o=n.options.cancelToken;o&&o.promise.then((e=>{s.abort.publish(e),r(e)})),s.error.subscribe(r),s.response.subscribe((s=>{t(e.onlyBody?s.body:s)})),setTimeout((()=>{try{s.request.publish(n)}catch(e){r(e)}}),0)}))}};class T{__CANCEL__=!0;message;constructor(e){this.message=e}toString(){return\"Cancel\"+(this.message?`: ${this.message}`:\"\")}}class I{promise;reason;constructor(e){if(\"function\"!=typeof e)throw new TypeError(\"executor must be a function.\");let t=null;this.promise=new Promise((e=>{t=e})),e((e=>{this.reason||(this.reason=new T(e),t(this.reason))}))}static source=()=>{let e;return{token:new I((t=>{e=t})),cancel:e}}}function M(e){if(!(!1===e||e&&e.host))throw new Error(\"Proxy middleware takes an object of host, port and auth properties\");return{processOptions:t=>Object.assign({proxy:e},t)}}N.Cancel=T,N.CancelToken=I,N.isCancel=e=>!(!e||!e?.__CANCEL__);var $=(e,t,s)=>(\"GET\"===s.method||\"HEAD\"===s.method)&&(e.isNetworkError||!1);function _(e){return 100*Math.pow(2,e)+100*Math.random()}const P=(e={})=>(e=>{const t=e.maxRetries||5,s=e.retryDelay||_,n=e.shouldRetry;return{onError:(e,r)=>{const o=r.options,i=o.maxRetries||t,a=o.retryDelay||s,c=o.shouldRetry||n,u=o.attemptNumber||0;if(null!==(l=o.body)&&\"object\"==typeof l&&\"function\"==typeof l.pipe||!c(e,u,o)||u>=i)return e;var l;const p=Object.assign({},r,{options:Object.assign({},o,{attemptNumber:u+1})});return setTimeout((()=>r.channels.request.publish(p)),a(u)),null}}})({shouldRetry:$,...e});function J(e){const t=new URLSearchParams,s=(e,n)=>{const r=n instanceof Set?Array.from(n):n;if(Array.isArray(r))if(r.length)for(const t in r)s(`${e}[${t}]`,r[t]);else t.append(`${e}[]`,\"\");else if(\"object\"==typeof r&&null!==r)for(const[t,n]of Object.entries(r))s(`${e}[${t}]`,n);else t.append(e,r)};for(const[t,n]of Object.entries(e))s(t,n);return t.toString()}function L(){return{processOptions:e=>{const t=e.body;return t&&\"function\"!=typeof t.pipe&&!F(t)&&j(t)?{...e,body:J(e.body),headers:{...e.headers,\"Content-Type\":\"application/x-www-form-urlencoded\"}}:e}}}P.shouldRetry=$;class z extends Error{request;code;constructor(e,t){super(e.message),this.request=t,this.code=e.code}}const B=(H=n,function(e={}){const{maxRetries:t=3,ms:s=1e3,maxFree:n=256}=e,{finalizeOptions:r}=H({keepAlive:!0,keepAliveMsecs:s,maxFreeSockets:n});return{finalizeOptions:r,onError:(e,s)=>{if((\"GET\"===s.options.method||\"POST\"===s.options.method)&&e instanceof z&&\"ECONNRESET\"===e.code&&e.request.reusedSocket){const e=s.options.attemptNumber||0;if(e<t){const t=Object.assign({},s,{options:Object.assign({},s.options,{attemptNumber:e+1})});return setImmediate((()=>s.channels.request.publish(t))),null}}return e}}});var H;export{T as Cancel,I as CancelToken,n as agent,i as base,h as debug,g as headers,y as httpErrors,w as injectResponse,x as jsonRequest,E as jsonResponse,B as keepAlive,k as mtls,A as observable,t as processOptions,S as progress,N as promise,M as proxy,P as retry,L as urlEncoded,s as validateOptions};//# sourceMappingURL=middleware.browser.js.map\n", "/******************************************************************************\nCopyright (c) Microsoft Corporation.\n\nPermission to use, copy, modify, and/or distribute this software for any\npurpose with or without fee is hereby granted.\n\nTHE SOFTWARE IS PROVIDED \"AS IS\" AND THE AUTHOR DISCLAIMS ALL WARRANTIES WITH\nREGARD TO THIS SOFTWARE INCLUDING ALL IMPLIED WARRANTIES OF MERCHANTABILITY\nAND FITNESS. IN NO EVENT SHALL THE AUTHOR BE LIABLE FOR ANY SPECIAL, DIRECT,\nINDIRECT, OR CONSEQUENTIAL DAMAGES OR ANY DAMAGES WHATSOEVER RESULTING FROM\nLOSS OF USE, DATA OR PROFITS, WHETHER IN AN ACTION OF CONTRACT, NEGLIGENCE OR\nOTHER TORTIOUS ACTION, ARISING OUT OF OR IN CONNECTION WITH THE USE OR\nPERFORMANCE OF THIS SOFTWARE.\n***************************************************************************** */\n/* global Reflect, Promise, SuppressedError, Symbol, Iterator */\n\nvar extendStatics = function(d, b) {\n  extendStatics = Object.setPrototypeOf ||\n      ({ __proto__: [] } instanceof Array && function (d, b) { d.__proto__ = b; }) ||\n      function (d, b) { for (var p in b) if (Object.prototype.hasOwnProperty.call(b, p)) d[p] = b[p]; };\n  return extendStatics(d, b);\n};\n\nexport function __extends(d, b) {\n  if (typeof b !== \"function\" && b !== null)\n      throw new TypeError(\"Class extends value \" + String(b) + \" is not a constructor or null\");\n  extendStatics(d, b);\n  function __() { this.constructor = d; }\n  d.prototype = b === null ? Object.create(b) : (__.prototype = b.prototype, new __());\n}\n\nexport var __assign = function() {\n  __assign = Object.assign || function __assign(t) {\n      for (var s, i = 1, n = arguments.length; i < n; i++) {\n          s = arguments[i];\n          for (var p in s) if (Object.prototype.hasOwnProperty.call(s, p)) t[p] = s[p];\n      }\n      return t;\n  }\n  return __assign.apply(this, arguments);\n}\n\nexport function __rest(s, e) {\n  var t = {};\n  for (var p in s) if (Object.prototype.hasOwnProperty.call(s, p) && e.indexOf(p) < 0)\n      t[p] = s[p];\n  if (s != null && typeof Object.getOwnPropertySymbols === \"function\")\n      for (var i = 0, p = Object.getOwnPropertySymbols(s); i < p.length; i++) {\n          if (e.indexOf(p[i]) < 0 && Object.prototype.propertyIsEnumerable.call(s, p[i]))\n              t[p[i]] = s[p[i]];\n      }\n  return t;\n}\n\nexport function __decorate(decorators, target, key, desc) {\n  var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;\n  if (typeof Reflect === \"object\" && typeof Reflect.decorate === \"function\") r = Reflect.decorate(decorators, target, key, desc);\n  else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;\n  return c > 3 && r && Object.defineProperty(target, key, r), r;\n}\n\nexport function __param(paramIndex, decorator) {\n  return function (target, key) { decorator(target, key, paramIndex); }\n}\n\nexport function __esDecorate(ctor, descriptorIn, decorators, contextIn, initializers, extraInitializers) {\n  function accept(f) { if (f !== void 0 && typeof f !== \"function\") throw new TypeError(\"Function expected\"); return f; }\n  var kind = contextIn.kind, key = kind === \"getter\" ? \"get\" : kind === \"setter\" ? \"set\" : \"value\";\n  var target = !descriptorIn && ctor ? contextIn[\"static\"] ? ctor : ctor.prototype : null;\n  var descriptor = descriptorIn || (target ? Object.getOwnPropertyDescriptor(target, contextIn.name) : {});\n  var _, done = false;\n  for (var i = decorators.length - 1; i >= 0; i--) {\n      var context = {};\n      for (var p in contextIn) context[p] = p === \"access\" ? {} : contextIn[p];\n      for (var p in contextIn.access) context.access[p] = contextIn.access[p];\n      context.addInitializer = function (f) { if (done) throw new TypeError(\"Cannot add initializers after decoration has completed\"); extraInitializers.push(accept(f || null)); };\n      var result = (0, decorators[i])(kind === \"accessor\" ? { get: descriptor.get, set: descriptor.set } : descriptor[key], context);\n      if (kind === \"accessor\") {\n          if (result === void 0) continue;\n          if (result === null || typeof result !== \"object\") throw new TypeError(\"Object expected\");\n          if (_ = accept(result.get)) descriptor.get = _;\n          if (_ = accept(result.set)) descriptor.set = _;\n          if (_ = accept(result.init)) initializers.unshift(_);\n      }\n      else if (_ = accept(result)) {\n          if (kind === \"field\") initializers.unshift(_);\n          else descriptor[key] = _;\n      }\n  }\n  if (target) Object.defineProperty(target, contextIn.name, descriptor);\n  done = true;\n};\n\nexport function __runInitializers(thisArg, initializers, value) {\n  var useValue = arguments.length > 2;\n  for (var i = 0; i < initializers.length; i++) {\n      value = useValue ? initializers[i].call(thisArg, value) : initializers[i].call(thisArg);\n  }\n  return useValue ? value : void 0;\n};\n\nexport function __propKey(x) {\n  return typeof x === \"symbol\" ? x : \"\".concat(x);\n};\n\nexport function __setFunctionName(f, name, prefix) {\n  if (typeof name === \"symbol\") name = name.description ? \"[\".concat(name.description, \"]\") : \"\";\n  return Object.defineProperty(f, \"name\", { configurable: true, value: prefix ? \"\".concat(prefix, \" \", name) : name });\n};\n\nexport function __metadata(metadataKey, metadataValue) {\n  if (typeof Reflect === \"object\" && typeof Reflect.metadata === \"function\") return Reflect.metadata(metadataKey, metadataValue);\n}\n\nexport function __awaiter(thisArg, _arguments, P, generator) {\n  function adopt(value) { return value instanceof P ? value : new P(function (resolve) { resolve(value); }); }\n  return new (P || (P = Promise))(function (resolve, reject) {\n      function fulfilled(value) { try { step(generator.next(value)); } catch (e) { reject(e); } }\n      function rejected(value) { try { step(generator[\"throw\"](value)); } catch (e) { reject(e); } }\n      function step(result) { result.done ? resolve(result.value) : adopt(result.value).then(fulfilled, rejected); }\n      step((generator = generator.apply(thisArg, _arguments || [])).next());\n  });\n}\n\nexport function __generator(thisArg, body) {\n  var _ = { label: 0, sent: function() { if (t[0] & 1) throw t[1]; return t[1]; }, trys: [], ops: [] }, f, y, t, g = Object.create((typeof Iterator === \"function\" ? Iterator : Object).prototype);\n  return g.next = verb(0), g[\"throw\"] = verb(1), g[\"return\"] = verb(2), typeof Symbol === \"function\" && (g[Symbol.iterator] = function() { return this; }), g;\n  function verb(n) { return function (v) { return step([n, v]); }; }\n  function step(op) {\n      if (f) throw new TypeError(\"Generator is already executing.\");\n      while (g && (g = 0, op[0] && (_ = 0)), _) try {\n          if (f = 1, y && (t = op[0] & 2 ? y[\"return\"] : op[0] ? y[\"throw\"] || ((t = y[\"return\"]) && t.call(y), 0) : y.next) && !(t = t.call(y, op[1])).done) return t;\n          if (y = 0, t) op = [op[0] & 2, t.value];\n          switch (op[0]) {\n              case 0: case 1: t = op; break;\n              case 4: _.label++; return { value: op[1], done: false };\n              case 5: _.label++; y = op[1]; op = [0]; continue;\n              case 7: op = _.ops.pop(); _.trys.pop(); continue;\n              default:\n                  if (!(t = _.trys, t = t.length > 0 && t[t.length - 1]) && (op[0] === 6 || op[0] === 2)) { _ = 0; continue; }\n                  if (op[0] === 3 && (!t || (op[1] > t[0] && op[1] < t[3]))) { _.label = op[1]; break; }\n                  if (op[0] === 6 && _.label < t[1]) { _.label = t[1]; t = op; break; }\n                  if (t && _.label < t[2]) { _.label = t[2]; _.ops.push(op); break; }\n                  if (t[2]) _.ops.pop();\n                  _.trys.pop(); continue;\n          }\n          op = body.call(thisArg, _);\n      } catch (e) { op = [6, e]; y = 0; } finally { f = t = 0; }\n      if (op[0] & 5) throw op[1]; return { value: op[0] ? op[1] : void 0, done: true };\n  }\n}\n\nexport var __createBinding = Object.create ? (function(o, m, k, k2) {\n  if (k2 === undefined) k2 = k;\n  var desc = Object.getOwnPropertyDescriptor(m, k);\n  if (!desc || (\"get\" in desc ? !m.__esModule : desc.writable || desc.configurable)) {\n      desc = { enumerable: true, get: function() { return m[k]; } };\n  }\n  Object.defineProperty(o, k2, desc);\n}) : (function(o, m, k, k2) {\n  if (k2 === undefined) k2 = k;\n  o[k2] = m[k];\n});\n\nexport function __exportStar(m, o) {\n  for (var p in m) if (p !== \"default\" && !Object.prototype.hasOwnProperty.call(o, p)) __createBinding(o, m, p);\n}\n\nexport function __values(o) {\n  var s = typeof Symbol === \"function\" && Symbol.iterator, m = s && o[s], i = 0;\n  if (m) return m.call(o);\n  if (o && typeof o.length === \"number\") return {\n      next: function () {\n          if (o && i >= o.length) o = void 0;\n          return { value: o && o[i++], done: !o };\n      }\n  };\n  throw new TypeError(s ? \"Object is not iterable.\" : \"Symbol.iterator is not defined.\");\n}\n\nexport function __read(o, n) {\n  var m = typeof Symbol === \"function\" && o[Symbol.iterator];\n  if (!m) return o;\n  var i = m.call(o), r, ar = [], e;\n  try {\n      while ((n === void 0 || n-- > 0) && !(r = i.next()).done) ar.push(r.value);\n  }\n  catch (error) { e = { error: error }; }\n  finally {\n      try {\n          if (r && !r.done && (m = i[\"return\"])) m.call(i);\n      }\n      finally { if (e) throw e.error; }\n  }\n  return ar;\n}\n\n/** @deprecated */\nexport function __spread() {\n  for (var ar = [], i = 0; i < arguments.length; i++)\n      ar = ar.concat(__read(arguments[i]));\n  return ar;\n}\n\n/** @deprecated */\nexport function __spreadArrays() {\n  for (var s = 0, i = 0, il = arguments.length; i < il; i++) s += arguments[i].length;\n  for (var r = Array(s), k = 0, i = 0; i < il; i++)\n      for (var a = arguments[i], j = 0, jl = a.length; j < jl; j++, k++)\n          r[k] = a[j];\n  return r;\n}\n\nexport function __spreadArray(to, from, pack) {\n  if (pack || arguments.length === 2) for (var i = 0, l = from.length, ar; i < l; i++) {\n      if (ar || !(i in from)) {\n          if (!ar) ar = Array.prototype.slice.call(from, 0, i);\n          ar[i] = from[i];\n      }\n  }\n  return to.concat(ar || Array.prototype.slice.call(from));\n}\n\nexport function __await(v) {\n  return this instanceof __await ? (this.v = v, this) : new __await(v);\n}\n\nexport function __asyncGenerator(thisArg, _arguments, generator) {\n  if (!Symbol.asyncIterator) throw new TypeError(\"Symbol.asyncIterator is not defined.\");\n  var g = generator.apply(thisArg, _arguments || []), i, q = [];\n  return i = Object.create((typeof AsyncIterator === \"function\" ? AsyncIterator : Object).prototype), verb(\"next\"), verb(\"throw\"), verb(\"return\", awaitReturn), i[Symbol.asyncIterator] = function () { return this; }, i;\n  function awaitReturn(f) { return function (v) { return Promise.resolve(v).then(f, reject); }; }\n  function verb(n, f) { if (g[n]) { i[n] = function (v) { return new Promise(function (a, b) { q.push([n, v, a, b]) > 1 || resume(n, v); }); }; if (f) i[n] = f(i[n]); } }\n  function resume(n, v) { try { step(g[n](v)); } catch (e) { settle(q[0][3], e); } }\n  function step(r) { r.value instanceof __await ? Promise.resolve(r.value.v).then(fulfill, reject) : settle(q[0][2], r); }\n  function fulfill(value) { resume(\"next\", value); }\n  function reject(value) { resume(\"throw\", value); }\n  function settle(f, v) { if (f(v), q.shift(), q.length) resume(q[0][0], q[0][1]); }\n}\n\nexport function __asyncDelegator(o) {\n  var i, p;\n  return i = {}, verb(\"next\"), verb(\"throw\", function (e) { throw e; }), verb(\"return\"), i[Symbol.iterator] = function () { return this; }, i;\n  function verb(n, f) { i[n] = o[n] ? function (v) { return (p = !p) ? { value: __await(o[n](v)), done: false } : f ? f(v) : v; } : f; }\n}\n\nexport function __asyncValues(o) {\n  if (!Symbol.asyncIterator) throw new TypeError(\"Symbol.asyncIterator is not defined.\");\n  var m = o[Symbol.asyncIterator], i;\n  return m ? m.call(o) : (o = typeof __values === \"function\" ? __values(o) : o[Symbol.iterator](), i = {}, verb(\"next\"), verb(\"throw\"), verb(\"return\"), i[Symbol.asyncIterator] = function () { return this; }, i);\n  function verb(n) { i[n] = o[n] && function (v) { return new Promise(function (resolve, reject) { v = o[n](v), settle(resolve, reject, v.done, v.value); }); }; }\n  function settle(resolve, reject, d, v) { Promise.resolve(v).then(function(v) { resolve({ value: v, done: d }); }, reject); }\n}\n\nexport function __makeTemplateObject(cooked, raw) {\n  if (Object.defineProperty) { Object.defineProperty(cooked, \"raw\", { value: raw }); } else { cooked.raw = raw; }\n  return cooked;\n};\n\nvar __setModuleDefault = Object.create ? (function(o, v) {\n  Object.defineProperty(o, \"default\", { enumerable: true, value: v });\n}) : function(o, v) {\n  o[\"default\"] = v;\n};\n\nvar ownKeys = function(o) {\n  ownKeys = Object.getOwnPropertyNames || function (o) {\n    var ar = [];\n    for (var k in o) if (Object.prototype.hasOwnProperty.call(o, k)) ar[ar.length] = k;\n    return ar;\n  };\n  return ownKeys(o);\n};\n\nexport function __importStar(mod) {\n  if (mod && mod.__esModule) return mod;\n  var result = {};\n  if (mod != null) for (var k = ownKeys(mod), i = 0; i < k.length; i++) if (k[i] !== \"default\") __createBinding(result, mod, k[i]);\n  __setModuleDefault(result, mod);\n  return result;\n}\n\nexport function __importDefault(mod) {\n  return (mod && mod.__esModule) ? mod : { default: mod };\n}\n\nexport function __classPrivateFieldGet(receiver, state, kind, f) {\n  if (kind === \"a\" && !f) throw new TypeError(\"Private accessor was defined without a getter\");\n  if (typeof state === \"function\" ? receiver !== state || !f : !state.has(receiver)) throw new TypeError(\"Cannot read private member from an object whose class did not declare it\");\n  return kind === \"m\" ? f : kind === \"a\" ? f.call(receiver) : f ? f.value : state.get(receiver);\n}\n\nexport function __classPrivateFieldSet(receiver, state, value, kind, f) {\n  if (kind === \"m\") throw new TypeError(\"Private method is not writable\");\n  if (kind === \"a\" && !f) throw new TypeError(\"Private accessor was defined without a setter\");\n  if (typeof state === \"function\" ? receiver !== state || !f : !state.has(receiver)) throw new TypeError(\"Cannot write private member to an object whose class did not declare it\");\n  return (kind === \"a\" ? f.call(receiver, value) : f ? f.value = value : state.set(receiver, value)), value;\n}\n\nexport function __classPrivateFieldIn(state, receiver) {\n  if (receiver === null || (typeof receiver !== \"object\" && typeof receiver !== \"function\")) throw new TypeError(\"Cannot use 'in' operator on non-object\");\n  return typeof state === \"function\" ? receiver === state : state.has(receiver);\n}\n\nexport function __addDisposableResource(env, value, async) {\n  if (value !== null && value !== void 0) {\n    if (typeof value !== \"object\" && typeof value !== \"function\") throw new TypeError(\"Object expected.\");\n    var dispose, inner;\n    if (async) {\n      if (!Symbol.asyncDispose) throw new TypeError(\"Symbol.asyncDispose is not defined.\");\n      dispose = value[Symbol.asyncDispose];\n    }\n    if (dispose === void 0) {\n      if (!Symbol.dispose) throw new TypeError(\"Symbol.dispose is not defined.\");\n      dispose = value[Symbol.dispose];\n      if (async) inner = dispose;\n    }\n    if (typeof dispose !== \"function\") throw new TypeError(\"Object not disposable.\");\n    if (inner) dispose = function() { try { inner.call(this); } catch (e) { return Promise.reject(e); } };\n    env.stack.push({ value: value, dispose: dispose, async: async });\n  }\n  else if (async) {\n    env.stack.push({ async: true });\n  }\n  return value;\n}\n\nvar _SuppressedError = typeof SuppressedError === \"function\" ? SuppressedError : function (error, suppressed, message) {\n  var e = new Error(message);\n  return e.name = \"SuppressedError\", e.error = error, e.suppressed = suppressed, e;\n};\n\nexport function __disposeResources(env) {\n  function fail(e) {\n    env.error = env.hasError ? new _SuppressedError(e, env.error, \"An error was suppressed during disposal.\") : e;\n    env.hasError = true;\n  }\n  var r, s = 0;\n  function next() {\n    while (r = env.stack.pop()) {\n      try {\n        if (!r.async && s === 1) return s = 0, env.stack.push(r), Promise.resolve().then(next);\n        if (r.dispose) {\n          var result = r.dispose.call(r.value);\n          if (r.async) return s |= 2, Promise.resolve(result).then(next, function(e) { fail(e); return next(); });\n        }\n        else s |= 1;\n      }\n      catch (e) {\n        fail(e);\n      }\n    }\n    if (s === 1) return env.hasError ? Promise.reject(env.error) : Promise.resolve();\n    if (env.hasError) throw env.error;\n  }\n  return next();\n}\n\nexport function __rewriteRelativeImportExtension(path, preserveJsx) {\n  if (typeof path === \"string\" && /^\\.\\.?\\//.test(path)) {\n      return path.replace(/\\.(tsx)$|((?:\\.d)?)((?:\\.[^./]+?)?)\\.([cm]?)ts$/i, function (m, tsx, d, ext, cm) {\n          return tsx ? preserveJsx ? \".jsx\" : \".js\" : d && (!ext || !cm) ? m : (d + ext + \".\" + cm.toLowerCase() + \"js\");\n      });\n  }\n  return path;\n}\n\nexport default {\n  __extends,\n  __assign,\n  __rest,\n  __decorate,\n  __param,\n  __esDecorate,\n  __runInitializers,\n  __propKey,\n  __setFunctionName,\n  __metadata,\n  __awaiter,\n  __generator,\n  __createBinding,\n  __exportStar,\n  __values,\n  __read,\n  __spread,\n  __spreadArrays,\n  __spreadArray,\n  __await,\n  __asyncGenerator,\n  __asyncDelegator,\n  __asyncValues,\n  __makeTemplateObject,\n  __importStar,\n  __importDefault,\n  __classPrivateFieldGet,\n  __classPrivateFieldSet,\n  __classPrivateFieldIn,\n  __addDisposableResource,\n  __disposeResources,\n  __rewriteRelativeImportExtension,\n};\n", "export function isFunction(value) {\n    return typeof value === 'function';\n}\n//# sourceMappingURL=isFunction.js.map", "export function createErrorClass(createImpl) {\n    var _super = function (instance) {\n        Error.call(instance);\n        instance.stack = new Error().stack;\n    };\n    var ctorFunc = createImpl(_super);\n    ctorFunc.prototype = Object.create(Error.prototype);\n    ctorFunc.prototype.constructor = ctorFunc;\n    return ctorFunc;\n}\n//# sourceMappingURL=createErrorClass.js.map", "import { createErrorClass } from './createErrorClass';\nexport var UnsubscriptionError = createErrorClass(function (_super) {\n    return function UnsubscriptionErrorImpl(errors) {\n        _super(this);\n        this.message = errors\n            ? errors.length + \" errors occurred during unsubscription:\\n\" + errors.map(function (err, i) { return i + 1 + \") \" + err.toString(); }).join('\\n  ')\n            : '';\n        this.name = 'UnsubscriptionError';\n        this.errors = errors;\n    };\n});\n//# sourceMappingURL=UnsubscriptionError.js.map", "export function arrRemove(arr, item) {\n    if (arr) {\n        var index = arr.indexOf(item);\n        0 <= index && arr.splice(index, 1);\n    }\n}\n//# sourceMappingURL=arrRemove.js.map", "import { __read, __spreadArray, __values } from \"tslib\";\nimport { isFunction } from './util/isFunction';\nimport { UnsubscriptionError } from './util/UnsubscriptionError';\nimport { arrRemove } from './util/arrRemove';\nvar Subscription = (function () {\n    function Subscription(initialTeardown) {\n        this.initialTeardown = initialTeardown;\n        this.closed = false;\n        this._parentage = null;\n        this._finalizers = null;\n    }\n    Subscription.prototype.unsubscribe = function () {\n        var e_1, _a, e_2, _b;\n        var errors;\n        if (!this.closed) {\n            this.closed = true;\n            var _parentage = this._parentage;\n            if (_parentage) {\n                this._parentage = null;\n                if (Array.isArray(_parentage)) {\n                    try {\n                        for (var _parentage_1 = __values(_parentage), _parentage_1_1 = _parentage_1.next(); !_parentage_1_1.done; _parentage_1_1 = _parentage_1.next()) {\n                            var parent_1 = _parentage_1_1.value;\n                            parent_1.remove(this);\n                        }\n                    }\n                    catch (e_1_1) { e_1 = { error: e_1_1 }; }\n                    finally {\n                        try {\n                            if (_parentage_1_1 && !_parentage_1_1.done && (_a = _parentage_1.return)) _a.call(_parentage_1);\n                        }\n                        finally { if (e_1) throw e_1.error; }\n                    }\n                }\n                else {\n                    _parentage.remove(this);\n                }\n            }\n            var initialFinalizer = this.initialTeardown;\n            if (isFunction(initialFinalizer)) {\n                try {\n                    initialFinalizer();\n                }\n                catch (e) {\n                    errors = e instanceof UnsubscriptionError ? e.errors : [e];\n                }\n            }\n            var _finalizers = this._finalizers;\n            if (_finalizers) {\n                this._finalizers = null;\n                try {\n                    for (var _finalizers_1 = __values(_finalizers), _finalizers_1_1 = _finalizers_1.next(); !_finalizers_1_1.done; _finalizers_1_1 = _finalizers_1.next()) {\n                        var finalizer = _finalizers_1_1.value;\n                        try {\n                            execFinalizer(finalizer);\n                        }\n                        catch (err) {\n                            errors = errors !== null && errors !== void 0 ? errors : [];\n                            if (err instanceof UnsubscriptionError) {\n                                errors = __spreadArray(__spreadArray([], __read(errors)), __read(err.errors));\n                            }\n                            else {\n                                errors.push(err);\n                            }\n                        }\n                    }\n                }\n                catch (e_2_1) { e_2 = { error: e_2_1 }; }\n                finally {\n                    try {\n                        if (_finalizers_1_1 && !_finalizers_1_1.done && (_b = _finalizers_1.return)) _b.call(_finalizers_1);\n                    }\n                    finally { if (e_2) throw e_2.error; }\n                }\n            }\n            if (errors) {\n                throw new UnsubscriptionError(errors);\n            }\n        }\n    };\n    Subscription.prototype.add = function (teardown) {\n        var _a;\n        if (teardown && teardown !== this) {\n            if (this.closed) {\n                execFinalizer(teardown);\n            }\n            else {\n                if (teardown instanceof Subscription) {\n                    if (teardown.closed || teardown._hasParent(this)) {\n                        return;\n                    }\n                    teardown._addParent(this);\n                }\n                (this._finalizers = (_a = this._finalizers) !== null && _a !== void 0 ? _a : []).push(teardown);\n            }\n        }\n    };\n    Subscription.prototype._hasParent = function (parent) {\n        var _parentage = this._parentage;\n        return _parentage === parent || (Array.isArray(_parentage) && _parentage.includes(parent));\n    };\n    Subscription.prototype._addParent = function (parent) {\n        var _parentage = this._parentage;\n        this._parentage = Array.isArray(_parentage) ? (_parentage.push(parent), _parentage) : _parentage ? [_parentage, parent] : parent;\n    };\n    Subscription.prototype._removeParent = function (parent) {\n        var _parentage = this._parentage;\n        if (_parentage === parent) {\n            this._parentage = null;\n        }\n        else if (Array.isArray(_parentage)) {\n            arrRemove(_parentage, parent);\n        }\n    };\n    Subscription.prototype.remove = function (teardown) {\n        var _finalizers = this._finalizers;\n        _finalizers && arrRemove(_finalizers, teardown);\n        if (teardown instanceof Subscription) {\n            teardown._removeParent(this);\n        }\n    };\n    Subscription.EMPTY = (function () {\n        var empty = new Subscription();\n        empty.closed = true;\n        return empty;\n    })();\n    return Subscription;\n}());\nexport { Subscription };\nexport var EMPTY_SUBSCRIPTION = Subscription.EMPTY;\nexport function isSubscription(value) {\n    return (value instanceof Subscription ||\n        (value && 'closed' in value && isFunction(value.remove) && isFunction(value.add) && isFunction(value.unsubscribe)));\n}\nfunction execFinalizer(finalizer) {\n    if (isFunction(finalizer)) {\n        finalizer();\n    }\n    else {\n        finalizer.unsubscribe();\n    }\n}\n//# sourceMappingURL=Subscription.js.map", "export var config = {\n    onUnhandledError: null,\n    onStoppedNotification: null,\n    Promise: undefined,\n    useDeprecatedSynchronousErrorHandling: false,\n    useDeprecatedNextContext: false,\n};\n//# sourceMappingURL=config.js.map", "import { __read, __spreadArray } from \"tslib\";\nexport var timeoutProvider = {\n    setTimeout: function (handler, timeout) {\n        var args = [];\n        for (var _i = 2; _i < arguments.length; _i++) {\n            args[_i - 2] = arguments[_i];\n        }\n        var delegate = timeoutProvider.delegate;\n        if (delegate === null || delegate === void 0 ? void 0 : delegate.setTimeout) {\n            return delegate.setTimeout.apply(delegate, __spreadArray([handler, timeout], __read(args)));\n        }\n        return setTimeout.apply(void 0, __spreadArray([handler, timeout], __read(args)));\n    },\n    clearTimeout: function (handle) {\n        var delegate = timeoutProvider.delegate;\n        return ((delegate === null || delegate === void 0 ? void 0 : delegate.clearTimeout) || clearTimeout)(handle);\n    },\n    delegate: undefined,\n};\n//# sourceMappingURL=timeoutProvider.js.map", "import { config } from '../config';\nimport { timeoutProvider } from '../scheduler/timeoutProvider';\nexport function reportUnhandledError(err) {\n    timeoutProvider.setTimeout(function () {\n        var onUnhandledError = config.onUnhandledError;\n        if (onUnhandledError) {\n            onUnhandledError(err);\n        }\n        else {\n            throw err;\n        }\n    });\n}\n//# sourceMappingURL=reportUnhandledError.js.map", "export function noop() { }\n//# sourceMappingURL=noop.js.map", "import { config } from '../config';\nvar context = null;\nexport function errorContext(cb) {\n    if (config.useDeprecatedSynchronousErrorHandling) {\n        var isRoot = !context;\n        if (isRoot) {\n            context = { errorThrown: false, error: null };\n        }\n        cb();\n        if (isRoot) {\n            var _a = context, errorThrown = _a.errorThrown, error = _a.error;\n            context = null;\n            if (errorThrown) {\n                throw error;\n            }\n        }\n    }\n    else {\n        cb();\n    }\n}\nexport function captureError(err) {\n    if (config.useDeprecatedSynchronousErrorHandling && context) {\n        context.errorThrown = true;\n        context.error = err;\n    }\n}\n//# sourceMappingURL=errorContext.js.map", "import { __extends } from \"tslib\";\nimport { isFunction } from './util/isFunction';\nimport { isSubscription, Subscription } from './Subscription';\nimport { config } from './config';\nimport { reportUnhandledError } from './util/reportUnhandledError';\nimport { noop } from './util/noop';\nimport { nextNotification, errorNotification, COMPLETE_NOTIFICATION } from './NotificationFactories';\nimport { timeoutProvider } from './scheduler/timeoutProvider';\nimport { captureError } from './util/errorContext';\nvar Subscriber = (function (_super) {\n    __extends(Subscriber, _super);\n    function Subscriber(destination) {\n        var _this = _super.call(this) || this;\n        _this.isStopped = false;\n        if (destination) {\n            _this.destination = destination;\n            if (isSubscription(destination)) {\n                destination.add(_this);\n            }\n        }\n        else {\n            _this.destination = EMPTY_OBSERVER;\n        }\n        return _this;\n    }\n    Subscriber.create = function (next, error, complete) {\n        return new SafeSubscriber(next, error, complete);\n    };\n    Subscriber.prototype.next = function (value) {\n        if (this.isStopped) {\n            handleStoppedNotification(nextNotification(value), this);\n        }\n        else {\n            this._next(value);\n        }\n    };\n    Subscriber.prototype.error = function (err) {\n        if (this.isStopped) {\n            handleStoppedNotification(errorNotification(err), this);\n        }\n        else {\n            this.isStopped = true;\n            this._error(err);\n        }\n    };\n    Subscriber.prototype.complete = function () {\n        if (this.isStopped) {\n            handleStoppedNotification(COMPLETE_NOTIFICATION, this);\n        }\n        else {\n            this.isStopped = true;\n            this._complete();\n        }\n    };\n    Subscriber.prototype.unsubscribe = function () {\n        if (!this.closed) {\n            this.isStopped = true;\n            _super.prototype.unsubscribe.call(this);\n            this.destination = null;\n        }\n    };\n    Subscriber.prototype._next = function (value) {\n        this.destination.next(value);\n    };\n    Subscriber.prototype._error = function (err) {\n        try {\n            this.destination.error(err);\n        }\n        finally {\n            this.unsubscribe();\n        }\n    };\n    Subscriber.prototype._complete = function () {\n        try {\n            this.destination.complete();\n        }\n        finally {\n            this.unsubscribe();\n        }\n    };\n    return Subscriber;\n}(Subscription));\nexport { Subscriber };\nvar _bind = Function.prototype.bind;\nfunction bind(fn, thisArg) {\n    return _bind.call(fn, thisArg);\n}\nvar ConsumerObserver = (function () {\n    function ConsumerObserver(partialObserver) {\n        this.partialObserver = partialObserver;\n    }\n    ConsumerObserver.prototype.next = function (value) {\n        var partialObserver = this.partialObserver;\n        if (partialObserver.next) {\n            try {\n                partialObserver.next(value);\n            }\n            catch (error) {\n                handleUnhandledError(error);\n            }\n        }\n    };\n    ConsumerObserver.prototype.error = function (err) {\n        var partialObserver = this.partialObserver;\n        if (partialObserver.error) {\n            try {\n                partialObserver.error(err);\n            }\n            catch (error) {\n                handleUnhandledError(error);\n            }\n        }\n        else {\n            handleUnhandledError(err);\n        }\n    };\n    ConsumerObserver.prototype.complete = function () {\n        var partialObserver = this.partialObserver;\n        if (partialObserver.complete) {\n            try {\n                partialObserver.complete();\n            }\n            catch (error) {\n                handleUnhandledError(error);\n            }\n        }\n    };\n    return ConsumerObserver;\n}());\nvar SafeSubscriber = (function (_super) {\n    __extends(SafeSubscriber, _super);\n    function SafeSubscriber(observerOrNext, error, complete) {\n        var _this = _super.call(this) || this;\n        var partialObserver;\n        if (isFunction(observerOrNext) || !observerOrNext) {\n            partialObserver = {\n                next: (observerOrNext !== null && observerOrNext !== void 0 ? observerOrNext : undefined),\n                error: error !== null && error !== void 0 ? error : undefined,\n                complete: complete !== null && complete !== void 0 ? complete : undefined,\n            };\n        }\n        else {\n            var context_1;\n            if (_this && config.useDeprecatedNextContext) {\n                context_1 = Object.create(observerOrNext);\n                context_1.unsubscribe = function () { return _this.unsubscribe(); };\n                partialObserver = {\n                    next: observerOrNext.next && bind(observerOrNext.next, context_1),\n                    error: observerOrNext.error && bind(observerOrNext.error, context_1),\n                    complete: observerOrNext.complete && bind(observerOrNext.complete, context_1),\n                };\n            }\n            else {\n                partialObserver = observerOrNext;\n            }\n        }\n        _this.destination = new ConsumerObserver(partialObserver);\n        return _this;\n    }\n    return SafeSubscriber;\n}(Subscriber));\nexport { SafeSubscriber };\nfunction handleUnhandledError(error) {\n    if (config.useDeprecatedSynchronousErrorHandling) {\n        captureError(error);\n    }\n    else {\n        reportUnhandledError(error);\n    }\n}\nfunction defaultErrorHandler(err) {\n    throw err;\n}\nfunction handleStoppedNotification(notification, subscriber) {\n    var onStoppedNotification = config.onStoppedNotification;\n    onStoppedNotification && timeoutProvider.setTimeout(function () { return onStoppedNotification(notification, subscriber); });\n}\nexport var EMPTY_OBSERVER = {\n    closed: true,\n    next: noop,\n    error: defaultErrorHandler,\n    complete: noop,\n};\n//# sourceMappingURL=Subscriber.js.map", "export var observable = (function () { return (typeof Symbol === 'function' && Symbol.observable) || '@@observable'; })();\n//# sourceMappingURL=observable.js.map", "export function identity(x) {\n    return x;\n}\n//# sourceMappingURL=identity.js.map", "import { identity } from './identity';\nexport function pipe() {\n    var fns = [];\n    for (var _i = 0; _i < arguments.length; _i++) {\n        fns[_i] = arguments[_i];\n    }\n    return pipeFromArray(fns);\n}\nexport function pipeFromArray(fns) {\n    if (fns.length === 0) {\n        return identity;\n    }\n    if (fns.length === 1) {\n        return fns[0];\n    }\n    return function piped(input) {\n        return fns.reduce(function (prev, fn) { return fn(prev); }, input);\n    };\n}\n//# sourceMappingURL=pipe.js.map", "import { SafeSubscriber, Subscriber } from './Subscriber';\nimport { isSubscription } from './Subscription';\nimport { observable as Symbol_observable } from './symbol/observable';\nimport { pipeFromArray } from './util/pipe';\nimport { config } from './config';\nimport { isFunction } from './util/isFunction';\nimport { errorContext } from './util/errorContext';\nvar Observable = (function () {\n    function Observable(subscribe) {\n        if (subscribe) {\n            this._subscribe = subscribe;\n        }\n    }\n    Observable.prototype.lift = function (operator) {\n        var observable = new Observable();\n        observable.source = this;\n        observable.operator = operator;\n        return observable;\n    };\n    Observable.prototype.subscribe = function (observerOrNext, error, complete) {\n        var _this = this;\n        var subscriber = isSubscriber(observerOrNext) ? observerOrNext : new SafeSubscriber(observerOrNext, error, complete);\n        errorContext(function () {\n            var _a = _this, operator = _a.operator, source = _a.source;\n            subscriber.add(operator\n                ?\n                    operator.call(subscriber, source)\n                : source\n                    ?\n                        _this._subscribe(subscriber)\n                    :\n                        _this._trySubscribe(subscriber));\n        });\n        return subscriber;\n    };\n    Observable.prototype._trySubscribe = function (sink) {\n        try {\n            return this._subscribe(sink);\n        }\n        catch (err) {\n            sink.error(err);\n        }\n    };\n    Observable.prototype.forEach = function (next, promiseCtor) {\n        var _this = this;\n        promiseCtor = getPromiseCtor(promiseCtor);\n        return new promiseCtor(function (resolve, reject) {\n            var subscriber = new SafeSubscriber({\n                next: function (value) {\n                    try {\n                        next(value);\n                    }\n                    catch (err) {\n                        reject(err);\n                        subscriber.unsubscribe();\n                    }\n                },\n                error: reject,\n                complete: resolve,\n            });\n            _this.subscribe(subscriber);\n        });\n    };\n    Observable.prototype._subscribe = function (subscriber) {\n        var _a;\n        return (_a = this.source) === null || _a === void 0 ? void 0 : _a.subscribe(subscriber);\n    };\n    Observable.prototype[Symbol_observable] = function () {\n        return this;\n    };\n    Observable.prototype.pipe = function () {\n        var operations = [];\n        for (var _i = 0; _i < arguments.length; _i++) {\n            operations[_i] = arguments[_i];\n        }\n        return pipeFromArray(operations)(this);\n    };\n    Observable.prototype.toPromise = function (promiseCtor) {\n        var _this = this;\n        promiseCtor = getPromiseCtor(promiseCtor);\n        return new promiseCtor(function (resolve, reject) {\n            var value;\n            _this.subscribe(function (x) { return (value = x); }, function (err) { return reject(err); }, function () { return resolve(value); });\n        });\n    };\n    Observable.create = function (subscribe) {\n        return new Observable(subscribe);\n    };\n    return Observable;\n}());\nexport { Observable };\nfunction getPromiseCtor(promiseCtor) {\n    var _a;\n    return (_a = promiseCtor !== null && promiseCtor !== void 0 ? promiseCtor : config.Promise) !== null && _a !== void 0 ? _a : Promise;\n}\nfunction isObserver(value) {\n    return value && isFunction(value.next) && isFunction(value.error) && isFunction(value.complete);\n}\nfunction isSubscriber(value) {\n    return (value && value instanceof Subscriber) || (isObserver(value) && isSubscription(value));\n}\n//# sourceMappingURL=Observable.js.map", "import { isFunction } from './isFunction';\nexport function hasLift(source) {\n    return isFunction(source === null || source === void 0 ? void 0 : source.lift);\n}\nexport function operate(init) {\n    return function (source) {\n        if (hasLift(source)) {\n            return source.lift(function (liftedSource) {\n                try {\n                    return init(liftedSource, this);\n                }\n                catch (err) {\n                    this.error(err);\n                }\n            });\n        }\n        throw new TypeError('Unable to lift unknown Observable type');\n    };\n}\n//# sourceMappingURL=lift.js.map", "import { __extends } from \"tslib\";\nimport { Subscriber } from '../Subscriber';\nexport function createOperatorSubscriber(destination, onNext, onComplete, onError, onFinalize) {\n    return new OperatorSubscriber(destination, onNext, onComplete, onError, onFinalize);\n}\nvar OperatorSubscriber = (function (_super) {\n    __extends(OperatorSubscriber, _super);\n    function OperatorSubscriber(destination, onNext, onComplete, onError, onFinalize, shouldUnsubscribe) {\n        var _this = _super.call(this, destination) || this;\n        _this.onFinalize = onFinalize;\n        _this.shouldUnsubscribe = shouldUnsubscribe;\n        _this._next = onNext\n            ? function (value) {\n                try {\n                    onNext(value);\n                }\n                catch (err) {\n                    destination.error(err);\n                }\n            }\n            : _super.prototype._next;\n        _this._error = onError\n            ? function (err) {\n                try {\n                    onError(err);\n                }\n                catch (err) {\n                    destination.error(err);\n                }\n                finally {\n                    this.unsubscribe();\n                }\n            }\n            : _super.prototype._error;\n        _this._complete = onComplete\n            ? function () {\n                try {\n                    onComplete();\n                }\n                catch (err) {\n                    destination.error(err);\n                }\n                finally {\n                    this.unsubscribe();\n                }\n            }\n            : _super.prototype._complete;\n        return _this;\n    }\n    OperatorSubscriber.prototype.unsubscribe = function () {\n        var _a;\n        if (!this.shouldUnsubscribe || this.shouldUnsubscribe()) {\n            var closed_1 = this.closed;\n            _super.prototype.unsubscribe.call(this);\n            !closed_1 && ((_a = this.onFinalize) === null || _a === void 0 ? void 0 : _a.call(this));\n        }\n    };\n    return OperatorSubscriber;\n}(Subscriber));\nexport { OperatorSubscriber };\n//# sourceMappingURL=OperatorSubscriber.js.map", "import { createErrorClass } from './createErrorClass';\nexport var ObjectUnsubscribedError = createErrorClass(function (_super) {\n    return function ObjectUnsubscribedErrorImpl() {\n        _super(this);\n        this.name = 'ObjectUnsubscribedError';\n        this.message = 'object unsubscribed';\n    };\n});\n//# sourceMappingURL=ObjectUnsubscribedError.js.map", "import { __extends, __values } from \"tslib\";\nimport { Observable } from './Observable';\nimport { Subscription, EMPTY_SUBSCRIPTION } from './Subscription';\nimport { ObjectUnsubscribedError } from './util/ObjectUnsubscribedError';\nimport { arrRemove } from './util/arrRemove';\nimport { errorContext } from './util/errorContext';\nvar Subject = (function (_super) {\n    __extends(Subject, _super);\n    function Subject() {\n        var _this = _super.call(this) || this;\n        _this.closed = false;\n        _this.currentObservers = null;\n        _this.observers = [];\n        _this.isStopped = false;\n        _this.hasError = false;\n        _this.thrownError = null;\n        return _this;\n    }\n    Subject.prototype.lift = function (operator) {\n        var subject = new AnonymousSubject(this, this);\n        subject.operator = operator;\n        return subject;\n    };\n    Subject.prototype._throwIfClosed = function () {\n        if (this.closed) {\n            throw new ObjectUnsubscribedError();\n        }\n    };\n    Subject.prototype.next = function (value) {\n        var _this = this;\n        errorContext(function () {\n            var e_1, _a;\n            _this._throwIfClosed();\n            if (!_this.isStopped) {\n                if (!_this.currentObservers) {\n                    _this.currentObservers = Array.from(_this.observers);\n                }\n                try {\n                    for (var _b = __values(_this.currentObservers), _c = _b.next(); !_c.done; _c = _b.next()) {\n                        var observer = _c.value;\n                        observer.next(value);\n                    }\n                }\n                catch (e_1_1) { e_1 = { error: e_1_1 }; }\n                finally {\n                    try {\n                        if (_c && !_c.done && (_a = _b.return)) _a.call(_b);\n                    }\n                    finally { if (e_1) throw e_1.error; }\n                }\n            }\n        });\n    };\n    Subject.prototype.error = function (err) {\n        var _this = this;\n        errorContext(function () {\n            _this._throwIfClosed();\n            if (!_this.isStopped) {\n                _this.hasError = _this.isStopped = true;\n                _this.thrownError = err;\n                var observers = _this.observers;\n                while (observers.length) {\n                    observers.shift().error(err);\n                }\n            }\n        });\n    };\n    Subject.prototype.complete = function () {\n        var _this = this;\n        errorContext(function () {\n            _this._throwIfClosed();\n            if (!_this.isStopped) {\n                _this.isStopped = true;\n                var observers = _this.observers;\n                while (observers.length) {\n                    observers.shift().complete();\n                }\n            }\n        });\n    };\n    Subject.prototype.unsubscribe = function () {\n        this.isStopped = this.closed = true;\n        this.observers = this.currentObservers = null;\n    };\n    Object.defineProperty(Subject.prototype, \"observed\", {\n        get: function () {\n            var _a;\n            return ((_a = this.observers) === null || _a === void 0 ? void 0 : _a.length) > 0;\n        },\n        enumerable: false,\n        configurable: true\n    });\n    Subject.prototype._trySubscribe = function (subscriber) {\n        this._throwIfClosed();\n        return _super.prototype._trySubscribe.call(this, subscriber);\n    };\n    Subject.prototype._subscribe = function (subscriber) {\n        this._throwIfClosed();\n        this._checkFinalizedStatuses(subscriber);\n        return this._innerSubscribe(subscriber);\n    };\n    Subject.prototype._innerSubscribe = function (subscriber) {\n        var _this = this;\n        var _a = this, hasError = _a.hasError, isStopped = _a.isStopped, observers = _a.observers;\n        if (hasError || isStopped) {\n            return EMPTY_SUBSCRIPTION;\n        }\n        this.currentObservers = null;\n        observers.push(subscriber);\n        return new Subscription(function () {\n            _this.currentObservers = null;\n            arrRemove(observers, subscriber);\n        });\n    };\n    Subject.prototype._checkFinalizedStatuses = function (subscriber) {\n        var _a = this, hasError = _a.hasError, thrownError = _a.thrownError, isStopped = _a.isStopped;\n        if (hasError) {\n            subscriber.error(thrownError);\n        }\n        else if (isStopped) {\n            subscriber.complete();\n        }\n    };\n    Subject.prototype.asObservable = function () {\n        var observable = new Observable();\n        observable.source = this;\n        return observable;\n    };\n    Subject.create = function (destination, source) {\n        return new AnonymousSubject(destination, source);\n    };\n    return Subject;\n}(Observable));\nexport { Subject };\nvar AnonymousSubject = (function (_super) {\n    __extends(AnonymousSubject, _super);\n    function AnonymousSubject(destination, source) {\n        var _this = _super.call(this) || this;\n        _this.destination = destination;\n        _this.source = source;\n        return _this;\n    }\n    AnonymousSubject.prototype.next = function (value) {\n        var _a, _b;\n        (_b = (_a = this.destination) === null || _a === void 0 ? void 0 : _a.next) === null || _b === void 0 ? void 0 : _b.call(_a, value);\n    };\n    AnonymousSubject.prototype.error = function (err) {\n        var _a, _b;\n        (_b = (_a = this.destination) === null || _a === void 0 ? void 0 : _a.error) === null || _b === void 0 ? void 0 : _b.call(_a, err);\n    };\n    AnonymousSubject.prototype.complete = function () {\n        var _a, _b;\n        (_b = (_a = this.destination) === null || _a === void 0 ? void 0 : _a.complete) === null || _b === void 0 ? void 0 : _b.call(_a);\n    };\n    AnonymousSubject.prototype._subscribe = function (subscriber) {\n        var _a, _b;\n        return (_b = (_a = this.source) === null || _a === void 0 ? void 0 : _a.subscribe(subscriber)) !== null && _b !== void 0 ? _b : EMPTY_SUBSCRIPTION;\n    };\n    return AnonymousSubject;\n}(Subject));\nexport { AnonymousSubject };\n//# sourceMappingURL=Subject.js.map", "export var dateTimestampProvider = {\n    now: function () {\n        return (dateTimestampProvider.delegate || Date).now();\n    },\n    delegate: undefined,\n};\n//# sourceMappingURL=dateTimestampProvider.js.map", "import { __extends } from \"tslib\";\nimport { Subject } from './Subject';\nimport { dateTimestampProvider } from './scheduler/dateTimestampProvider';\nvar ReplaySubject = (function (_super) {\n    __extends(ReplaySubject, _super);\n    function ReplaySubject(_bufferSize, _windowTime, _timestampProvider) {\n        if (_bufferSize === void 0) { _bufferSize = Infinity; }\n        if (_windowTime === void 0) { _windowTime = Infinity; }\n        if (_timestampProvider === void 0) { _timestampProvider = dateTimestampProvider; }\n        var _this = _super.call(this) || this;\n        _this._bufferSize = _bufferSize;\n        _this._windowTime = _windowTime;\n        _this._timestampProvider = _timestampProvider;\n        _this._buffer = [];\n        _this._infiniteTimeWindow = true;\n        _this._infiniteTimeWindow = _windowTime === Infinity;\n        _this._bufferSize = Math.max(1, _bufferSize);\n        _this._windowTime = Math.max(1, _windowTime);\n        return _this;\n    }\n    ReplaySubject.prototype.next = function (value) {\n        var _a = this, isStopped = _a.isStopped, _buffer = _a._buffer, _infiniteTimeWindow = _a._infiniteTimeWindow, _timestampProvider = _a._timestampProvider, _windowTime = _a._windowTime;\n        if (!isStopped) {\n            _buffer.push(value);\n            !_infiniteTimeWindow && _buffer.push(_timestampProvider.now() + _windowTime);\n        }\n        this._trimBuffer();\n        _super.prototype.next.call(this, value);\n    };\n    ReplaySubject.prototype._subscribe = function (subscriber) {\n        this._throwIfClosed();\n        this._trimBuffer();\n        var subscription = this._innerSubscribe(subscriber);\n        var _a = this, _infiniteTimeWindow = _a._infiniteTimeWindow, _buffer = _a._buffer;\n        var copy = _buffer.slice();\n        for (var i = 0; i < copy.length && !subscriber.closed; i += _infiniteTimeWindow ? 1 : 2) {\n            subscriber.next(copy[i]);\n        }\n        this._checkFinalizedStatuses(subscriber);\n        return subscription;\n    };\n    ReplaySubject.prototype._trimBuffer = function () {\n        var _a = this, _bufferSize = _a._bufferSize, _timestampProvider = _a._timestampProvider, _buffer = _a._buffer, _infiniteTimeWindow = _a._infiniteTimeWindow;\n        var adjustedBufferSize = (_infiniteTimeWindow ? 1 : 2) * _bufferSize;\n        _bufferSize < Infinity && adjustedBufferSize < _buffer.length && _buffer.splice(0, _buffer.length - adjustedBufferSize);\n        if (!_infiniteTimeWindow) {\n            var now = _timestampProvider.now();\n            var last = 0;\n            for (var i = 1; i < _buffer.length && _buffer[i] <= now; i += 2) {\n                last = i;\n            }\n            last && _buffer.splice(0, last + 1);\n        }\n    };\n    return ReplaySubject;\n}(Subject));\nexport { ReplaySubject };\n//# sourceMappingURL=ReplaySubject.js.map", "import { __extends } from \"tslib\";\nimport { Subscription } from '../Subscription';\nvar Action = (function (_super) {\n    __extends(Action, _super);\n    function Action(scheduler, work) {\n        return _super.call(this) || this;\n    }\n    Action.prototype.schedule = function (state, delay) {\n        if (delay === void 0) { delay = 0; }\n        return this;\n    };\n    return Action;\n}(Subscription));\nexport { Action };\n//# sourceMappingURL=Action.js.map", "import { __read, __spreadArray } from \"tslib\";\nexport var intervalProvider = {\n    setInterval: function (handler, timeout) {\n        var args = [];\n        for (var _i = 2; _i < arguments.length; _i++) {\n            args[_i - 2] = arguments[_i];\n        }\n        var delegate = intervalProvider.delegate;\n        if (delegate === null || delegate === void 0 ? void 0 : delegate.setInterval) {\n            return delegate.setInterval.apply(delegate, __spreadArray([handler, timeout], __read(args)));\n        }\n        return setInterval.apply(void 0, __spreadArray([handler, timeout], __read(args)));\n    },\n    clearInterval: function (handle) {\n        var delegate = intervalProvider.delegate;\n        return ((delegate === null || delegate === void 0 ? void 0 : delegate.clearInterval) || clearInterval)(handle);\n    },\n    delegate: undefined,\n};\n//# sourceMappingURL=intervalProvider.js.map", "import { __extends } from \"tslib\";\nimport { Action } from './Action';\nimport { intervalProvider } from './intervalProvider';\nimport { arrRemove } from '../util/arrRemove';\nvar AsyncAction = (function (_super) {\n    __extends(AsyncAction, _super);\n    function AsyncAction(scheduler, work) {\n        var _this = _super.call(this, scheduler, work) || this;\n        _this.scheduler = scheduler;\n        _this.work = work;\n        _this.pending = false;\n        return _this;\n    }\n    AsyncAction.prototype.schedule = function (state, delay) {\n        var _a;\n        if (delay === void 0) { delay = 0; }\n        if (this.closed) {\n            return this;\n        }\n        this.state = state;\n        var id = this.id;\n        var scheduler = this.scheduler;\n        if (id != null) {\n            this.id = this.recycleAsyncId(scheduler, id, delay);\n        }\n        this.pending = true;\n        this.delay = delay;\n        this.id = (_a = this.id) !== null && _a !== void 0 ? _a : this.requestAsyncId(scheduler, this.id, delay);\n        return this;\n    };\n    AsyncAction.prototype.requestAsyncId = function (scheduler, _id, delay) {\n        if (delay === void 0) { delay = 0; }\n        return intervalProvider.setInterval(scheduler.flush.bind(scheduler, this), delay);\n    };\n    AsyncAction.prototype.recycleAsyncId = function (_scheduler, id, delay) {\n        if (delay === void 0) { delay = 0; }\n        if (delay != null && this.delay === delay && this.pending === false) {\n            return id;\n        }\n        if (id != null) {\n            intervalProvider.clearInterval(id);\n        }\n        return undefined;\n    };\n    AsyncAction.prototype.execute = function (state, delay) {\n        if (this.closed) {\n            return new Error('executing a cancelled action');\n        }\n        this.pending = false;\n        var error = this._execute(state, delay);\n        if (error) {\n            return error;\n        }\n        else if (this.pending === false && this.id != null) {\n            this.id = this.recycleAsyncId(this.scheduler, this.id, null);\n        }\n    };\n    AsyncAction.prototype._execute = function (state, _delay) {\n        var errored = false;\n        var errorValue;\n        try {\n            this.work(state);\n        }\n        catch (e) {\n            errored = true;\n            errorValue = e ? e : new Error('Scheduled action threw falsy error');\n        }\n        if (errored) {\n            this.unsubscribe();\n            return errorValue;\n        }\n    };\n    AsyncAction.prototype.unsubscribe = function () {\n        if (!this.closed) {\n            var _a = this, id = _a.id, scheduler = _a.scheduler;\n            var actions = scheduler.actions;\n            this.work = this.state = this.scheduler = null;\n            this.pending = false;\n            arrRemove(actions, this);\n            if (id != null) {\n                this.id = this.recycleAsyncId(scheduler, id, null);\n            }\n            this.delay = null;\n            _super.prototype.unsubscribe.call(this);\n        }\n    };\n    return AsyncAction;\n}(Action));\nexport { AsyncAction };\n//# sourceMappingURL=AsyncAction.js.map", "import { dateTimestampProvider } from './scheduler/dateTimestampProvider';\nvar Scheduler = (function () {\n    function Scheduler(schedulerActionCtor, now) {\n        if (now === void 0) { now = Scheduler.now; }\n        this.schedulerActionCtor = schedulerActionCtor;\n        this.now = now;\n    }\n    Scheduler.prototype.schedule = function (work, delay, state) {\n        if (delay === void 0) { delay = 0; }\n        return new this.schedulerActionCtor(this, work).schedule(state, delay);\n    };\n    Scheduler.now = dateTimestampProvider.now;\n    return Scheduler;\n}());\nexport { Scheduler };\n//# sourceMappingURL=Scheduler.js.map", "import { __extends } from \"tslib\";\nimport { Scheduler } from '../Scheduler';\nvar AsyncScheduler = (function (_super) {\n    __extends(AsyncScheduler, _super);\n    function AsyncScheduler(SchedulerAction, now) {\n        if (now === void 0) { now = Scheduler.now; }\n        var _this = _super.call(this, SchedulerAction, now) || this;\n        _this.actions = [];\n        _this._active = false;\n        return _this;\n    }\n    AsyncScheduler.prototype.flush = function (action) {\n        var actions = this.actions;\n        if (this._active) {\n            actions.push(action);\n            return;\n        }\n        var error;\n        this._active = true;\n        do {\n            if ((error = action.execute(action.state, action.delay))) {\n                break;\n            }\n        } while ((action = actions.shift()));\n        this._active = false;\n        if (error) {\n            while ((action = actions.shift())) {\n                action.unsubscribe();\n            }\n            throw error;\n        }\n    };\n    return AsyncScheduler;\n}(Scheduler));\nexport { AsyncScheduler };\n//# sourceMappingURL=AsyncScheduler.js.map", "import { AsyncAction } from './AsyncAction';\nimport { AsyncScheduler } from './AsyncScheduler';\nexport var asyncScheduler = new AsyncScheduler(AsyncAction);\nexport var async = asyncScheduler;\n//# sourceMappingURL=async.js.map", "import { Observable } from '../Observable';\nexport var EMPTY = new Observable(function (subscriber) { return subscriber.complete(); });\nexport function empty(scheduler) {\n    return scheduler ? emptyScheduled(scheduler) : EMPTY;\n}\nfunction emptyScheduled(scheduler) {\n    return new Observable(function (subscriber) { return scheduler.schedule(function () { return subscriber.complete(); }); });\n}\n//# sourceMappingURL=empty.js.map", "import { isFunction } from './isFunction';\nexport function isScheduler(value) {\n    return value && isFunction(value.schedule);\n}\n//# sourceMappingURL=isScheduler.js.map", "import { isFunction } from './isFunction';\nimport { isScheduler } from './isScheduler';\nfunction last(arr) {\n    return arr[arr.length - 1];\n}\nexport function popResultSelector(args) {\n    return isFunction(last(args)) ? args.pop() : undefined;\n}\nexport function popScheduler(args) {\n    return isScheduler(last(args)) ? args.pop() : undefined;\n}\nexport function popNumber(args, defaultValue) {\n    return typeof last(args) === 'number' ? args.pop() : defaultValue;\n}\n//# sourceMappingURL=args.js.map", "export var isArrayLike = (function (x) { return x && typeof x.length === 'number' && typeof x !== 'function'; });\n//# sourceMappingURL=isArrayLike.js.map", "import { isFunction } from \"./isFunction\";\nexport function isPromise(value) {\n    return isFunction(value === null || value === void 0 ? void 0 : value.then);\n}\n//# sourceMappingURL=isPromise.js.map", "import { observable as Symbol_observable } from '../symbol/observable';\nimport { isFunction } from './isFunction';\nexport function isInteropObservable(input) {\n    return isFunction(input[Symbol_observable]);\n}\n//# sourceMappingURL=isInteropObservable.js.map", "import { isFunction } from './isFunction';\nexport function isAsyncIterable(obj) {\n    return Symbol.asyncIterator && isFunction(obj === null || obj === void 0 ? void 0 : obj[Symbol.asyncIterator]);\n}\n//# sourceMappingURL=isAsyncIterable.js.map", "export function createInvalidObservableTypeError(input) {\n    return new TypeError(\"You provided \" + (input !== null && typeof input === 'object' ? 'an invalid object' : \"'\" + input + \"'\") + \" where a stream was expected. You can provide an Observable, Promise, ReadableStream, Array, AsyncIterable, or Iterable.\");\n}\n//# sourceMappingURL=throwUnobservableError.js.map", "export function getSymbolIterator() {\n    if (typeof Symbol !== 'function' || !Symbol.iterator) {\n        return '@@iterator';\n    }\n    return Symbol.iterator;\n}\nexport var iterator = getSymbolIterator();\n//# sourceMappingURL=iterator.js.map", "import { iterator as Symbol_iterator } from '../symbol/iterator';\nimport { isFunction } from './isFunction';\nexport function isIterable(input) {\n    return isFunction(input === null || input === void 0 ? void 0 : input[Symbol_iterator]);\n}\n//# sourceMappingURL=isIterable.js.map", "import { __asyncGenerator, __await, __generator } from \"tslib\";\nimport { isFunction } from './isFunction';\nexport function readableStreamLikeToAsyncGenerator(readableStream) {\n    return __asyncGenerator(this, arguments, function readableStreamLikeToAsyncGenerator_1() {\n        var reader, _a, value, done;\n        return __generator(this, function (_b) {\n            switch (_b.label) {\n                case 0:\n                    reader = readableStream.getReader();\n                    _b.label = 1;\n                case 1:\n                    _b.trys.push([1, , 9, 10]);\n                    _b.label = 2;\n                case 2:\n                    if (!true) return [3, 8];\n                    return [4, __await(reader.read())];\n                case 3:\n                    _a = _b.sent(), value = _a.value, done = _a.done;\n                    if (!done) return [3, 5];\n                    return [4, __await(void 0)];\n                case 4: return [2, _b.sent()];\n                case 5: return [4, __await(value)];\n                case 6: return [4, _b.sent()];\n                case 7:\n                    _b.sent();\n                    return [3, 2];\n                case 8: return [3, 10];\n                case 9:\n                    reader.releaseLock();\n                    return [7];\n                case 10: return [2];\n            }\n        });\n    });\n}\nexport function isReadableStreamLike(obj) {\n    return isFunction(obj === null || obj === void 0 ? void 0 : obj.getReader);\n}\n//# sourceMappingURL=isReadableStreamLike.js.map", "import { __asyncValues, __awaiter, __generator, __values } from \"tslib\";\nimport { isArrayLike } from '../util/isArrayLike';\nimport { isPromise } from '../util/isPromise';\nimport { Observable } from '../Observable';\nimport { isInteropObservable } from '../util/isInteropObservable';\nimport { isAsyncIterable } from '../util/isAsyncIterable';\nimport { createInvalidObservableTypeError } from '../util/throwUnobservableError';\nimport { isIterable } from '../util/isIterable';\nimport { isReadableStreamLike, readableStreamLikeToAsyncGenerator } from '../util/isReadableStreamLike';\nimport { isFunction } from '../util/isFunction';\nimport { reportUnhandledError } from '../util/reportUnhandledError';\nimport { observable as Symbol_observable } from '../symbol/observable';\nexport function innerFrom(input) {\n    if (input instanceof Observable) {\n        return input;\n    }\n    if (input != null) {\n        if (isInteropObservable(input)) {\n            return fromInteropObservable(input);\n        }\n        if (isArrayLike(input)) {\n            return fromArrayLike(input);\n        }\n        if (isPromise(input)) {\n            return fromPromise(input);\n        }\n        if (isAsyncIterable(input)) {\n            return fromAsyncIterable(input);\n        }\n        if (isIterable(input)) {\n            return fromIterable(input);\n        }\n        if (isReadableStreamLike(input)) {\n            return fromReadableStreamLike(input);\n        }\n    }\n    throw createInvalidObservableTypeError(input);\n}\nexport function fromInteropObservable(obj) {\n    return new Observable(function (subscriber) {\n        var obs = obj[Symbol_observable]();\n        if (isFunction(obs.subscribe)) {\n            return obs.subscribe(subscriber);\n        }\n        throw new TypeError('Provided object does not correctly implement Symbol.observable');\n    });\n}\nexport function fromArrayLike(array) {\n    return new Observable(function (subscriber) {\n        for (var i = 0; i < array.length && !subscriber.closed; i++) {\n            subscriber.next(array[i]);\n        }\n        subscriber.complete();\n    });\n}\nexport function fromPromise(promise) {\n    return new Observable(function (subscriber) {\n        promise\n            .then(function (value) {\n            if (!subscriber.closed) {\n                subscriber.next(value);\n                subscriber.complete();\n            }\n        }, function (err) { return subscriber.error(err); })\n            .then(null, reportUnhandledError);\n    });\n}\nexport function fromIterable(iterable) {\n    return new Observable(function (subscriber) {\n        var e_1, _a;\n        try {\n            for (var iterable_1 = __values(iterable), iterable_1_1 = iterable_1.next(); !iterable_1_1.done; iterable_1_1 = iterable_1.next()) {\n                var value = iterable_1_1.value;\n                subscriber.next(value);\n                if (subscriber.closed) {\n                    return;\n                }\n            }\n        }\n        catch (e_1_1) { e_1 = { error: e_1_1 }; }\n        finally {\n            try {\n                if (iterable_1_1 && !iterable_1_1.done && (_a = iterable_1.return)) _a.call(iterable_1);\n            }\n            finally { if (e_1) throw e_1.error; }\n        }\n        subscriber.complete();\n    });\n}\nexport function fromAsyncIterable(asyncIterable) {\n    return new Observable(function (subscriber) {\n        process(asyncIterable, subscriber).catch(function (err) { return subscriber.error(err); });\n    });\n}\nexport function fromReadableStreamLike(readableStream) {\n    return fromAsyncIterable(readableStreamLikeToAsyncGenerator(readableStream));\n}\nfunction process(asyncIterable, subscriber) {\n    var asyncIterable_1, asyncIterable_1_1;\n    var e_2, _a;\n    return __awaiter(this, void 0, void 0, function () {\n        var value, e_2_1;\n        return __generator(this, function (_b) {\n            switch (_b.label) {\n                case 0:\n                    _b.trys.push([0, 5, 6, 11]);\n                    asyncIterable_1 = __asyncValues(asyncIterable);\n                    _b.label = 1;\n                case 1: return [4, asyncIterable_1.next()];\n                case 2:\n                    if (!(asyncIterable_1_1 = _b.sent(), !asyncIterable_1_1.done)) return [3, 4];\n                    value = asyncIterable_1_1.value;\n                    subscriber.next(value);\n                    if (subscriber.closed) {\n                        return [2];\n                    }\n                    _b.label = 3;\n                case 3: return [3, 1];\n                case 4: return [3, 11];\n                case 5:\n                    e_2_1 = _b.sent();\n                    e_2 = { error: e_2_1 };\n                    return [3, 11];\n                case 6:\n                    _b.trys.push([6, , 9, 10]);\n                    if (!(asyncIterable_1_1 && !asyncIterable_1_1.done && (_a = asyncIterable_1.return))) return [3, 8];\n                    return [4, _a.call(asyncIterable_1)];\n                case 7:\n                    _b.sent();\n                    _b.label = 8;\n                case 8: return [3, 10];\n                case 9:\n                    if (e_2) throw e_2.error;\n                    return [7];\n                case 10: return [7];\n                case 11:\n                    subscriber.complete();\n                    return [2];\n            }\n        });\n    });\n}\n//# sourceMappingURL=innerFrom.js.map", "export function executeSchedule(parentSubscription, scheduler, work, delay, repeat) {\n    if (delay === void 0) { delay = 0; }\n    if (repeat === void 0) { repeat = false; }\n    var scheduleSubscription = scheduler.schedule(function () {\n        work();\n        if (repeat) {\n            parentSubscription.add(this.schedule(null, delay));\n        }\n        else {\n            this.unsubscribe();\n        }\n    }, delay);\n    parentSubscription.add(scheduleSubscription);\n    if (!repeat) {\n        return scheduleSubscription;\n    }\n}\n//# sourceMappingURL=executeSchedule.js.map", "import { executeSchedule } from '../util/executeSchedule';\nimport { operate } from '../util/lift';\nimport { createOperatorSubscriber } from './OperatorSubscriber';\nexport function observeOn(scheduler, delay) {\n    if (delay === void 0) { delay = 0; }\n    return operate(function (source, subscriber) {\n        source.subscribe(createOperatorSubscriber(subscriber, function (value) { return executeSchedule(subscriber, scheduler, function () { return subscriber.next(value); }, delay); }, function () { return executeSchedule(subscriber, scheduler, function () { return subscriber.complete(); }, delay); }, function (err) { return executeSchedule(subscriber, scheduler, function () { return subscriber.error(err); }, delay); }));\n    });\n}\n//# sourceMappingURL=observeOn.js.map", "import { operate } from '../util/lift';\nexport function subscribeOn(scheduler, delay) {\n    if (delay === void 0) { delay = 0; }\n    return operate(function (source, subscriber) {\n        subscriber.add(scheduler.schedule(function () { return source.subscribe(subscriber); }, delay));\n    });\n}\n//# sourceMappingURL=subscribeOn.js.map", "import { innerFrom } from '../observable/innerFrom';\nimport { observeOn } from '../operators/observeOn';\nimport { subscribeOn } from '../operators/subscribeOn';\nexport function scheduleObservable(input, scheduler) {\n    return innerFrom(input).pipe(subscribeOn(scheduler), observeOn(scheduler));\n}\n//# sourceMappingURL=scheduleObservable.js.map", "import { innerFrom } from '../observable/innerFrom';\nimport { observeOn } from '../operators/observeOn';\nimport { subscribeOn } from '../operators/subscribeOn';\nexport function schedulePromise(input, scheduler) {\n    return innerFrom(input).pipe(subscribeOn(scheduler), observeOn(scheduler));\n}\n//# sourceMappingURL=schedulePromise.js.map", "import { Observable } from '../Observable';\nexport function scheduleArray(input, scheduler) {\n    return new Observable(function (subscriber) {\n        var i = 0;\n        return scheduler.schedule(function () {\n            if (i === input.length) {\n                subscriber.complete();\n            }\n            else {\n                subscriber.next(input[i++]);\n                if (!subscriber.closed) {\n                    this.schedule();\n                }\n            }\n        });\n    });\n}\n//# sourceMappingURL=scheduleArray.js.map", "import { Observable } from '../Observable';\nimport { iterator as Symbol_iterator } from '../symbol/iterator';\nimport { isFunction } from '../util/isFunction';\nimport { executeSchedule } from '../util/executeSchedule';\nexport function scheduleIterable(input, scheduler) {\n    return new Observable(function (subscriber) {\n        var iterator;\n        executeSchedule(subscriber, scheduler, function () {\n            iterator = input[Symbol_iterator]();\n            executeSchedule(subscriber, scheduler, function () {\n                var _a;\n                var value;\n                var done;\n                try {\n                    (_a = iterator.next(), value = _a.value, done = _a.done);\n                }\n                catch (err) {\n                    subscriber.error(err);\n                    return;\n                }\n                if (done) {\n                    subscriber.complete();\n                }\n                else {\n                    subscriber.next(value);\n                }\n            }, 0, true);\n        });\n        return function () { return isFunction(iterator === null || iterator === void 0 ? void 0 : iterator.return) && iterator.return(); };\n    });\n}\n//# sourceMappingURL=scheduleIterable.js.map", "import { Observable } from '../Observable';\nimport { executeSchedule } from '../util/executeSchedule';\nexport function scheduleAsyncIterable(input, scheduler) {\n    if (!input) {\n        throw new Error('Iterable cannot be null');\n    }\n    return new Observable(function (subscriber) {\n        executeSchedule(subscriber, scheduler, function () {\n            var iterator = input[Symbol.asyncIterator]();\n            executeSchedule(subscriber, scheduler, function () {\n                iterator.next().then(function (result) {\n                    if (result.done) {\n                        subscriber.complete();\n                    }\n                    else {\n                        subscriber.next(result.value);\n                    }\n                });\n            }, 0, true);\n        });\n    });\n}\n//# sourceMappingURL=scheduleAsyncIterable.js.map", "import { scheduleAsyncIterable } from './scheduleAsyncIterable';\nimport { readableStreamLikeToAsyncGenerator } from '../util/isReadableStreamLike';\nexport function scheduleReadableStreamLike(input, scheduler) {\n    return scheduleAsyncIterable(readableStreamLikeToAsyncGenerator(input), scheduler);\n}\n//# sourceMappingURL=scheduleReadableStreamLike.js.map", "import { scheduleObservable } from './scheduleObservable';\nimport { schedulePromise } from './schedulePromise';\nimport { scheduleArray } from './scheduleArray';\nimport { scheduleIterable } from './scheduleIterable';\nimport { scheduleAsyncIterable } from './scheduleAsyncIterable';\nimport { isInteropObservable } from '../util/isInteropObservable';\nimport { isPromise } from '../util/isPromise';\nimport { isArrayLike } from '../util/isArrayLike';\nimport { isIterable } from '../util/isIterable';\nimport { isAsyncIterable } from '../util/isAsyncIterable';\nimport { createInvalidObservableTypeError } from '../util/throwUnobservableError';\nimport { isReadableStreamLike } from '../util/isReadableStreamLike';\nimport { scheduleReadableStreamLike } from './scheduleReadableStreamLike';\nexport function scheduled(input, scheduler) {\n    if (input != null) {\n        if (isInteropObservable(input)) {\n            return scheduleObservable(input, scheduler);\n        }\n        if (isArrayLike(input)) {\n            return scheduleArray(input, scheduler);\n        }\n        if (isPromise(input)) {\n            return schedulePromise(input, scheduler);\n        }\n        if (isAsyncIterable(input)) {\n            return scheduleAsyncIterable(input, scheduler);\n        }\n        if (isIterable(input)) {\n            return scheduleIterable(input, scheduler);\n        }\n        if (isReadableStreamLike(input)) {\n            return scheduleReadableStreamLike(input, scheduler);\n        }\n    }\n    throw createInvalidObservableTypeError(input);\n}\n//# sourceMappingURL=scheduled.js.map", "import { scheduled } from '../scheduled/scheduled';\nimport { innerFrom } from './innerFrom';\nexport function from(input, scheduler) {\n    return scheduler ? scheduled(input, scheduler) : innerFrom(input);\n}\n//# sourceMappingURL=from.js.map", "import { popScheduler } from '../util/args';\nimport { from } from './from';\nexport function of() {\n    var args = [];\n    for (var _i = 0; _i < arguments.length; _i++) {\n        args[_i] = arguments[_i];\n    }\n    var scheduler = popScheduler(args);\n    return from(args, scheduler);\n}\n//# sourceMappingURL=of.js.map", "import { Observable } from '../Observable';\nimport { isFunction } from '../util/isFunction';\nexport function throwError(errorOrErrorFactory, scheduler) {\n    var errorFactory = isFunction(errorOrErrorFactory) ? errorOrErrorFactory : function () { return errorOrErrorFactory; };\n    var init = function (subscriber) { return subscriber.error(errorFactory()); };\n    return new Observable(scheduler ? function (subscriber) { return scheduler.schedule(init, 0, subscriber); } : init);\n}\n//# sourceMappingURL=throwError.js.map", "import { Observable } from '../Observable';\nimport { isFunction } from './isFunction';\nexport function isObservable(obj) {\n    return !!obj && (obj instanceof Observable || (isFunction(obj.lift) && isFunction(obj.subscribe)));\n}\n//# sourceMappingURL=isObservable.js.map", "import { createErrorClass } from './createErrorClass';\nexport var EmptyError = createErrorClass(function (_super) {\n    return function EmptyErrorImpl() {\n        _super(this);\n        this.name = 'EmptyError';\n        this.message = 'no elements in sequence';\n    };\n});\n//# sourceMappingURL=EmptyError.js.map", "import { EmptyError } from './util/EmptyError';\nexport function lastValueFrom(source, config) {\n    var hasConfig = typeof config === 'object';\n    return new Promise(function (resolve, reject) {\n        var _hasValue = false;\n        var _value;\n        source.subscribe({\n            next: function (value) {\n                _value = value;\n                _hasValue = true;\n            },\n            error: reject,\n            complete: function () {\n                if (_hasValue) {\n                    resolve(_value);\n                }\n                else if (hasConfig) {\n                    resolve(config.defaultValue);\n                }\n                else {\n                    reject(new EmptyError());\n                }\n            },\n        });\n    });\n}\n//# sourceMappingURL=lastValueFrom.js.map", "import { EmptyError } from './util/EmptyError';\nimport { SafeSubscriber } from './Subscriber';\nexport function firstValueFrom(source, config) {\n    var hasConfig = typeof config === 'object';\n    return new Promise(function (resolve, reject) {\n        var subscriber = new SafeSubscriber({\n            next: function (value) {\n                resolve(value);\n                subscriber.unsubscribe();\n            },\n            error: reject,\n            complete: function () {\n                if (hasConfig) {\n                    resolve(config.defaultValue);\n                }\n                else {\n                    reject(new EmptyError());\n                }\n            },\n        });\n        source.subscribe(subscriber);\n    });\n}\n//# sourceMappingURL=firstValueFrom.js.map", "export function isValidDate(value) {\n    return value instanceof Date && !isNaN(value);\n}\n//# sourceMappingURL=isDate.js.map", "import { operate } from '../util/lift';\nimport { createOperatorSubscriber } from './OperatorSubscriber';\nexport function map(project, thisArg) {\n    return operate(function (source, subscriber) {\n        var index = 0;\n        source.subscribe(createOperatorSubscriber(subscriber, function (value) {\n            subscriber.next(project.call(thisArg, value, index++));\n        }));\n    });\n}\n//# sourceMappingURL=map.js.map", "import { __read, __spreadArray } from \"tslib\";\nimport { map } from \"../operators/map\";\nvar isArray = Array.isArray;\nfunction callOrApply(fn, args) {\n    return isArray(args) ? fn.apply(void 0, __spreadArray([], __read(args))) : fn(args);\n}\nexport function mapOneOrManyArgs(fn) {\n    return map(function (args) { return callOrApply(fn, args); });\n}\n//# sourceMappingURL=mapOneOrManyArgs.js.map", "import { Observable } from '../Observable';\nimport { argsArgArrayOrObject } from '../util/argsArgArrayOrObject';\nimport { from } from './from';\nimport { identity } from '../util/identity';\nimport { mapOneOrManyArgs } from '../util/mapOneOrManyArgs';\nimport { popResultSelector, popScheduler } from '../util/args';\nimport { createObject } from '../util/createObject';\nimport { createOperatorSubscriber } from '../operators/OperatorSubscriber';\nimport { executeSchedule } from '../util/executeSchedule';\nexport function combineLatest() {\n    var args = [];\n    for (var _i = 0; _i < arguments.length; _i++) {\n        args[_i] = arguments[_i];\n    }\n    var scheduler = popScheduler(args);\n    var resultSelector = popResultSelector(args);\n    var _a = argsArgArrayOrObject(args), observables = _a.args, keys = _a.keys;\n    if (observables.length === 0) {\n        return from([], scheduler);\n    }\n    var result = new Observable(combineLatestInit(observables, scheduler, keys\n        ?\n            function (values) { return createObject(keys, values); }\n        :\n            identity));\n    return resultSelector ? result.pipe(mapOneOrManyArgs(resultSelector)) : result;\n}\nexport function combineLatestInit(observables, scheduler, valueTransform) {\n    if (valueTransform === void 0) { valueTransform = identity; }\n    return function (subscriber) {\n        maybeSchedule(scheduler, function () {\n            var length = observables.length;\n            var values = new Array(length);\n            var active = length;\n            var remainingFirstValues = length;\n            var _loop_1 = function (i) {\n                maybeSchedule(scheduler, function () {\n                    var source = from(observables[i], scheduler);\n                    var hasFirstValue = false;\n                    source.subscribe(createOperatorSubscriber(subscriber, function (value) {\n                        values[i] = value;\n                        if (!hasFirstValue) {\n                            hasFirstValue = true;\n                            remainingFirstValues--;\n                        }\n                        if (!remainingFirstValues) {\n                            subscriber.next(valueTransform(values.slice()));\n                        }\n                    }, function () {\n                        if (!--active) {\n                            subscriber.complete();\n                        }\n                    }));\n                }, subscriber);\n            };\n            for (var i = 0; i < length; i++) {\n                _loop_1(i);\n            }\n        }, subscriber);\n    };\n}\nfunction maybeSchedule(scheduler, execute, subscription) {\n    if (scheduler) {\n        executeSchedule(subscription, scheduler, execute);\n    }\n    else {\n        execute();\n    }\n}\n//# sourceMappingURL=combineLatest.js.map", "import { innerFrom } from '../observable/innerFrom';\nimport { executeSchedule } from '../util/executeSchedule';\nimport { createOperatorSubscriber } from './OperatorSubscriber';\nexport function mergeInternals(source, subscriber, project, concurrent, onBeforeNext, expand, innerSubScheduler, additionalFinalizer) {\n    var buffer = [];\n    var active = 0;\n    var index = 0;\n    var isComplete = false;\n    var checkComplete = function () {\n        if (isComplete && !buffer.length && !active) {\n            subscriber.complete();\n        }\n    };\n    var outerNext = function (value) { return (active < concurrent ? doInnerSub(value) : buffer.push(value)); };\n    var doInnerSub = function (value) {\n        expand && subscriber.next(value);\n        active++;\n        var innerComplete = false;\n        innerFrom(project(value, index++)).subscribe(createOperatorSubscriber(subscriber, function (innerValue) {\n            onBeforeNext === null || onBeforeNext === void 0 ? void 0 : onBeforeNext(innerValue);\n            if (expand) {\n                outerNext(innerValue);\n            }\n            else {\n                subscriber.next(innerValue);\n            }\n        }, function () {\n            innerComplete = true;\n        }, undefined, function () {\n            if (innerComplete) {\n                try {\n                    active--;\n                    var _loop_1 = function () {\n                        var bufferedValue = buffer.shift();\n                        if (innerSubScheduler) {\n                            executeSchedule(subscriber, innerSubScheduler, function () { return doInnerSub(bufferedValue); });\n                        }\n                        else {\n                            doInnerSub(bufferedValue);\n                        }\n                    };\n                    while (buffer.length && active < concurrent) {\n                        _loop_1();\n                    }\n                    checkComplete();\n                }\n                catch (err) {\n                    subscriber.error(err);\n                }\n            }\n        }));\n    };\n    source.subscribe(createOperatorSubscriber(subscriber, outerNext, function () {\n        isComplete = true;\n        checkComplete();\n    }));\n    return function () {\n        additionalFinalizer === null || additionalFinalizer === void 0 ? void 0 : additionalFinalizer();\n    };\n}\n//# sourceMappingURL=mergeInternals.js.map", "import { map } from './map';\nimport { innerFrom } from '../observable/innerFrom';\nimport { operate } from '../util/lift';\nimport { mergeInternals } from './mergeInternals';\nimport { isFunction } from '../util/isFunction';\nexport function mergeMap(project, resultSelector, concurrent) {\n    if (concurrent === void 0) { concurrent = Infinity; }\n    if (isFunction(resultSelector)) {\n        return mergeMap(function (a, i) { return map(function (b, ii) { return resultSelector(a, b, i, ii); })(innerFrom(project(a, i))); }, concurrent);\n    }\n    else if (typeof resultSelector === 'number') {\n        concurrent = resultSelector;\n    }\n    return operate(function (source, subscriber) { return mergeInternals(source, subscriber, project, concurrent); });\n}\n//# sourceMappingURL=mergeMap.js.map", "import { mergeMap } from './mergeMap';\nimport { identity } from '../util/identity';\nexport function mergeAll(concurrent) {\n    if (concurrent === void 0) { concurrent = Infinity; }\n    return mergeMap(identity, concurrent);\n}\n//# sourceMappingURL=mergeAll.js.map", "import { mergeAll } from './mergeAll';\nexport function concatAll() {\n    return mergeAll(1);\n}\n//# sourceMappingURL=concatAll.js.map", "import { concatAll } from '../operators/concatAll';\nimport { popScheduler } from '../util/args';\nimport { from } from './from';\nexport function concat() {\n    var args = [];\n    for (var _i = 0; _i < arguments.length; _i++) {\n        args[_i] = arguments[_i];\n    }\n    return concatAll()(from(args, popScheduler(args)));\n}\n//# sourceMappingURL=concat.js.map", "import { Observable } from '../Observable';\nimport { innerFrom } from './innerFrom';\nexport function defer(observableFactory) {\n    return new Observable(function (subscriber) {\n        innerFrom(observableFactory()).subscribe(subscriber);\n    });\n}\n//# sourceMappingURL=defer.js.map", "import { Observable } from '../Observable';\nimport { async as asyncScheduler } from '../scheduler/async';\nimport { isScheduler } from '../util/isScheduler';\nimport { isValidDate } from '../util/isDate';\nexport function timer(dueTime, intervalOrScheduler, scheduler) {\n    if (dueTime === void 0) { dueTime = 0; }\n    if (scheduler === void 0) { scheduler = asyncScheduler; }\n    var intervalDuration = -1;\n    if (intervalOrScheduler != null) {\n        if (isScheduler(intervalOrScheduler)) {\n            scheduler = intervalOrScheduler;\n        }\n        else {\n            intervalDuration = intervalOrScheduler;\n        }\n    }\n    return new Observable(function (subscriber) {\n        var due = isValidDate(dueTime) ? +dueTime - scheduler.now() : dueTime;\n        if (due < 0) {\n            due = 0;\n        }\n        var n = 0;\n        return scheduler.schedule(function () {\n            if (!subscriber.closed) {\n                subscriber.next(n++);\n                if (0 <= intervalDuration) {\n                    this.schedule(undefined, intervalDuration);\n                }\n                else {\n                    subscriber.complete();\n                }\n            }\n        }, due);\n    });\n}\n//# sourceMappingURL=timer.js.map", "import { mergeAll } from '../operators/mergeAll';\nimport { innerFrom } from './innerFrom';\nimport { EMPTY } from './empty';\nimport { popNumber, popScheduler } from '../util/args';\nimport { from } from './from';\nexport function merge() {\n    var args = [];\n    for (var _i = 0; _i < arguments.length; _i++) {\n        args[_i] = arguments[_i];\n    }\n    var scheduler = popScheduler(args);\n    var concurrent = popNumber(args, Infinity);\n    var sources = args;\n    return !sources.length\n        ?\n            EMPTY\n        : sources.length === 1\n            ?\n                innerFrom(sources[0])\n            :\n                mergeAll(concurrent)(from(sources, scheduler));\n}\n//# sourceMappingURL=merge.js.map", "var isArray = Array.isArray;\nexport function argsOrArgArray(args) {\n    return args.length === 1 && isArray(args[0]) ? args[0] : args;\n}\n//# sourceMappingURL=argsOrArgArray.js.map", "import { operate } from '../util/lift';\nimport { createOperatorSubscriber } from './OperatorSubscriber';\nexport function filter(predicate, thisArg) {\n    return operate(function (source, subscriber) {\n        var index = 0;\n        source.subscribe(createOperatorSubscriber(subscriber, function (value) { return predicate.call(thisArg, value, index++) && subscriber.next(value); }));\n    });\n}\n//# sourceMappingURL=filter.js.map", "import { innerFrom } from '../observable/innerFrom';\nimport { createOperatorSubscriber } from './OperatorSubscriber';\nimport { operate } from '../util/lift';\nexport function catchError(selector) {\n    return operate(function (source, subscriber) {\n        var innerSub = null;\n        var syncUnsub = false;\n        var handledResult;\n        innerSub = source.subscribe(createOperatorSubscriber(subscriber, undefined, undefined, function (err) {\n            handledResult = innerFrom(selector(err, catchError(selector)(source)));\n            if (innerSub) {\n                innerSub.unsubscribe();\n                innerSub = null;\n                handledResult.subscribe(subscriber);\n            }\n            else {\n                syncUnsub = true;\n            }\n        }));\n        if (syncUnsub) {\n            innerSub.unsubscribe();\n            innerSub = null;\n            handledResult.subscribe(subscriber);\n        }\n    });\n}\n//# sourceMappingURL=catchError.js.map", "import { __read, __spreadArray } from \"tslib\";\nimport { combineLatestInit } from '../observable/combineLatest';\nimport { operate } from '../util/lift';\nimport { argsOrArgArray } from '../util/argsOrArgArray';\nimport { mapOneOrManyArgs } from '../util/mapOneOrManyArgs';\nimport { pipe } from '../util/pipe';\nimport { popResultSelector } from '../util/args';\nexport function combineLatest() {\n    var args = [];\n    for (var _i = 0; _i < arguments.length; _i++) {\n        args[_i] = arguments[_i];\n    }\n    var resultSelector = popResultSelector(args);\n    return resultSelector\n        ? pipe(combineLatest.apply(void 0, __spreadArray([], __read(args))), mapOneOrManyArgs(resultSelector))\n        : operate(function (source, subscriber) {\n            combineLatestInit(__spreadArray([source], __read(argsOrArgArray(args))))(subscriber);\n        });\n}\n//# sourceMappingURL=combineLatest.js.map", "import { __read, __spreadArray } from \"tslib\";\nimport { combineLatest } from './combineLatest';\nexport function combineLatestWith() {\n    var otherSources = [];\n    for (var _i = 0; _i < arguments.length; _i++) {\n        otherSources[_i] = arguments[_i];\n    }\n    return combineLatest.apply(void 0, __spreadArray([], __read(otherSources)));\n}\n//# sourceMappingURL=combineLatestWith.js.map", "import { operate } from '../util/lift';\nexport function finalize(callback) {\n    return operate(function (source, subscriber) {\n        try {\n            source.subscribe(subscriber);\n        }\n        finally {\n            subscriber.add(callback);\n        }\n    });\n}\n//# sourceMappingURL=finalize.js.map", "import { __read, __spreadArray } from \"tslib\";\nimport { innerFrom } from '../observable/innerFrom';\nimport { Subject } from '../Subject';\nimport { SafeSubscriber } from '../Subscriber';\nimport { operate } from '../util/lift';\nexport function share(options) {\n    if (options === void 0) { options = {}; }\n    var _a = options.connector, connector = _a === void 0 ? function () { return new Subject(); } : _a, _b = options.resetOnError, resetOnError = _b === void 0 ? true : _b, _c = options.resetOnComplete, resetOnComplete = _c === void 0 ? true : _c, _d = options.resetOnRefCountZero, resetOnRefCountZero = _d === void 0 ? true : _d;\n    return function (wrapperSource) {\n        var connection;\n        var resetConnection;\n        var subject;\n        var refCount = 0;\n        var hasCompleted = false;\n        var hasErrored = false;\n        var cancelReset = function () {\n            resetConnection === null || resetConnection === void 0 ? void 0 : resetConnection.unsubscribe();\n            resetConnection = undefined;\n        };\n        var reset = function () {\n            cancelReset();\n            connection = subject = undefined;\n            hasCompleted = hasErrored = false;\n        };\n        var resetAndUnsubscribe = function () {\n            var conn = connection;\n            reset();\n            conn === null || conn === void 0 ? void 0 : conn.unsubscribe();\n        };\n        return operate(function (source, subscriber) {\n            refCount++;\n            if (!hasErrored && !hasCompleted) {\n                cancelReset();\n            }\n            var dest = (subject = subject !== null && subject !== void 0 ? subject : connector());\n            subscriber.add(function () {\n                refCount--;\n                if (refCount === 0 && !hasErrored && !hasCompleted) {\n                    resetConnection = handleReset(resetAndUnsubscribe, resetOnRefCountZero);\n                }\n            });\n            dest.subscribe(subscriber);\n            if (!connection &&\n                refCount > 0) {\n                connection = new SafeSubscriber({\n                    next: function (value) { return dest.next(value); },\n                    error: function (err) {\n                        hasErrored = true;\n                        cancelReset();\n                        resetConnection = handleReset(reset, resetOnError, err);\n                        dest.error(err);\n                    },\n                    complete: function () {\n                        hasCompleted = true;\n                        cancelReset();\n                        resetConnection = handleReset(reset, resetOnComplete);\n                        dest.complete();\n                    },\n                });\n                innerFrom(source).subscribe(connection);\n            }\n        })(wrapperSource);\n    };\n}\nfunction handleReset(reset, on) {\n    var args = [];\n    for (var _i = 2; _i < arguments.length; _i++) {\n        args[_i - 2] = arguments[_i];\n    }\n    if (on === true) {\n        reset();\n        return;\n    }\n    if (on === false) {\n        return;\n    }\n    var onSubscriber = new SafeSubscriber({\n        next: function () {\n            onSubscriber.unsubscribe();\n            reset();\n        },\n    });\n    return innerFrom(on.apply(void 0, __spreadArray([], __read(args)))).subscribe(onSubscriber);\n}\n//# sourceMappingURL=share.js.map", "import { ReplaySubject } from '../ReplaySubject';\nimport { share } from './share';\nexport function shareReplay(configOrBufferSize, windowTime, scheduler) {\n    var _a, _b, _c;\n    var bufferSize;\n    var refCount = false;\n    if (configOrBufferSize && typeof configOrBufferSize === 'object') {\n        (_a = configOrBufferSize.bufferSize, bufferSize = _a === void 0 ? Infinity : _a, _b = configOrBufferSize.windowTime, windowTime = _b === void 0 ? Infinity : _b, _c = configOrBufferSize.refCount, refCount = _c === void 0 ? false : _c, scheduler = configOrBufferSize.scheduler);\n    }\n    else {\n        bufferSize = (configOrBufferSize !== null && configOrBufferSize !== void 0 ? configOrBufferSize : Infinity);\n    }\n    return share({\n        connector: function () { return new ReplaySubject(bufferSize, windowTime, scheduler); },\n        resetOnError: true,\n        resetOnComplete: false,\n        resetOnRefCountZero: refCount,\n    });\n}\n//# sourceMappingURL=shareReplay.js.map", "import { isFunction } from '../util/isFunction';\nimport { operate } from '../util/lift';\nimport { createOperatorSubscriber } from './OperatorSubscriber';\nimport { identity } from '../util/identity';\nexport function tap(observerOrNext, error, complete) {\n    var tapObserver = isFunction(observerOrNext) || error || complete\n        ?\n            { next: observerOrNext, error: error, complete: complete }\n        : observerOrNext;\n    return tapObserver\n        ? operate(function (source, subscriber) {\n            var _a;\n            (_a = tapObserver.subscribe) === null || _a === void 0 ? void 0 : _a.call(tapObserver);\n            var isUnsub = true;\n            source.subscribe(createOperatorSubscriber(subscriber, function (value) {\n                var _a;\n                (_a = tapObserver.next) === null || _a === void 0 ? void 0 : _a.call(tapObserver, value);\n                subscriber.next(value);\n            }, function () {\n                var _a;\n                isUnsub = false;\n                (_a = tapObserver.complete) === null || _a === void 0 ? void 0 : _a.call(tapObserver);\n                subscriber.complete();\n            }, function (err) {\n                var _a;\n                isUnsub = false;\n                (_a = tapObserver.error) === null || _a === void 0 ? void 0 : _a.call(tapObserver, err);\n                subscriber.error(err);\n            }, function () {\n                var _a, _b;\n                if (isUnsub) {\n                    (_a = tapObserver.unsubscribe) === null || _a === void 0 ? void 0 : _a.call(tapObserver);\n                }\n                (_b = tapObserver.finalize) === null || _b === void 0 ? void 0 : _b.call(tapObserver);\n            }));\n        })\n        :\n            identity;\n}\n//# sourceMappingURL=tap.js.map", "var s = { 0: 8203, 1: 8204, 2: 8205, 3: 8290, 4: 8291, 5: 8288, 6: 65279, 7: 8289, 8: 119155, 9: 119156, a: 119157, b: 119158, c: 119159, d: 119160, e: 119161, f: 119162 }, c = { 0: 8203, 1: 8204, 2: 8205, 3: 65279 }, u = new Array(4).fill(String.fromCodePoint(c[0])).join(\"\");\nfunction E(t) {\n  let e = JSON.stringify(t);\n  return `${u}${Array.from(e).map((r) => {\n    let n = r.charCodeAt(0);\n    if (n > 255) throw new Error(`Only ASCII edit info can be encoded. Error attempting to encode ${e} on character ${r} (${n})`);\n    return Array.from(n.toString(4).padStart(4, \"0\")).map((o) => String.fromCodePoint(c[o])).join(\"\");\n  }).join(\"\")}`;\n}\nfunction I(t) {\n  return !Number.isNaN(Number(t)) || /[a-z]/i.test(t) && !/\\d+(?:[-:\\/]\\d+){2}(?:T\\d+(?:[-:\\/]\\d+){1,2}(\\.\\d+)?Z?)?/.test(t) ? !1 : !!Date.parse(t);\n}\nfunction T(t) {\n  try {\n    new URL(t, t.startsWith(\"/\") ? \"https://acme.com\" : void 0);\n  } catch {\n    return !1;\n  }\n  return !0;\n}\nfunction C(t, e, r = \"auto\") {\n  return r === !0 || r === \"auto\" && (I(t) || T(t)) ? t : `${t}${E(e)}`;\n}\nObject.fromEntries(Object.entries(c).map((t) => t.reverse()));\nObject.fromEntries(Object.entries(s).map((t) => t.reverse()));\nvar S = `${Object.values(s).map((t) => `\\\\u{${t.toString(16)}}`).join(\"\")}`, f = new RegExp(`[${S}]{4,}`, \"gu\");\nfunction _(t) {\n  var e;\n  return { cleaned: t.replace(f, \"\"), encoded: ((e = t.match(f)) == null ? void 0 : e[0]) || \"\" };\n}\nfunction O(t) {\n  return t && JSON.parse(_(JSON.stringify(t)).cleaned);\n}\nfunction stegaClean(result) {\n  return O(result);\n}\nconst vercelStegaCleanAll = stegaClean;\nexport {\n  C,\n  stegaClean,\n  vercelStegaCleanAll\n};\n//# sourceMappingURL=stegaClean.js.map\n", "const rePropName = /[^.[\\]]+|\\[(?:(-?\\d+(?:\\.\\d+)?)|([\"'])((?:(?!\\2)[^\\\\]|\\\\.)*?)\\2)\\]|(?=(?:\\.|\\[\\])(?:\\.|\\[\\]|$))/g, reKeySegment = /_key\\s*==\\s*['\"](.*)['\"]/, reIndexTuple = /^\\d*:\\d*$/;\nfunction isIndexSegment(segment) {\n  return typeof segment == \"number\" || typeof segment == \"string\" && /^\\[\\d+\\]$/.test(segment);\n}\nfunction isKeySegment(segment) {\n  return typeof segment == \"string\" ? reKeySegment.test(segment.trim()) : typeof segment == \"object\" && \"_key\" in segment;\n}\nfunction isIndexTuple(segment) {\n  if (typeof segment == \"string\" && reIndexTuple.test(segment))\n    return !0;\n  if (!Array.isArray(segment) || segment.length !== 2)\n    return !1;\n  const [from, to] = segment;\n  return (typeof from == \"number\" || from === \"\") && (typeof to == \"number\" || to === \"\");\n}\nfunction get(obj, path, defaultVal) {\n  const select = typeof path == \"string\" ? fromString(path) : path;\n  if (!Array.isArray(select))\n    throw new Error(\"Path must be an array or a string\");\n  let acc = obj;\n  for (let i = 0; i < select.length; i++) {\n    const segment = select[i];\n    if (isIndexSegment(segment)) {\n      if (!Array.isArray(acc))\n        return defaultVal;\n      acc = acc[segment];\n    }\n    if (isKeySegment(segment)) {\n      if (!Array.isArray(acc))\n        return defaultVal;\n      acc = acc.find((item) => item._key === segment._key);\n    }\n    if (typeof segment == \"string\" && (acc = typeof acc == \"object\" && acc !== null ? acc[segment] : void 0), typeof acc > \"u\")\n      return defaultVal;\n  }\n  return acc;\n}\nfunction toString(path) {\n  if (!Array.isArray(path))\n    throw new Error(\"Path is not an array\");\n  return path.reduce((target, segment, i) => {\n    const segmentType = typeof segment;\n    if (segmentType === \"number\")\n      return `${target}[${segment}]`;\n    if (segmentType === \"string\")\n      return `${target}${i === 0 ? \"\" : \".\"}${segment}`;\n    if (isKeySegment(segment) && segment._key)\n      return `${target}[_key==\"${segment._key}\"]`;\n    if (Array.isArray(segment)) {\n      const [from, to] = segment;\n      return `${target}[${from}:${to}]`;\n    }\n    throw new Error(`Unsupported path segment \\`${JSON.stringify(segment)}\\``);\n  }, \"\");\n}\nfunction fromString(path) {\n  if (typeof path != \"string\")\n    throw new Error(\"Path is not a string\");\n  const segments = path.match(rePropName);\n  if (!segments)\n    throw new Error(\"Invalid path string\");\n  return segments.map(parsePathSegment);\n}\nfunction parsePathSegment(segment) {\n  return isIndexSegment(segment) ? parseIndexSegment(segment) : isKeySegment(segment) ? parseKeySegment(segment) : isIndexTuple(segment) ? parseIndexTupleSegment(segment) : segment;\n}\nfunction parseIndexSegment(segment) {\n  return Number(segment.replace(/[^\\d]/g, \"\"));\n}\nfunction parseKeySegment(segment) {\n  return { _key: segment.match(reKeySegment)[1] };\n}\nfunction parseIndexTupleSegment(segment) {\n  const [from, to] = segment.split(\":\").map((seg) => seg === \"\" ? seg : Number(seg));\n  return [from, to];\n}\nvar studioPath = /* @__PURE__ */ Object.freeze({\n  __proto__: null,\n  fromString,\n  get,\n  isIndexSegment,\n  isIndexTuple,\n  isKeySegment,\n  reKeySegment,\n  toString\n});\nconst DRAFTS_FOLDER = \"drafts\", VERSION_FOLDER = \"versions\", PATH_SEPARATOR = \".\", DRAFTS_PREFIX = `${DRAFTS_FOLDER}${PATH_SEPARATOR}`, VERSION_PREFIX = `${VERSION_FOLDER}${PATH_SEPARATOR}`;\nfunction isDraftId(id) {\n  return id.startsWith(DRAFTS_PREFIX);\n}\nfunction isVersionId(id) {\n  return id.startsWith(VERSION_PREFIX);\n}\nfunction isPublishedId(id) {\n  return !isDraftId(id) && !isVersionId(id);\n}\nfunction getDraftId(id) {\n  if (isVersionId(id)) {\n    const publishedId = getPublishedId(id);\n    return DRAFTS_PREFIX + publishedId;\n  }\n  return isDraftId(id) ? id : DRAFTS_PREFIX + id;\n}\nfunction getVersionId(id, version) {\n  if (version === \"drafts\" || version === \"published\")\n    throw new Error('Version can not be \"published\" or \"drafts\"');\n  return `${VERSION_PREFIX}${version}${PATH_SEPARATOR}${getPublishedId(id)}`;\n}\nfunction getVersionFromId(id) {\n  if (!isVersionId(id)) return;\n  const [_versionPrefix, versionId, ..._publishedId] = id.split(PATH_SEPARATOR);\n  return versionId;\n}\nfunction getPublishedId(id) {\n  return isVersionId(id) ? id.split(PATH_SEPARATOR).slice(2).join(PATH_SEPARATOR) : isDraftId(id) ? id.slice(DRAFTS_PREFIX.length) : id;\n}\nconst ESCAPE = {\n  \"\\f\": \"\\\\f\",\n  \"\\n\": \"\\\\n\",\n  \"\\r\": \"\\\\r\",\n  \"\t\": \"\\\\t\",\n  \"'\": \"\\\\'\",\n  \"\\\\\": \"\\\\\\\\\"\n}, UNESCAPE = {\n  \"\\\\f\": \"\\f\",\n  \"\\\\n\": `\n`,\n  \"\\\\r\": \"\\r\",\n  \"\\\\t\": \"\t\",\n  \"\\\\'\": \"'\",\n  \"\\\\\\\\\": \"\\\\\"\n};\nfunction jsonPath(path) {\n  return `$${path.map((segment) => typeof segment == \"string\" ? `['${segment.replace(/[\\f\\n\\r\\t'\\\\]/g, (match) => ESCAPE[match])}']` : typeof segment == \"number\" ? `[${segment}]` : segment._key !== \"\" ? `[?(@._key=='${segment._key.replace(/['\\\\]/g, (match) => ESCAPE[match])}')]` : `[${segment._index}]`).join(\"\")}`;\n}\nfunction parseJsonPath(path) {\n  const parsed = [], parseRe = /\\['(.*?)'\\]|\\[(\\d+)\\]|\\[\\?\\(@\\._key=='(.*?)'\\)\\]/g;\n  let match;\n  for (; (match = parseRe.exec(path)) !== null; ) {\n    if (match[1] !== void 0) {\n      const key = match[1].replace(/\\\\(\\\\|f|n|r|t|')/g, (m) => UNESCAPE[m]);\n      parsed.push(key);\n      continue;\n    }\n    if (match[2] !== void 0) {\n      parsed.push(parseInt(match[2], 10));\n      continue;\n    }\n    if (match[3] !== void 0) {\n      const _key = match[3].replace(/\\\\(\\\\')/g, (m) => UNESCAPE[m]);\n      parsed.push({\n        _key,\n        _index: -1\n      });\n      continue;\n    }\n  }\n  return parsed;\n}\nfunction jsonPathToStudioPath(path) {\n  return path.map((segment) => {\n    if (typeof segment == \"string\" || typeof segment == \"number\")\n      return segment;\n    if (segment._key !== \"\")\n      return { _key: segment._key };\n    if (segment._index !== -1)\n      return segment._index;\n    throw new Error(`invalid segment:${JSON.stringify(segment)}`);\n  });\n}\nfunction studioPathToJsonPath(path) {\n  return (typeof path == \"string\" ? fromString(path) : path).map((segment) => {\n    if (typeof segment == \"string\" || typeof segment == \"number\")\n      return segment;\n    if (Array.isArray(segment))\n      throw new Error(`IndexTuple segments aren't supported:${JSON.stringify(segment)}`);\n    if (isContentSourceMapParsedPathKeyedSegment(segment))\n      return segment;\n    if (segment._key)\n      return { _key: segment._key, _index: -1 };\n    throw new Error(`invalid segment:${JSON.stringify(segment)}`);\n  });\n}\nfunction isContentSourceMapParsedPathKeyedSegment(segment) {\n  return typeof segment == \"object\" && \"_key\" in segment && \"_index\" in segment;\n}\nfunction jsonPathToMappingPath(path) {\n  return path.map((segment) => {\n    if (typeof segment == \"string\" || typeof segment == \"number\")\n      return segment;\n    if (segment._index !== -1)\n      return segment._index;\n    throw new Error(`invalid segment:${JSON.stringify(segment)}`);\n  });\n}\nfunction resolveMapping(resultPath, csm) {\n  if (!csm?.mappings)\n    return;\n  const resultMappingPath = jsonPath(jsonPathToMappingPath(resultPath));\n  if (csm.mappings[resultMappingPath] !== void 0)\n    return {\n      mapping: csm.mappings[resultMappingPath],\n      matchedPath: resultMappingPath,\n      pathSuffix: \"\"\n    };\n  const mappings = Object.entries(csm.mappings).filter(([key]) => resultMappingPath.startsWith(key)).sort(([key1], [key2]) => key2.length - key1.length);\n  if (mappings.length == 0)\n    return;\n  const [matchedPath, mapping] = mappings[0], pathSuffix = resultMappingPath.substring(matchedPath.length);\n  return { mapping, matchedPath, pathSuffix };\n}\nfunction isArray(value) {\n  return value !== null && Array.isArray(value);\n}\nfunction isRecord(value) {\n  return typeof value == \"object\" && value !== null;\n}\nfunction walkMap(value, mappingFn, path = []) {\n  if (isArray(value))\n    return value.map((v, idx) => {\n      if (isRecord(v)) {\n        const _key = v._key;\n        if (typeof _key == \"string\")\n          return walkMap(v, mappingFn, path.concat({ _key, _index: idx }));\n      }\n      return walkMap(v, mappingFn, path.concat(idx));\n    });\n  if (isRecord(value)) {\n    if (value._type === \"block\" || value._type === \"span\") {\n      const result = { ...value };\n      return value._type === \"block\" ? result.children = walkMap(value.children, mappingFn, path.concat(\"children\")) : value._type === \"span\" && (result.text = walkMap(value.text, mappingFn, path.concat(\"text\"))), result;\n    }\n    return Object.fromEntries(\n      Object.entries(value).map(([k, v]) => [k, walkMap(v, mappingFn, path.concat(k))])\n    );\n  }\n  return mappingFn(value, path);\n}\nfunction createEditUrl(options) {\n  const {\n    baseUrl,\n    workspace: _workspace = \"default\",\n    tool: _tool = \"default\",\n    id: _id,\n    type,\n    path,\n    projectId,\n    dataset\n  } = options;\n  if (!baseUrl)\n    throw new Error(\"baseUrl is required\");\n  if (!path)\n    throw new Error(\"path is required\");\n  if (!_id)\n    throw new Error(\"id is required\");\n  if (baseUrl !== \"/\" && baseUrl.endsWith(\"/\"))\n    throw new Error(\"baseUrl must not end with a slash\");\n  const workspace = _workspace === \"default\" ? void 0 : _workspace, tool = _tool === \"default\" ? void 0 : _tool, id = getPublishedId(_id), stringifiedPath = Array.isArray(path) ? toString(jsonPathToStudioPath(path)) : path, searchParams = new URLSearchParams({\n    baseUrl,\n    id,\n    type,\n    path: stringifiedPath\n  });\n  if (workspace && searchParams.set(\"workspace\", workspace), tool && searchParams.set(\"tool\", tool), projectId && searchParams.set(\"projectId\", projectId), dataset && searchParams.set(\"dataset\", dataset), isPublishedId(_id))\n    searchParams.set(\"perspective\", \"published\");\n  else if (isVersionId(_id)) {\n    const versionId = getVersionFromId(_id);\n    searchParams.set(\"perspective\", versionId);\n  }\n  const segments = [baseUrl === \"/\" ? \"\" : baseUrl];\n  workspace && segments.push(workspace);\n  const routerParams = [\n    \"mode=presentation\",\n    `id=${id}`,\n    `type=${type}`,\n    `path=${encodeURIComponent(stringifiedPath)}`\n  ];\n  return tool && routerParams.push(`tool=${tool}`), segments.push(\"intent\", \"edit\", `${routerParams.join(\";\")}?${searchParams}`), segments.join(\"/\");\n}\nfunction resolveEditInfo(options) {\n  const { resultSourceMap: csm, resultPath } = options, { mapping, pathSuffix } = resolveMapping(resultPath, csm) || {};\n  if (!mapping || mapping.source.type === \"literal\" || mapping.source.type === \"unknown\")\n    return;\n  const sourceDoc = csm.documents[mapping.source.document], sourcePath = csm.paths[mapping.source.path];\n  if (sourceDoc && sourcePath) {\n    const { baseUrl, workspace, tool } = resolveStudioBaseRoute(\n      typeof options.studioUrl == \"function\" ? options.studioUrl(sourceDoc) : options.studioUrl\n    );\n    if (!baseUrl) return;\n    const { _id, _type, _projectId, _dataset } = sourceDoc;\n    return {\n      baseUrl,\n      workspace,\n      tool,\n      id: _id,\n      type: _type,\n      path: parseJsonPath(sourcePath + pathSuffix),\n      projectId: _projectId,\n      dataset: _dataset\n    };\n  }\n}\nfunction resolveStudioBaseRoute(studioUrl) {\n  let baseUrl = typeof studioUrl == \"string\" ? studioUrl : studioUrl.baseUrl;\n  return baseUrl !== \"/\" && (baseUrl = baseUrl.replace(/\\/$/, \"\")), typeof studioUrl == \"string\" ? { baseUrl } : { ...studioUrl, baseUrl };\n}\nexport {\n  DRAFTS_FOLDER,\n  VERSION_FOLDER,\n  createEditUrl,\n  get,\n  getDraftId,\n  getPublishedId,\n  getVersionFromId,\n  getVersionId,\n  isDraftId,\n  isPublishedId,\n  isVersionId,\n  jsonPath,\n  jsonPathToStudioPath,\n  parseJsonPath,\n  reKeySegment,\n  resolveEditInfo,\n  resolveMapping,\n  resolveStudioBaseRoute,\n  studioPath,\n  studioPathToJsonPath,\n  toString,\n  walkMap\n};\n//# sourceMappingURL=resolveEditInfo.js.map\n", "import crypto from 'crypto'\nimport { urlAlphabet } from './url-alphabet/index.js'\nconst POOL_SIZE_MULTIPLIER = 128\nlet pool, poolOffset\nlet fillPool = bytes => {\n  if (!pool || pool.length < bytes) {\n    pool = Buffer.allocUnsafe(bytes * POOL_SIZE_MULTIPLIER)\n    crypto.randomFillSync(pool)\n    poolOffset = 0\n  } else if (poolOffset + bytes > pool.length) {\n    crypto.randomFillSync(pool)\n    poolOffset = 0\n  }\n  poolOffset += bytes\n}\nlet random = bytes => {\n  fillPool((bytes |= 0))\n  return pool.subarray(poolOffset - bytes, poolOffset)\n}\nlet customRandom = (alphabet, defaultSize, getRandom) => {\n  let mask = (2 << (31 - Math.clz32((alphabet.length - 1) | 1))) - 1\n  let step = Math.ceil((1.6 * mask * defaultSize) / alphabet.length)\n  return (size = defaultSize) => {\n    let id = ''\n    while (true) {\n      let bytes = getRandom(step)\n      let i = step\n      while (i--) {\n        id += alphabet[bytes[i] & mask] || ''\n        if (id.length === size) return id\n      }\n    }\n  }\n}\nlet customAlphabet = (alphabet, size = 21) =>\n  customRandom(alphabet, size, random)\nlet nanoid = (size = 21) => {\n  fillPool((size |= 0))\n  let id = ''\n  for (let i = poolOffset - size; i < poolOffset; i++) {\n    id += urlAlphabet[pool[i] & 63]\n  }\n  return id\n}\nexport { nanoid, customAlphabet, customRandom, urlAlphabet, random }\n", "import { getIt } from \"get-it\";\nimport { adapter, environment } from \"get-it\";\nimport { retry, jsonRequest, jsonResponse, progress, observable } from \"get-it/middleware\";\nimport { Observable, defer, of, isObservable, mergeMap, from, lastValueFrom, shareReplay, catchError, concat, throwError, timer, tap, finalize, share, merge, EMPTY, map as map$1, firstValueFrom } from \"rxjs\";\nimport { stegaClean } from \"./_chunks-es/stegaClean.js\";\nimport { combineLatestWith, map, filter, finalize as finalize$1 } from \"rxjs/operators\";\nimport { getVersionFromId, isDraftId, getVersionId, getDraftId, isVersionId, getPublishedId } from \"@sanity/client/csm\";\nimport { customAlphabet } from \"nanoid\";\nclass ClientError extends Error {\n  response;\n  statusCode = 400;\n  responseBody;\n  details;\n  constructor(res) {\n    const props = extractErrorProps(res);\n    super(props.message), Object.assign(this, props);\n  }\n}\nclass ServerError extends Error {\n  response;\n  statusCode = 500;\n  responseBody;\n  details;\n  constructor(res) {\n    const props = extractErrorProps(res);\n    super(props.message), Object.assign(this, props);\n  }\n}\nfunction extractErrorProps(res) {\n  const body = res.body, props = {\n    response: res,\n    statusCode: res.statusCode,\n    responseBody: stringifyBody(body, res),\n    message: \"\",\n    details: void 0\n  };\n  if (body.error && body.message)\n    return props.message = `${body.error} - ${body.message}`, props;\n  if (isMutationError(body) || isActionError(body)) {\n    const allItems = body.error.items || [], items = allItems.slice(0, 5).map((item) => item.error?.description).filter(Boolean);\n    let itemsStr = items.length ? `:\n- ${items.join(`\n- `)}` : \"\";\n    return allItems.length > 5 && (itemsStr += `\n...and ${allItems.length - 5} more`), props.message = `${body.error.description}${itemsStr}`, props.details = body.error, props;\n  }\n  return body.error && body.error.description ? (props.message = body.error.description, props.details = body.error, props) : (props.message = body.error || body.message || httpErrorMessage(res), props);\n}\nfunction isMutationError(body) {\n  return isPlainObject(body) && isPlainObject(body.error) && body.error.type === \"mutationError\" && typeof body.error.description == \"string\";\n}\nfunction isActionError(body) {\n  return isPlainObject(body) && isPlainObject(body.error) && body.error.type === \"actionError\" && typeof body.error.description == \"string\";\n}\nfunction isPlainObject(obj) {\n  return typeof obj == \"object\" && obj !== null && !Array.isArray(obj);\n}\nfunction httpErrorMessage(res) {\n  const statusMessage = res.statusMessage ? ` ${res.statusMessage}` : \"\";\n  return `${res.method}-request to ${res.url} resulted in HTTP ${res.statusCode}${statusMessage}`;\n}\nfunction stringifyBody(body, res) {\n  return (res.headers[\"content-type\"] || \"\").toLowerCase().indexOf(\"application/json\") !== -1 ? JSON.stringify(body, null, 2) : body;\n}\nclass CorsOriginError extends Error {\n  projectId;\n  addOriginUrl;\n  constructor({ projectId: projectId2 }) {\n    super(\"CorsOriginError\"), this.name = \"CorsOriginError\", this.projectId = projectId2;\n    const url = new URL(`https://sanity.io/manage/project/${projectId2}/api`);\n    if (typeof location < \"u\") {\n      const { origin } = location;\n      url.searchParams.set(\"cors\", \"add\"), url.searchParams.set(\"origin\", origin), this.addOriginUrl = url, this.message = `The current origin is not allowed to connect to the Live Content API. Add it here: ${url}`;\n    } else\n      this.message = `The current origin is not allowed to connect to the Live Content API. Change your configuration here: ${url}`;\n  }\n}\nconst httpError = {\n  onResponse: (res) => {\n    if (res.statusCode >= 500)\n      throw new ServerError(res);\n    if (res.statusCode >= 400)\n      throw new ClientError(res);\n    return res;\n  }\n};\nfunction printWarnings() {\n  const seen = {};\n  return {\n    onResponse: (res) => {\n      const warn = res.headers[\"x-sanity-warning\"], warnings = Array.isArray(warn) ? warn : [warn];\n      for (const msg of warnings)\n        !msg || seen[msg] || (seen[msg] = !0, console.warn(msg));\n      return res;\n    }\n  };\n}\nfunction defineHttpRequest(envMiddleware2) {\n  return getIt([\n    retry({ shouldRetry }),\n    ...envMiddleware2,\n    printWarnings(),\n    jsonRequest(),\n    jsonResponse(),\n    progress(),\n    httpError,\n    observable({ implementation: Observable })\n  ]);\n}\nfunction shouldRetry(err, attempt, options) {\n  if (options.maxRetries === 0) return !1;\n  const isSafe = options.method === \"GET\" || options.method === \"HEAD\", isQuery2 = (options.uri || options.url).startsWith(\"/data/query\"), isRetriableResponse = err.response && (err.response.statusCode === 429 || err.response.statusCode === 502 || err.response.statusCode === 503);\n  return (isSafe || isQuery2) && isRetriableResponse ? !0 : retry.shouldRetry(err, attempt, options);\n}\nconst BASE_URL = \"https://www.sanity.io/help/\";\nfunction generateHelpUrl(slug) {\n  return BASE_URL + slug;\n}\nconst VALID_ASSET_TYPES = [\"image\", \"file\"], VALID_INSERT_LOCATIONS = [\"before\", \"after\", \"replace\"], dataset = (name) => {\n  if (!/^(~[a-z0-9]{1}[-\\w]{0,63}|[a-z0-9]{1}[-\\w]{0,63})$/.test(name))\n    throw new Error(\n      \"Datasets can only contain lowercase characters, numbers, underscores and dashes, and start with tilde, and be maximum 64 characters\"\n    );\n}, projectId = (id) => {\n  if (!/^[-a-z0-9]+$/i.test(id))\n    throw new Error(\"`projectId` can only contain only a-z, 0-9 and dashes\");\n}, validateAssetType = (type) => {\n  if (VALID_ASSET_TYPES.indexOf(type) === -1)\n    throw new Error(`Invalid asset type: ${type}. Must be one of ${VALID_ASSET_TYPES.join(\", \")}`);\n}, validateObject = (op, val) => {\n  if (val === null || typeof val != \"object\" || Array.isArray(val))\n    throw new Error(`${op}() takes an object of properties`);\n}, validateDocumentId = (op, id) => {\n  if (typeof id != \"string\" || !/^[a-z0-9_][a-z0-9_.-]{0,127}$/i.test(id) || id.includes(\"..\"))\n    throw new Error(`${op}(): \"${id}\" is not a valid document ID`);\n}, requireDocumentId = (op, doc) => {\n  if (!doc._id)\n    throw new Error(`${op}() requires that the document contains an ID (\"_id\" property)`);\n  validateDocumentId(op, doc._id);\n}, validateDocumentType = (op, type) => {\n  if (typeof type != \"string\")\n    throw new Error(`\\`${op}()\\`: \\`${type}\\` is not a valid document type`);\n}, requireDocumentType = (op, doc) => {\n  if (!doc._type)\n    throw new Error(`\\`${op}()\\` requires that the document contains a type (\\`_type\\` property)`);\n  validateDocumentType(op, doc._type);\n}, validateVersionIdMatch = (builtVersionId, document) => {\n  if (document._id && document._id !== builtVersionId)\n    throw new Error(\n      `The provided document ID (\\`${document._id}\\`) does not match the generated version ID (\\`${builtVersionId}\\`)`\n    );\n}, validateInsert = (at, selector, items) => {\n  const signature = \"insert(at, selector, items)\";\n  if (VALID_INSERT_LOCATIONS.indexOf(at) === -1) {\n    const valid = VALID_INSERT_LOCATIONS.map((loc) => `\"${loc}\"`).join(\", \");\n    throw new Error(`${signature} takes an \"at\"-argument which is one of: ${valid}`);\n  }\n  if (typeof selector != \"string\")\n    throw new Error(`${signature} takes a \"selector\"-argument which must be a string`);\n  if (!Array.isArray(items))\n    throw new Error(`${signature} takes an \"items\"-argument which must be an array`);\n}, hasDataset = (config) => {\n  if (!config.dataset)\n    throw new Error(\"`dataset` must be provided to perform queries\");\n  return config.dataset || \"\";\n}, requestTag = (tag) => {\n  if (typeof tag != \"string\" || !/^[a-z0-9._-]{1,75}$/i.test(tag))\n    throw new Error(\n      \"Tag can only contain alphanumeric characters, underscores, dashes and dots, and be between one and 75 characters long.\"\n    );\n  return tag;\n}, resourceConfig = (config) => {\n  if (!config[\"~experimental_resource\"])\n    throw new Error(\"`resource` must be provided to perform resource queries\");\n  const { type, id } = config[\"~experimental_resource\"];\n  switch (type) {\n    case \"dataset\": {\n      if (id.split(\".\").length !== 2)\n        throw new Error('Dataset resource ID must be in the format \"project.dataset\"');\n      return;\n    }\n    case \"dashboard\":\n    case \"media-library\":\n    case \"canvas\":\n      return;\n    default:\n      throw new Error(`Unsupported resource type: ${type.toString()}`);\n  }\n}, resourceGuard = (service, config) => {\n  if (config[\"~experimental_resource\"])\n    throw new Error(`\\`${service}\\` does not support resource-based operations`);\n};\nfunction once(fn) {\n  let didCall = !1, returnValue;\n  return (...args) => (didCall || (returnValue = fn(...args), didCall = !0), returnValue);\n}\nconst createWarningPrinter = (message) => (\n  // eslint-disable-next-line no-console\n  once((...args) => console.warn(message.join(\" \"), ...args))\n), printCdnAndWithCredentialsWarning = createWarningPrinter([\n  \"Because you set `withCredentials` to true, we will override your `useCdn`\",\n  \"setting to be false since (cookie-based) credentials are never set on the CDN\"\n]), printCdnWarning = createWarningPrinter([\n  \"Since you haven't set a value for `useCdn`, we will deliver content using our\",\n  \"global, edge-cached API-CDN. If you wish to have content delivered faster, set\",\n  \"`useCdn: false` to use the Live API. Note: You may incur higher costs using the live API.\"\n]), printCdnPreviewDraftsWarning = createWarningPrinter([\n  \"The Sanity client is configured with the `perspective` set to `drafts` or `previewDrafts`, which doesn't support the API-CDN.\",\n  \"The Live API will be used instead. Set `useCdn: false` in your configuration to hide this warning.\"\n]), printPreviewDraftsDeprecationWarning = createWarningPrinter([\n  \"The `previewDrafts` perspective has been renamed to  `drafts` and will be removed in a future API version\"\n]), printBrowserTokenWarning = createWarningPrinter([\n  \"You have configured Sanity client to use a token in the browser. This may cause unintentional security issues.\",\n  `See ${generateHelpUrl(\n    \"js-client-browser-token\"\n  )} for more information and how to hide this warning.`\n]), printCredentialedTokenWarning = createWarningPrinter([\n  \"You have configured Sanity client to use a token, but also provided `withCredentials: true`.\",\n  \"This is no longer supported - only token will be used - remove `withCredentials: true`.\"\n]), printNoApiVersionSpecifiedWarning = createWarningPrinter([\n  \"Using the Sanity client without specifying an API version is deprecated.\",\n  `See ${generateHelpUrl(\"js-client-api-version\")}`\n]), printNoDefaultExport = createWarningPrinter([\n  \"The default export of @sanity/client has been deprecated. Use the named export `createClient` instead.\"\n]), defaultCdnHost = \"apicdn.sanity.io\", defaultConfig = {\n  apiHost: \"https://api.sanity.io\",\n  apiVersion: \"1\",\n  useProjectHostname: !0,\n  stega: { enabled: !1 }\n}, LOCALHOSTS = [\"localhost\", \"127.0.0.1\", \"0.0.0.0\"], isLocal = (host) => LOCALHOSTS.indexOf(host) !== -1;\nfunction validateApiVersion(apiVersion) {\n  if (apiVersion === \"1\" || apiVersion === \"X\")\n    return;\n  const apiDate = new Date(apiVersion);\n  if (!(/^\\d{4}-\\d{2}-\\d{2}$/.test(apiVersion) && apiDate instanceof Date && apiDate.getTime() > 0))\n    throw new Error(\"Invalid API version string, expected `1` or date in format `YYYY-MM-DD`\");\n}\nfunction validateApiPerspective(perspective) {\n  if (Array.isArray(perspective) && perspective.length > 1 && perspective.includes(\"raw\"))\n    throw new TypeError(\n      'Invalid API perspective value: \"raw\". The raw-perspective can not be combined with other perspectives'\n    );\n}\nconst initConfig = (config, prevConfig) => {\n  const specifiedConfig = {\n    ...prevConfig,\n    ...config,\n    stega: {\n      ...typeof prevConfig.stega == \"boolean\" ? { enabled: prevConfig.stega } : prevConfig.stega || defaultConfig.stega,\n      ...typeof config.stega == \"boolean\" ? { enabled: config.stega } : config.stega || {}\n    }\n  };\n  specifiedConfig.apiVersion || printNoApiVersionSpecifiedWarning();\n  const newConfig = {\n    ...defaultConfig,\n    ...specifiedConfig\n  }, projectBased = newConfig.useProjectHostname && !newConfig[\"~experimental_resource\"];\n  if (typeof Promise > \"u\") {\n    const helpUrl = generateHelpUrl(\"js-client-promise-polyfill\");\n    throw new Error(`No native Promise-implementation found, polyfill needed - see ${helpUrl}`);\n  }\n  if (projectBased && !newConfig.projectId)\n    throw new Error(\"Configuration must contain `projectId`\");\n  if (newConfig[\"~experimental_resource\"] && resourceConfig(newConfig), typeof newConfig.perspective < \"u\" && validateApiPerspective(newConfig.perspective), \"encodeSourceMap\" in newConfig)\n    throw new Error(\n      \"It looks like you're using options meant for '@sanity/preview-kit/client'. 'encodeSourceMap' is not supported in '@sanity/client'. Did you mean 'stega.enabled'?\"\n    );\n  if (\"encodeSourceMapAtPath\" in newConfig)\n    throw new Error(\n      \"It looks like you're using options meant for '@sanity/preview-kit/client'. 'encodeSourceMapAtPath' is not supported in '@sanity/client'. Did you mean 'stega.filter'?\"\n    );\n  if (typeof newConfig.stega.enabled != \"boolean\")\n    throw new Error(`stega.enabled must be a boolean, received ${newConfig.stega.enabled}`);\n  if (newConfig.stega.enabled && newConfig.stega.studioUrl === void 0)\n    throw new Error(\"stega.studioUrl must be defined when stega.enabled is true\");\n  if (newConfig.stega.enabled && typeof newConfig.stega.studioUrl != \"string\" && typeof newConfig.stega.studioUrl != \"function\")\n    throw new Error(\n      `stega.studioUrl must be a string or a function, received ${newConfig.stega.studioUrl}`\n    );\n  const isBrowser = typeof window < \"u\" && window.location && window.location.hostname, isLocalhost = isBrowser && isLocal(window.location.hostname), hasToken = !!newConfig.token;\n  newConfig.withCredentials && hasToken && (printCredentialedTokenWarning(), newConfig.withCredentials = !1), isBrowser && isLocalhost && hasToken && newConfig.ignoreBrowserTokenWarning !== !0 ? printBrowserTokenWarning() : typeof newConfig.useCdn > \"u\" && printCdnWarning(), projectBased && projectId(newConfig.projectId), newConfig.dataset && dataset(newConfig.dataset), \"requestTagPrefix\" in newConfig && (newConfig.requestTagPrefix = newConfig.requestTagPrefix ? requestTag(newConfig.requestTagPrefix).replace(/\\.+$/, \"\") : void 0), newConfig.apiVersion = `${newConfig.apiVersion}`.replace(/^v/, \"\"), newConfig.isDefaultApi = newConfig.apiHost === defaultConfig.apiHost, newConfig.useCdn === !0 && newConfig.withCredentials && printCdnAndWithCredentialsWarning(), newConfig.useCdn = newConfig.useCdn !== !1 && !newConfig.withCredentials, validateApiVersion(newConfig.apiVersion);\n  const hostParts = newConfig.apiHost.split(\"://\", 2), protocol = hostParts[0], host = hostParts[1], cdnHost = newConfig.isDefaultApi ? defaultCdnHost : host;\n  return projectBased ? (newConfig.url = `${protocol}://${newConfig.projectId}.${host}/v${newConfig.apiVersion}`, newConfig.cdnUrl = `${protocol}://${newConfig.projectId}.${cdnHost}/v${newConfig.apiVersion}`) : (newConfig.url = `${newConfig.apiHost}/v${newConfig.apiVersion}`, newConfig.cdnUrl = newConfig.url), newConfig;\n};\nclass ConnectionFailedError extends Error {\n  name = \"ConnectionFailedError\";\n}\nclass DisconnectError extends Error {\n  name = \"DisconnectError\";\n  reason;\n  constructor(message, reason, options = {}) {\n    super(message, options), this.reason = reason;\n  }\n}\nclass ChannelError extends Error {\n  name = \"ChannelError\";\n  data;\n  constructor(message, data) {\n    super(message), this.data = data;\n  }\n}\nclass MessageError extends Error {\n  name = \"MessageError\";\n  data;\n  constructor(message, data, options = {}) {\n    super(message, options), this.data = data;\n  }\n}\nclass MessageParseError extends Error {\n  name = \"MessageParseError\";\n}\nconst REQUIRED_EVENTS = [\"channelError\", \"disconnect\"];\nfunction connectEventSource(initEventSource, events) {\n  return defer(() => {\n    const es = initEventSource();\n    return isObservable(es) ? es : of(es);\n  }).pipe(mergeMap((es) => connectWithESInstance(es, events)));\n}\nfunction connectWithESInstance(es, events) {\n  return new Observable((observer) => {\n    const emitOpen = events.includes(\"open\"), emitReconnect = events.includes(\"reconnect\");\n    function onError(evt) {\n      if (\"data\" in evt) {\n        const [parseError, event] = parseEvent(evt);\n        observer.error(\n          parseError ? new MessageParseError(\"Unable to parse EventSource error message\", { cause: event }) : new MessageError((event?.data).message, event)\n        );\n        return;\n      }\n      es.readyState === es.CLOSED ? observer.error(new ConnectionFailedError(\"EventSource connection failed\")) : emitReconnect && observer.next({ type: \"reconnect\" });\n    }\n    function onOpen() {\n      observer.next({ type: \"open\" });\n    }\n    function onMessage(message) {\n      const [parseError, event] = parseEvent(message);\n      if (parseError) {\n        observer.error(\n          new MessageParseError(\"Unable to parse EventSource message\", { cause: parseError })\n        );\n        return;\n      }\n      if (message.type === \"channelError\") {\n        observer.error(new ChannelError(extractErrorMessage(event?.data), event.data));\n        return;\n      }\n      if (message.type === \"disconnect\") {\n        observer.error(\n          new DisconnectError(\n            `Server disconnected client: ${event.data?.reason || \"unknown error\"}`\n          )\n        );\n        return;\n      }\n      observer.next({\n        type: message.type,\n        id: message.lastEventId,\n        ...event.data ? { data: event.data } : {}\n      });\n    }\n    es.addEventListener(\"error\", onError), emitOpen && es.addEventListener(\"open\", onOpen);\n    const cleanedEvents = [.../* @__PURE__ */ new Set([...REQUIRED_EVENTS, ...events])].filter((type) => type !== \"error\" && type !== \"open\" && type !== \"reconnect\");\n    return cleanedEvents.forEach((type) => es.addEventListener(type, onMessage)), () => {\n      es.removeEventListener(\"error\", onError), emitOpen && es.removeEventListener(\"open\", onOpen), cleanedEvents.forEach((type) => es.removeEventListener(type, onMessage)), es.close();\n    };\n  });\n}\nfunction parseEvent(message) {\n  try {\n    const data = typeof message.data == \"string\" && JSON.parse(message.data);\n    return [\n      null,\n      {\n        type: message.type,\n        id: message.lastEventId,\n        ...isEmptyObject(data) ? {} : { data }\n      }\n    ];\n  } catch (err) {\n    return [err, null];\n  }\n}\nfunction extractErrorMessage(err) {\n  return err.error ? err.error.description ? err.error.description : typeof err.error == \"string\" ? err.error : JSON.stringify(err.error, null, 2) : err.message || \"Unknown listener error\";\n}\nfunction isEmptyObject(data) {\n  for (const _ in data)\n    return !1;\n  return !0;\n}\nfunction getSelection(sel) {\n  if (typeof sel == \"string\")\n    return { id: sel };\n  if (Array.isArray(sel))\n    return { query: \"*[_id in $ids]\", params: { ids: sel } };\n  if (typeof sel == \"object\" && sel !== null && \"query\" in sel && typeof sel.query == \"string\")\n    return \"params\" in sel && typeof sel.params == \"object\" && sel.params !== null ? { query: sel.query, params: sel.params } : { query: sel.query };\n  const selectionOpts = [\n    \"* Document ID (<docId>)\",\n    \"* Array of document IDs\",\n    \"* Object containing `query`\"\n  ].join(`\n`);\n  throw new Error(`Unknown selection - must be one of:\n\n${selectionOpts}`);\n}\nclass BasePatch {\n  selection;\n  operations;\n  constructor(selection, operations = {}) {\n    this.selection = selection, this.operations = operations;\n  }\n  /**\n   * Sets the given attributes to the document. Does NOT merge objects.\n   * The operation is added to the current patch, ready to be commited by `commit()`\n   *\n   * @param attrs - Attributes to set. To set a deep attribute, use JSONMatch, eg: \\{\"nested.prop\": \"value\"\\}\n   */\n  set(attrs) {\n    return this._assign(\"set\", attrs);\n  }\n  /**\n   * Sets the given attributes to the document if they are not currently set. Does NOT merge objects.\n   * The operation is added to the current patch, ready to be commited by `commit()`\n   *\n   * @param attrs - Attributes to set. To set a deep attribute, use JSONMatch, eg: \\{\"nested.prop\": \"value\"\\}\n   */\n  setIfMissing(attrs) {\n    return this._assign(\"setIfMissing\", attrs);\n  }\n  /**\n   * Performs a \"diff-match-patch\" operation on the string attributes provided.\n   * The operation is added to the current patch, ready to be commited by `commit()`\n   *\n   * @param attrs - Attributes to perform operation on. To set a deep attribute, use JSONMatch, eg: \\{\"nested.prop\": \"dmp\"\\}\n   */\n  diffMatchPatch(attrs) {\n    return validateObject(\"diffMatchPatch\", attrs), this._assign(\"diffMatchPatch\", attrs);\n  }\n  /**\n   * Unsets the attribute paths provided.\n   * The operation is added to the current patch, ready to be commited by `commit()`\n   *\n   * @param attrs - Attribute paths to unset.\n   */\n  unset(attrs) {\n    if (!Array.isArray(attrs))\n      throw new Error(\"unset(attrs) takes an array of attributes to unset, non-array given\");\n    return this.operations = Object.assign({}, this.operations, { unset: attrs }), this;\n  }\n  /**\n   * Increment a numeric value. Each entry in the argument is either an attribute or a JSON path. The value may be a positive or negative integer or floating-point value. The operation will fail if target value is not a numeric value, or doesn't exist.\n   *\n   * @param attrs - Object of attribute paths to increment, values representing the number to increment by.\n   */\n  inc(attrs) {\n    return this._assign(\"inc\", attrs);\n  }\n  /**\n   * Decrement a numeric value. Each entry in the argument is either an attribute or a JSON path. The value may be a positive or negative integer or floating-point value. The operation will fail if target value is not a numeric value, or doesn't exist.\n   *\n   * @param attrs - Object of attribute paths to decrement, values representing the number to decrement by.\n   */\n  dec(attrs) {\n    return this._assign(\"dec\", attrs);\n  }\n  /**\n   * Provides methods for modifying arrays, by inserting, appending and replacing elements via a JSONPath expression.\n   *\n   * @param at - Location to insert at, relative to the given selector, or 'replace' the matched path\n   * @param selector - JSONPath expression, eg `comments[-1]` or `blocks[_key==\"abc123\"]`\n   * @param items - Array of items to insert/replace\n   */\n  insert(at, selector, items) {\n    return validateInsert(at, selector, items), this._assign(\"insert\", { [at]: selector, items });\n  }\n  /**\n   * Append the given items to the array at the given JSONPath\n   *\n   * @param selector - Attribute/path to append to, eg `comments` or `person.hobbies`\n   * @param items - Array of items to append to the array\n   */\n  append(selector, items) {\n    return this.insert(\"after\", `${selector}[-1]`, items);\n  }\n  /**\n   * Prepend the given items to the array at the given JSONPath\n   *\n   * @param selector - Attribute/path to prepend to, eg `comments` or `person.hobbies`\n   * @param items - Array of items to prepend to the array\n   */\n  prepend(selector, items) {\n    return this.insert(\"before\", `${selector}[0]`, items);\n  }\n  /**\n   * Change the contents of an array by removing existing elements and/or adding new elements.\n   *\n   * @param selector - Attribute or JSONPath expression for array\n   * @param start - Index at which to start changing the array (with origin 0). If greater than the length of the array, actual starting index will be set to the length of the array. If negative, will begin that many elements from the end of the array (with origin -1) and will be set to 0 if absolute value is greater than the length of the array.x\n   * @param deleteCount - An integer indicating the number of old array elements to remove.\n   * @param items - The elements to add to the array, beginning at the start index. If you don't specify any elements, splice() will only remove elements from the array.\n   */\n  splice(selector, start, deleteCount, items) {\n    const delAll = typeof deleteCount > \"u\" || deleteCount === -1, startIndex = start < 0 ? start - 1 : start, delCount = delAll ? -1 : Math.max(0, start + deleteCount), delRange = startIndex < 0 && delCount >= 0 ? \"\" : delCount, rangeSelector = `${selector}[${startIndex}:${delRange}]`;\n    return this.insert(\"replace\", rangeSelector, items || []);\n  }\n  /**\n   * Adds a revision clause, preventing the document from being patched if the `_rev` property does not match the given value\n   *\n   * @param rev - Revision to lock the patch to\n   */\n  ifRevisionId(rev) {\n    return this.operations.ifRevisionID = rev, this;\n  }\n  /**\n   * Return a plain JSON representation of the patch\n   */\n  serialize() {\n    return { ...getSelection(this.selection), ...this.operations };\n  }\n  /**\n   * Return a plain JSON representation of the patch\n   */\n  toJSON() {\n    return this.serialize();\n  }\n  /**\n   * Clears the patch of all operations\n   */\n  reset() {\n    return this.operations = {}, this;\n  }\n  _assign(op, props, merge2 = !0) {\n    return validateObject(op, props), this.operations = Object.assign({}, this.operations, {\n      [op]: Object.assign({}, merge2 && this.operations[op] || {}, props)\n    }), this;\n  }\n  _set(op, props) {\n    return this._assign(op, props, !1);\n  }\n}\nclass ObservablePatch extends BasePatch {\n  #client;\n  constructor(selection, operations, client) {\n    super(selection, operations), this.#client = client;\n  }\n  /**\n   * Clones the patch\n   */\n  clone() {\n    return new ObservablePatch(this.selection, { ...this.operations }, this.#client);\n  }\n  commit(options) {\n    if (!this.#client)\n      throw new Error(\n        \"No `client` passed to patch, either provide one or pass the patch to a clients `mutate()` method\"\n      );\n    const returnFirst = typeof this.selection == \"string\", opts = Object.assign({ returnFirst, returnDocuments: !0 }, options);\n    return this.#client.mutate({ patch: this.serialize() }, opts);\n  }\n}\nclass Patch extends BasePatch {\n  #client;\n  constructor(selection, operations, client) {\n    super(selection, operations), this.#client = client;\n  }\n  /**\n   * Clones the patch\n   */\n  clone() {\n    return new Patch(this.selection, { ...this.operations }, this.#client);\n  }\n  commit(options) {\n    if (!this.#client)\n      throw new Error(\n        \"No `client` passed to patch, either provide one or pass the patch to a clients `mutate()` method\"\n      );\n    const returnFirst = typeof this.selection == \"string\", opts = Object.assign({ returnFirst, returnDocuments: !0 }, options);\n    return this.#client.mutate({ patch: this.serialize() }, opts);\n  }\n}\nconst defaultMutateOptions = { returnDocuments: !1 };\nclass BaseTransaction {\n  operations;\n  trxId;\n  constructor(operations = [], transactionId) {\n    this.operations = operations, this.trxId = transactionId;\n  }\n  /**\n   * Creates a new Sanity document. If `_id` is provided and already exists, the mutation will fail. If no `_id` is given, one will automatically be generated by the database.\n   * The operation is added to the current transaction, ready to be commited by `commit()`\n   *\n   * @param doc - Document to create. Requires a `_type` property.\n   */\n  create(doc) {\n    return validateObject(\"create\", doc), this._add({ create: doc });\n  }\n  /**\n   * Creates a new Sanity document. If a document with the same `_id` already exists, the create operation will be ignored.\n   * The operation is added to the current transaction, ready to be commited by `commit()`\n   *\n   * @param doc - Document to create if it does not already exist. Requires `_id` and `_type` properties.\n   */\n  createIfNotExists(doc) {\n    const op = \"createIfNotExists\";\n    return validateObject(op, doc), requireDocumentId(op, doc), this._add({ [op]: doc });\n  }\n  /**\n   * Creates a new Sanity document, or replaces an existing one if the same `_id` is already used.\n   * The operation is added to the current transaction, ready to be commited by `commit()`\n   *\n   * @param doc - Document to create or replace. Requires `_id` and `_type` properties.\n   */\n  createOrReplace(doc) {\n    const op = \"createOrReplace\";\n    return validateObject(op, doc), requireDocumentId(op, doc), this._add({ [op]: doc });\n  }\n  /**\n   * Deletes the document with the given document ID\n   * The operation is added to the current transaction, ready to be commited by `commit()`\n   *\n   * @param documentId - Document ID to delete\n   */\n  delete(documentId) {\n    return validateDocumentId(\"delete\", documentId), this._add({ delete: { id: documentId } });\n  }\n  transactionId(id) {\n    return id ? (this.trxId = id, this) : this.trxId;\n  }\n  /**\n   * Return a plain JSON representation of the transaction\n   */\n  serialize() {\n    return [...this.operations];\n  }\n  /**\n   * Return a plain JSON representation of the transaction\n   */\n  toJSON() {\n    return this.serialize();\n  }\n  /**\n   * Clears the transaction of all operations\n   */\n  reset() {\n    return this.operations = [], this;\n  }\n  _add(mut) {\n    return this.operations.push(mut), this;\n  }\n}\nclass Transaction extends BaseTransaction {\n  #client;\n  constructor(operations, client, transactionId) {\n    super(operations, transactionId), this.#client = client;\n  }\n  /**\n   * Clones the transaction\n   */\n  clone() {\n    return new Transaction([...this.operations], this.#client, this.trxId);\n  }\n  commit(options) {\n    if (!this.#client)\n      throw new Error(\n        \"No `client` passed to transaction, either provide one or pass the transaction to a clients `mutate()` method\"\n      );\n    return this.#client.mutate(\n      this.serialize(),\n      Object.assign({ transactionId: this.trxId }, defaultMutateOptions, options || {})\n    );\n  }\n  patch(patchOrDocumentId, patchOps) {\n    const isBuilder = typeof patchOps == \"function\", isPatch = typeof patchOrDocumentId != \"string\" && patchOrDocumentId instanceof Patch, isMutationSelection = typeof patchOrDocumentId == \"object\" && (\"query\" in patchOrDocumentId || \"id\" in patchOrDocumentId);\n    if (isPatch)\n      return this._add({ patch: patchOrDocumentId.serialize() });\n    if (isBuilder) {\n      const patch = patchOps(new Patch(patchOrDocumentId, {}, this.#client));\n      if (!(patch instanceof Patch))\n        throw new Error(\"function passed to `patch()` must return the patch\");\n      return this._add({ patch: patch.serialize() });\n    }\n    if (isMutationSelection) {\n      const patch = new Patch(patchOrDocumentId, patchOps || {}, this.#client);\n      return this._add({ patch: patch.serialize() });\n    }\n    return this._add({ patch: { id: patchOrDocumentId, ...patchOps } });\n  }\n}\nclass ObservableTransaction extends BaseTransaction {\n  #client;\n  constructor(operations, client, transactionId) {\n    super(operations, transactionId), this.#client = client;\n  }\n  /**\n   * Clones the transaction\n   */\n  clone() {\n    return new ObservableTransaction([...this.operations], this.#client, this.trxId);\n  }\n  commit(options) {\n    if (!this.#client)\n      throw new Error(\n        \"No `client` passed to transaction, either provide one or pass the transaction to a clients `mutate()` method\"\n      );\n    return this.#client.mutate(\n      this.serialize(),\n      Object.assign({ transactionId: this.trxId }, defaultMutateOptions, options || {})\n    );\n  }\n  patch(patchOrDocumentId, patchOps) {\n    const isBuilder = typeof patchOps == \"function\";\n    if (typeof patchOrDocumentId != \"string\" && patchOrDocumentId instanceof ObservablePatch)\n      return this._add({ patch: patchOrDocumentId.serialize() });\n    if (isBuilder) {\n      const patch = patchOps(new ObservablePatch(patchOrDocumentId, {}, this.#client));\n      if (!(patch instanceof ObservablePatch))\n        throw new Error(\"function passed to `patch()` must return the patch\");\n      return this._add({ patch: patch.serialize() });\n    }\n    return this._add({ patch: { id: patchOrDocumentId, ...patchOps } });\n  }\n}\nconst projectHeader = \"X-Sanity-Project-ID\";\nfunction requestOptions(config, overrides = {}) {\n  const headers = {};\n  config.headers && Object.assign(headers, config.headers);\n  const token = overrides.token || config.token;\n  token && (headers.Authorization = `Bearer ${token}`), !overrides.useGlobalApi && !config.useProjectHostname && config.projectId && (headers[projectHeader] = config.projectId);\n  const withCredentials = !!(typeof overrides.withCredentials > \"u\" ? config.withCredentials : overrides.withCredentials), timeout = typeof overrides.timeout > \"u\" ? config.timeout : overrides.timeout;\n  return Object.assign({}, overrides, {\n    headers: Object.assign({}, headers, overrides.headers || {}),\n    timeout: typeof timeout > \"u\" ? 5 * 60 * 1e3 : timeout,\n    proxy: overrides.proxy || config.proxy,\n    json: !0,\n    withCredentials,\n    fetch: typeof overrides.fetch == \"object\" && typeof config.fetch == \"object\" ? { ...config.fetch, ...overrides.fetch } : overrides.fetch || config.fetch\n  });\n}\nconst encodeQueryString = ({\n  query,\n  params = {},\n  options = {}\n}) => {\n  const searchParams = new URLSearchParams(), { tag, includeMutations, returnQuery, ...opts } = options;\n  tag && searchParams.append(\"tag\", tag), searchParams.append(\"query\", query);\n  for (const [key, value] of Object.entries(params))\n    searchParams.append(`$${key}`, JSON.stringify(value));\n  for (const [key, value] of Object.entries(opts))\n    value && searchParams.append(key, `${value}`);\n  return returnQuery === !1 && searchParams.append(\"returnQuery\", \"false\"), includeMutations === !1 && searchParams.append(\"includeMutations\", \"false\"), `?${searchParams}`;\n}, excludeFalsey = (param, defValue) => param === !1 ? void 0 : typeof param > \"u\" ? defValue : param, getMutationQuery = (options = {}) => ({\n  dryRun: options.dryRun,\n  returnIds: !0,\n  returnDocuments: excludeFalsey(options.returnDocuments, !0),\n  visibility: options.visibility || \"sync\",\n  autoGenerateArrayKeys: options.autoGenerateArrayKeys,\n  skipCrossDatasetReferenceValidation: options.skipCrossDatasetReferenceValidation\n}), isResponse = (event) => event.type === \"response\", getBody = (event) => event.body, indexBy = (docs, attr) => docs.reduce((indexed, doc) => (indexed[attr(doc)] = doc, indexed), /* @__PURE__ */ Object.create(null)), getQuerySizeLimit = 11264;\nfunction _fetch(client, httpRequest, _stega, query, _params = {}, options = {}) {\n  const stega = \"stega\" in options ? {\n    ..._stega || {},\n    ...typeof options.stega == \"boolean\" ? { enabled: options.stega } : options.stega || {}\n  } : _stega, params = stega.enabled ? stegaClean(_params) : _params, mapResponse = options.filterResponse === !1 ? (res) => res : (res) => res.result, { cache, next, ...opts } = {\n    // Opt out of setting a `signal` on an internal `fetch` if one isn't provided.\n    // This is necessary in React Server Components to avoid opting out of Request Memoization.\n    useAbortSignal: typeof options.signal < \"u\",\n    // Set `resultSourceMap' when stega is enabled, as it's required for encoding.\n    resultSourceMap: stega.enabled ? \"withKeyArraySelector\" : options.resultSourceMap,\n    ...options,\n    // Default to not returning the query, unless `filterResponse` is `false`,\n    // or `returnQuery` is explicitly set. `true` is the default in Content Lake, so skip if truthy\n    returnQuery: options.filterResponse === !1 && options.returnQuery !== !1\n  }, reqOpts = typeof cache < \"u\" || typeof next < \"u\" ? { ...opts, fetch: { cache, next } } : opts, $request = _dataRequest(client, httpRequest, \"query\", { query, params }, reqOpts);\n  return stega.enabled ? $request.pipe(\n    combineLatestWith(\n      from(\n        import(\"./_chunks-es/stegaEncodeSourceMap.js\").then(function(n) {\n          return n.stegaEncodeSourceMap$1;\n        }).then(\n          ({ stegaEncodeSourceMap }) => stegaEncodeSourceMap\n        )\n      )\n    ),\n    map(\n      ([res, stegaEncodeSourceMap]) => {\n        const result = stegaEncodeSourceMap(res.result, res.resultSourceMap, stega);\n        return mapResponse({ ...res, result });\n      }\n    )\n  ) : $request.pipe(map(mapResponse));\n}\nfunction _getDocument(client, httpRequest, id, opts = {}) {\n  const docId = (() => {\n    if (!opts.releaseId)\n      return id;\n    const versionId = getVersionFromId(id);\n    if (!versionId) {\n      if (isDraftId(id))\n        throw new Error(\n          `The document ID (\\`${id}\\`) is a draft, but \\`options.releaseId\\` is set as \\`${opts.releaseId}\\``\n        );\n      return getVersionId(id, opts.releaseId);\n    }\n    if (versionId !== opts.releaseId)\n      throw new Error(\n        `The document ID (\\`${id}\\`) is already a version of \\`${versionId}\\` release, but this does not match the provided \\`options.releaseId\\` (\\`${opts.releaseId}\\`)`\n      );\n    return id;\n  })(), options = {\n    uri: _getDataUrl(client, \"doc\", docId),\n    json: !0,\n    tag: opts.tag,\n    signal: opts.signal\n  };\n  return _requestObservable(client, httpRequest, options).pipe(\n    filter(isResponse),\n    map((event) => event.body.documents && event.body.documents[0])\n  );\n}\nfunction _getDocuments(client, httpRequest, ids, opts = {}) {\n  const options = {\n    uri: _getDataUrl(client, \"doc\", ids.join(\",\")),\n    json: !0,\n    tag: opts.tag,\n    signal: opts.signal\n  };\n  return _requestObservable(client, httpRequest, options).pipe(\n    filter(isResponse),\n    map((event) => {\n      const indexed = indexBy(event.body.documents || [], (doc) => doc._id);\n      return ids.map((id) => indexed[id] || null);\n    })\n  );\n}\nfunction _getReleaseDocuments(client, httpRequest, releaseId, opts = {}) {\n  return _dataRequest(\n    client,\n    httpRequest,\n    \"query\",\n    {\n      query: \"*[sanity::partOfRelease($releaseId)]\",\n      params: {\n        releaseId\n      }\n    },\n    opts\n  );\n}\nfunction _createIfNotExists(client, httpRequest, doc, options) {\n  return requireDocumentId(\"createIfNotExists\", doc), _create(client, httpRequest, doc, \"createIfNotExists\", options);\n}\nfunction _createOrReplace(client, httpRequest, doc, options) {\n  return requireDocumentId(\"createOrReplace\", doc), _create(client, httpRequest, doc, \"createOrReplace\", options);\n}\nfunction _createVersion(client, httpRequest, doc, publishedId, options) {\n  return requireDocumentId(\"createVersion\", doc), requireDocumentType(\"createVersion\", doc), _action(client, httpRequest, {\n    actionType: \"sanity.action.document.version.create\",\n    publishedId,\n    document: doc\n  }, options);\n}\nfunction _delete(client, httpRequest, selection, options) {\n  return _dataRequest(\n    client,\n    httpRequest,\n    \"mutate\",\n    { mutations: [{ delete: getSelection(selection) }] },\n    options\n  );\n}\nfunction _discardVersion(client, httpRequest, versionId, purge = !1, options) {\n  return _action(client, httpRequest, {\n    actionType: \"sanity.action.document.version.discard\",\n    versionId,\n    purge\n  }, options);\n}\nfunction _replaceVersion(client, httpRequest, doc, options) {\n  return requireDocumentId(\"replaceVersion\", doc), requireDocumentType(\"replaceVersion\", doc), _action(client, httpRequest, {\n    actionType: \"sanity.action.document.version.replace\",\n    document: doc\n  }, options);\n}\nfunction _unpublishVersion(client, httpRequest, versionId, publishedId, options) {\n  return _action(client, httpRequest, {\n    actionType: \"sanity.action.document.version.unpublish\",\n    versionId,\n    publishedId\n  }, options);\n}\nfunction _mutate(client, httpRequest, mutations, options) {\n  let mut;\n  mutations instanceof Patch || mutations instanceof ObservablePatch ? mut = { patch: mutations.serialize() } : mutations instanceof Transaction || mutations instanceof ObservableTransaction ? mut = mutations.serialize() : mut = mutations;\n  const muts = Array.isArray(mut) ? mut : [mut], transactionId = options && options.transactionId || void 0;\n  return _dataRequest(client, httpRequest, \"mutate\", { mutations: muts, transactionId }, options);\n}\nfunction _action(client, httpRequest, actions, options) {\n  const acts = Array.isArray(actions) ? actions : [actions], transactionId = options && options.transactionId || void 0, skipCrossDatasetReferenceValidation = options && options.skipCrossDatasetReferenceValidation || void 0, dryRun = options && options.dryRun || void 0;\n  return _dataRequest(\n    client,\n    httpRequest,\n    \"actions\",\n    { actions: acts, transactionId, skipCrossDatasetReferenceValidation, dryRun },\n    options\n  );\n}\nfunction _dataRequest(client, httpRequest, endpoint, body, options = {}) {\n  const isMutation = endpoint === \"mutate\", isAction = endpoint === \"actions\", isQuery2 = endpoint === \"query\", strQuery = isMutation || isAction ? \"\" : encodeQueryString(body), useGet = !isMutation && !isAction && strQuery.length < getQuerySizeLimit, stringQuery = useGet ? strQuery : \"\", returnFirst = options.returnFirst, { timeout, token, tag, headers, returnQuery, lastLiveEventId, cacheMode } = options, uri = _getDataUrl(client, endpoint, stringQuery), reqOptions = {\n    method: useGet ? \"GET\" : \"POST\",\n    uri,\n    json: !0,\n    body: useGet ? void 0 : body,\n    query: isMutation && getMutationQuery(options),\n    timeout,\n    headers,\n    token,\n    tag,\n    returnQuery,\n    perspective: options.perspective,\n    resultSourceMap: options.resultSourceMap,\n    lastLiveEventId: Array.isArray(lastLiveEventId) ? lastLiveEventId[0] : lastLiveEventId,\n    cacheMode,\n    canUseCdn: isQuery2,\n    signal: options.signal,\n    fetch: options.fetch,\n    useAbortSignal: options.useAbortSignal,\n    useCdn: options.useCdn\n  };\n  return _requestObservable(client, httpRequest, reqOptions).pipe(\n    filter(isResponse),\n    map(getBody),\n    map((res) => {\n      if (!isMutation)\n        return res;\n      const results = res.results || [];\n      if (options.returnDocuments)\n        return returnFirst ? results[0] && results[0].document : results.map((mut) => mut.document);\n      const key = returnFirst ? \"documentId\" : \"documentIds\", ids = returnFirst ? results[0] && results[0].id : results.map((mut) => mut.id);\n      return {\n        transactionId: res.transactionId,\n        results,\n        [key]: ids\n      };\n    })\n  );\n}\nfunction _create(client, httpRequest, doc, op, options = {}) {\n  const mutation = { [op]: doc }, opts = Object.assign({ returnFirst: !0, returnDocuments: !0 }, options);\n  return _dataRequest(client, httpRequest, \"mutate\", { mutations: [mutation] }, opts);\n}\nconst hasDataConfig = (client) => client.config().dataset !== void 0 && client.config().projectId !== void 0 || client.config()[\"~experimental_resource\"] !== void 0, isQuery = (client, uri) => hasDataConfig(client) && uri.startsWith(_getDataUrl(client, \"query\")), isMutate = (client, uri) => hasDataConfig(client) && uri.startsWith(_getDataUrl(client, \"mutate\")), isDoc = (client, uri) => hasDataConfig(client) && uri.startsWith(_getDataUrl(client, \"doc\", \"\")), isListener = (client, uri) => hasDataConfig(client) && uri.startsWith(_getDataUrl(client, \"listen\")), isHistory = (client, uri) => hasDataConfig(client) && uri.startsWith(_getDataUrl(client, \"history\", \"\")), isData = (client, uri) => uri.startsWith(\"/data/\") || isQuery(client, uri) || isMutate(client, uri) || isDoc(client, uri) || isListener(client, uri) || isHistory(client, uri);\nfunction _requestObservable(client, httpRequest, options) {\n  const uri = options.url || options.uri, config = client.config(), canUseCdn = typeof options.canUseCdn > \"u\" ? [\"GET\", \"HEAD\"].indexOf(options.method || \"GET\") >= 0 && isData(client, uri) : options.canUseCdn;\n  let useCdn = (options.useCdn ?? config.useCdn) && canUseCdn;\n  const tag = options.tag && config.requestTagPrefix ? [config.requestTagPrefix, options.tag].join(\".\") : options.tag || config.requestTagPrefix;\n  if (tag && options.tag !== null && (options.query = { tag: requestTag(tag), ...options.query }), [\"GET\", \"HEAD\", \"POST\"].indexOf(options.method || \"GET\") >= 0 && isQuery(client, uri)) {\n    const resultSourceMap = options.resultSourceMap ?? config.resultSourceMap;\n    resultSourceMap !== void 0 && resultSourceMap !== !1 && (options.query = { resultSourceMap, ...options.query });\n    const perspectiveOption = options.perspective || config.perspective;\n    typeof perspectiveOption < \"u\" && (perspectiveOption === \"previewDrafts\" && printPreviewDraftsDeprecationWarning(), validateApiPerspective(perspectiveOption), options.query = {\n      perspective: Array.isArray(perspectiveOption) ? perspectiveOption.join(\",\") : perspectiveOption,\n      ...options.query\n    }, (Array.isArray(perspectiveOption) && perspectiveOption.length > 0 || // previewDrafts was renamed to drafts, but keep for backwards compat\n    perspectiveOption === \"previewDrafts\" || perspectiveOption === \"drafts\") && useCdn && (useCdn = !1, printCdnPreviewDraftsWarning())), options.lastLiveEventId && (options.query = { ...options.query, lastLiveEventId: options.lastLiveEventId }), options.returnQuery === !1 && (options.query = { returnQuery: \"false\", ...options.query }), useCdn && options.cacheMode == \"noStale\" && (options.query = { cacheMode: \"noStale\", ...options.query });\n  }\n  const reqOptions = requestOptions(\n    config,\n    Object.assign({}, options, {\n      url: _getUrl(client, uri, useCdn)\n    })\n  ), request = new Observable(\n    (subscriber) => httpRequest(reqOptions, config.requester).subscribe(subscriber)\n  );\n  return options.signal ? request.pipe(_withAbortSignal(options.signal)) : request;\n}\nfunction _request(client, httpRequest, options) {\n  return _requestObservable(client, httpRequest, options).pipe(\n    filter((event) => event.type === \"response\"),\n    map((event) => event.body)\n  );\n}\nfunction _getDataUrl(client, operation, path) {\n  const config = client.config();\n  if (config[\"~experimental_resource\"]) {\n    resourceConfig(config);\n    const resourceBase = resourceDataBase(config), uri2 = path !== void 0 ? `${operation}/${path}` : operation;\n    return `${resourceBase}/${uri2}`.replace(/\\/($|\\?)/, \"$1\");\n  }\n  const catalog = hasDataset(config), baseUri = `/${operation}/${catalog}`;\n  return `/data${path !== void 0 ? `${baseUri}/${path}` : baseUri}`.replace(/\\/($|\\?)/, \"$1\");\n}\nfunction _getUrl(client, uri, canUseCdn = !1) {\n  const { url, cdnUrl } = client.config();\n  return `${canUseCdn ? cdnUrl : url}/${uri.replace(/^\\//, \"\")}`;\n}\nfunction _withAbortSignal(signal) {\n  return (input) => new Observable((observer) => {\n    const abort = () => observer.error(_createAbortError(signal));\n    if (signal && signal.aborted) {\n      abort();\n      return;\n    }\n    const subscription = input.subscribe(observer);\n    return signal.addEventListener(\"abort\", abort), () => {\n      signal.removeEventListener(\"abort\", abort), subscription.unsubscribe();\n    };\n  });\n}\nconst isDomExceptionSupported = !!globalThis.DOMException;\nfunction _createAbortError(signal) {\n  if (isDomExceptionSupported)\n    return new DOMException(signal?.reason ?? \"The operation was aborted.\", \"AbortError\");\n  const error = new Error(signal?.reason ?? \"The operation was aborted.\");\n  return error.name = \"AbortError\", error;\n}\nconst resourceDataBase = (config) => {\n  if (!config[\"~experimental_resource\"])\n    throw new Error(\"`resource` must be provided to perform resource queries\");\n  const { type, id } = config[\"~experimental_resource\"];\n  switch (type) {\n    case \"dataset\": {\n      const segments = id.split(\".\");\n      if (segments.length !== 2)\n        throw new Error('Dataset ID must be in the format \"project.dataset\"');\n      return `/projects/${segments[0]}/datasets/${segments[1]}`;\n    }\n    case \"canvas\":\n      return `/canvases/${id}`;\n    case \"media-library\":\n      return `/media-libraries/${id}`;\n    case \"dashboard\":\n      return `/dashboards/${id}`;\n    default:\n      throw new Error(`Unsupported resource type: ${type.toString()}`);\n  }\n};\nfunction _generate(client, httpRequest, request) {\n  const dataset2 = hasDataset(client.config());\n  return _request(client, httpRequest, {\n    method: \"POST\",\n    uri: `/agent/action/generate/${dataset2}`,\n    body: request\n  });\n}\nfunction _transform(client, httpRequest, request) {\n  const dataset2 = hasDataset(client.config());\n  return _request(client, httpRequest, {\n    method: \"POST\",\n    uri: `/agent/action/transform/${dataset2}`,\n    body: request\n  });\n}\nfunction _translate(client, httpRequest, request) {\n  const dataset2 = hasDataset(client.config());\n  return _request(client, httpRequest, {\n    method: \"POST\",\n    uri: `/agent/action/translate/${dataset2}`,\n    body: request\n  });\n}\nclass ObservableAgentsActionClient {\n  #client;\n  #httpRequest;\n  constructor(client, httpRequest) {\n    this.#client = client, this.#httpRequest = httpRequest;\n  }\n  /**\n   * Run an instruction to generate content in a target document.\n   * @param request - instruction request\n   */\n  generate(request) {\n    return _generate(this.#client, this.#httpRequest, request);\n  }\n  /**\n   * Transform a target document based on a source.\n   * @param request - translation request\n   */\n  transform(request) {\n    return _transform(this.#client, this.#httpRequest, request);\n  }\n  /**\n   * Translate a target document based on a source.\n   * @param request - translation request\n   */\n  translate(request) {\n    return _translate(this.#client, this.#httpRequest, request);\n  }\n}\nclass AgentActionsClient {\n  #client;\n  #httpRequest;\n  constructor(client, httpRequest) {\n    this.#client = client, this.#httpRequest = httpRequest;\n  }\n  /**\n   * Run an instruction to generate content in a target document.\n   * @param request - instruction request\n   */\n  generate(request) {\n    return lastValueFrom(_generate(this.#client, this.#httpRequest, request));\n  }\n  /**\n   * Transform a target document based on a source.\n   * @param request - translation request\n   */\n  transform(request) {\n    return lastValueFrom(_transform(this.#client, this.#httpRequest, request));\n  }\n  /**\n   * Translate a target document based on a source.\n   * @param request - translation request\n   */\n  translate(request) {\n    return lastValueFrom(_translate(this.#client, this.#httpRequest, request));\n  }\n}\nclass ObservableAssetsClient {\n  #client;\n  #httpRequest;\n  constructor(client, httpRequest) {\n    this.#client = client, this.#httpRequest = httpRequest;\n  }\n  upload(assetType, body, options) {\n    return _upload(this.#client, this.#httpRequest, assetType, body, options);\n  }\n}\nclass AssetsClient {\n  #client;\n  #httpRequest;\n  constructor(client, httpRequest) {\n    this.#client = client, this.#httpRequest = httpRequest;\n  }\n  upload(assetType, body, options) {\n    const observable2 = _upload(this.#client, this.#httpRequest, assetType, body, options);\n    return lastValueFrom(\n      observable2.pipe(\n        filter((event) => event.type === \"response\"),\n        map(\n          (event) => event.body.document\n        )\n      )\n    );\n  }\n}\nfunction _upload(client, httpRequest, assetType, body, opts = {}) {\n  validateAssetType(assetType);\n  let meta = opts.extract || void 0;\n  meta && !meta.length && (meta = [\"none\"]);\n  const config = client.config(), options = optionsFromFile(opts, body), { tag, label, title, description, creditLine, filename, source } = options, query = {\n    label,\n    title,\n    description,\n    filename,\n    meta,\n    creditLine\n  };\n  return source && (query.sourceId = source.id, query.sourceName = source.name, query.sourceUrl = source.url), _requestObservable(client, httpRequest, {\n    tag,\n    method: \"POST\",\n    timeout: options.timeout || 0,\n    uri: buildAssetUploadUrl(config, assetType),\n    headers: options.contentType ? { \"Content-Type\": options.contentType } : {},\n    query,\n    body\n  });\n}\nfunction buildAssetUploadUrl(config, assetType) {\n  const assetTypeEndpoint = assetType === \"image\" ? \"images\" : \"files\";\n  if (config[\"~experimental_resource\"]) {\n    const { type, id } = config[\"~experimental_resource\"];\n    switch (type) {\n      case \"dataset\":\n        throw new Error(\n          \"Assets are not supported for dataset resources, yet. Configure the client with `{projectId: <projectId>, dataset: <datasetId>}` instead.\"\n        );\n      case \"canvas\":\n        return `/canvases/${id}/assets/${assetTypeEndpoint}`;\n      case \"media-library\":\n        return `/media-libraries/${id}/upload`;\n      case \"dashboard\":\n        return `/dashboards/${id}/assets/${assetTypeEndpoint}`;\n      default:\n        throw new Error(`Unsupported resource type: ${type.toString()}`);\n    }\n  }\n  const dataset2 = hasDataset(config);\n  return `assets/${assetTypeEndpoint}/${dataset2}`;\n}\nfunction optionsFromFile(opts, file) {\n  return typeof File > \"u\" || !(file instanceof File) ? opts : Object.assign(\n    {\n      filename: opts.preserveFilename === !1 ? void 0 : file.name,\n      contentType: file.type\n    },\n    opts\n  );\n}\nvar defaults = (obj, defaults2) => Object.keys(defaults2).concat(Object.keys(obj)).reduce((target, prop) => (target[prop] = typeof obj[prop] > \"u\" ? defaults2[prop] : obj[prop], target), {});\nconst pick = (obj, props) => props.reduce((selection, prop) => (typeof obj[prop] > \"u\" || (selection[prop] = obj[prop]), selection), {}), eventSourcePolyfill = defer(() => import(\"@sanity/eventsource\")).pipe(\n  map(({ default: EventSource2 }) => EventSource2),\n  shareReplay(1)\n);\nfunction reconnectOnConnectionFailure() {\n  return function(source) {\n    return source.pipe(\n      catchError((err, caught) => err instanceof ConnectionFailedError ? concat(of({ type: \"reconnect\" }), timer(1e3).pipe(mergeMap(() => caught))) : throwError(() => err))\n    );\n  };\n}\nconst MAX_URL_LENGTH = 14800, possibleOptions = [\n  \"includePreviousRevision\",\n  \"includeResult\",\n  \"includeMutations\",\n  \"includeAllVersions\",\n  \"visibility\",\n  \"effectFormat\",\n  \"tag\"\n], defaultOptions = {\n  includeResult: !0\n};\nfunction _listen(query, params, opts = {}) {\n  const { url, token, withCredentials, requestTagPrefix } = this.config(), tag = opts.tag && requestTagPrefix ? [requestTagPrefix, opts.tag].join(\".\") : opts.tag, options = { ...defaults(opts, defaultOptions), tag }, listenOpts = pick(options, possibleOptions), qs = encodeQueryString({ query, params, options: { tag, ...listenOpts } }), uri = `${url}${_getDataUrl(this, \"listen\", qs)}`;\n  if (uri.length > MAX_URL_LENGTH)\n    return throwError(() => new Error(\"Query too large for listener\"));\n  const listenFor = options.events ? options.events : [\"mutation\"], esOptions = {};\n  return withCredentials && (esOptions.withCredentials = !0), token && (esOptions.headers = {\n    Authorization: `Bearer ${token}`\n  }), connectEventSource(() => (\n    // use polyfill if there is no global EventSource or if we need to set headers\n    (typeof EventSource > \"u\" || esOptions.headers ? eventSourcePolyfill : of(EventSource)).pipe(map((EventSource2) => new EventSource2(uri, esOptions)))\n  ), listenFor).pipe(\n    reconnectOnConnectionFailure(),\n    filter((event) => listenFor.includes(event.type)),\n    map(\n      (event) => ({\n        type: event.type,\n        ...\"data\" in event ? event.data : {}\n      })\n    )\n  );\n}\nfunction shareReplayLatest(configOrPredicate, config) {\n  return _shareReplayLatest(\n    typeof configOrPredicate == \"function\" ? { predicate: configOrPredicate, ...config } : configOrPredicate\n  );\n}\nfunction _shareReplayLatest(config) {\n  return (source) => {\n    let latest, emitted = !1;\n    const { predicate, ...shareConfig } = config, wrapped = source.pipe(\n      tap((value) => {\n        config.predicate(value) && (emitted = !0, latest = value);\n      }),\n      finalize(() => {\n        emitted = !1, latest = void 0;\n      }),\n      share(shareConfig)\n    ), emitLatest = new Observable((subscriber) => {\n      emitted && subscriber.next(\n        // this cast is safe because of the emitted check which asserts that we got T from the source\n        latest\n      ), subscriber.complete();\n    });\n    return merge(wrapped, emitLatest);\n  };\n}\nconst requiredApiVersion = \"2021-03-25\";\nclass LiveClient {\n  #client;\n  constructor(client) {\n    this.#client = client;\n  }\n  /**\n   * Requires `apiVersion` to be `2021-03-25` or later.\n   */\n  events({\n    includeDrafts = !1,\n    tag: _tag\n  } = {}) {\n    resourceGuard(\"live\", this.#client.config());\n    const {\n      projectId: projectId2,\n      apiVersion: _apiVersion,\n      token,\n      withCredentials,\n      requestTagPrefix\n    } = this.#client.config(), apiVersion = _apiVersion.replace(/^v/, \"\");\n    if (apiVersion !== \"X\" && apiVersion < requiredApiVersion)\n      throw new Error(\n        `The live events API requires API version ${requiredApiVersion} or later. The current API version is ${apiVersion}. Please update your API version to use this feature.`\n      );\n    if (includeDrafts && !token && !withCredentials)\n      throw new Error(\n        \"The live events API requires a token or withCredentials when 'includeDrafts: true'. Please update your client configuration. The token should have the lowest possible access role.\"\n      );\n    const path = _getDataUrl(this.#client, \"live/events\"), url = new URL(this.#client.getUrl(path, !1)), tag = _tag && requestTagPrefix ? [requestTagPrefix, _tag].join(\".\") : _tag;\n    tag && url.searchParams.set(\"tag\", tag), includeDrafts && url.searchParams.set(\"includeDrafts\", \"true\");\n    const esOptions = {};\n    includeDrafts && token && (esOptions.headers = {\n      Authorization: `Bearer ${token}`\n    }), includeDrafts && withCredentials && (esOptions.withCredentials = !0);\n    const key = `${url.href}::${JSON.stringify(esOptions)}`, existing = eventsCache.get(key);\n    if (existing)\n      return existing;\n    const events = connectEventSource(() => (\n      // use polyfill if there is no global EventSource or if we need to set headers\n      (typeof EventSource > \"u\" || esOptions.headers ? eventSourcePolyfill : of(EventSource)).pipe(map((EventSource2) => new EventSource2(url.href, esOptions)))\n    ), [\n      \"message\",\n      \"restart\",\n      \"welcome\",\n      \"reconnect\",\n      \"goaway\"\n    ]).pipe(\n      reconnectOnConnectionFailure(),\n      map((event) => {\n        if (event.type === \"message\") {\n          const { data, ...rest } = event;\n          return { ...rest, tags: data.tags };\n        }\n        return event;\n      })\n    ), checkCors = fetchObservable(url, {\n      method: \"OPTIONS\",\n      mode: \"cors\",\n      credentials: esOptions.withCredentials ? \"include\" : \"omit\",\n      headers: esOptions.headers\n    }).pipe(\n      mergeMap(() => EMPTY),\n      catchError(() => {\n        throw new CorsOriginError({ projectId: projectId2 });\n      })\n    ), observable2 = concat(checkCors, events).pipe(\n      finalize$1(() => eventsCache.delete(key)),\n      shareReplayLatest({\n        predicate: (event) => event.type === \"welcome\"\n      })\n    );\n    return eventsCache.set(key, observable2), observable2;\n  }\n}\nfunction fetchObservable(url, init) {\n  return new Observable((observer) => {\n    const controller = new AbortController(), signal = controller.signal;\n    return fetch(url, { ...init, signal: controller.signal }).then(\n      (response) => {\n        observer.next(response), observer.complete();\n      },\n      (err) => {\n        signal.aborted || observer.error(err);\n      }\n    ), () => controller.abort();\n  });\n}\nconst eventsCache = /* @__PURE__ */ new Map();\nclass ObservableDatasetsClient {\n  #client;\n  #httpRequest;\n  constructor(client, httpRequest) {\n    this.#client = client, this.#httpRequest = httpRequest;\n  }\n  /**\n   * Create a new dataset with the given name\n   *\n   * @param name - Name of the dataset to create\n   * @param options - Options for the dataset\n   */\n  create(name, options) {\n    return _modify(this.#client, this.#httpRequest, \"PUT\", name, options);\n  }\n  /**\n   * Edit a dataset with the given name\n   *\n   * @param name - Name of the dataset to edit\n   * @param options - New options for the dataset\n   */\n  edit(name, options) {\n    return _modify(this.#client, this.#httpRequest, \"PATCH\", name, options);\n  }\n  /**\n   * Delete a dataset with the given name\n   *\n   * @param name - Name of the dataset to delete\n   */\n  delete(name) {\n    return _modify(this.#client, this.#httpRequest, \"DELETE\", name);\n  }\n  /**\n   * Fetch a list of datasets for the configured project\n   */\n  list() {\n    return _request(this.#client, this.#httpRequest, {\n      uri: \"/datasets\",\n      tag: null\n    });\n  }\n}\nclass DatasetsClient {\n  #client;\n  #httpRequest;\n  constructor(client, httpRequest) {\n    this.#client = client, this.#httpRequest = httpRequest;\n  }\n  /**\n   * Create a new dataset with the given name\n   *\n   * @param name - Name of the dataset to create\n   * @param options - Options for the dataset\n   */\n  create(name, options) {\n    return resourceGuard(\"dataset\", this.#client.config()), lastValueFrom(\n      _modify(this.#client, this.#httpRequest, \"PUT\", name, options)\n    );\n  }\n  /**\n   * Edit a dataset with the given name\n   *\n   * @param name - Name of the dataset to edit\n   * @param options - New options for the dataset\n   */\n  edit(name, options) {\n    return resourceGuard(\"dataset\", this.#client.config()), lastValueFrom(\n      _modify(this.#client, this.#httpRequest, \"PATCH\", name, options)\n    );\n  }\n  /**\n   * Delete a dataset with the given name\n   *\n   * @param name - Name of the dataset to delete\n   */\n  delete(name) {\n    return resourceGuard(\"dataset\", this.#client.config()), lastValueFrom(_modify(this.#client, this.#httpRequest, \"DELETE\", name));\n  }\n  /**\n   * Fetch a list of datasets for the configured project\n   */\n  list() {\n    return resourceGuard(\"dataset\", this.#client.config()), lastValueFrom(\n      _request(this.#client, this.#httpRequest, { uri: \"/datasets\", tag: null })\n    );\n  }\n}\nfunction _modify(client, httpRequest, method, name, options) {\n  return resourceGuard(\"dataset\", client.config()), dataset(name), _request(client, httpRequest, {\n    method,\n    uri: `/datasets/${name}`,\n    body: options,\n    tag: null\n  });\n}\nclass ObservableProjectsClient {\n  #client;\n  #httpRequest;\n  constructor(client, httpRequest) {\n    this.#client = client, this.#httpRequest = httpRequest;\n  }\n  list(options) {\n    resourceGuard(\"projects\", this.#client.config());\n    const uri = options?.includeMembers === !1 ? \"/projects?includeMembers=false\" : \"/projects\";\n    return _request(this.#client, this.#httpRequest, { uri });\n  }\n  /**\n   * Fetch a project by project ID\n   *\n   * @param projectId - ID of the project to fetch\n   */\n  getById(projectId2) {\n    return resourceGuard(\"projects\", this.#client.config()), _request(this.#client, this.#httpRequest, { uri: `/projects/${projectId2}` });\n  }\n}\nclass ProjectsClient {\n  #client;\n  #httpRequest;\n  constructor(client, httpRequest) {\n    this.#client = client, this.#httpRequest = httpRequest;\n  }\n  list(options) {\n    resourceGuard(\"projects\", this.#client.config());\n    const uri = options?.includeMembers === !1 ? \"/projects?includeMembers=false\" : \"/projects\";\n    return lastValueFrom(_request(this.#client, this.#httpRequest, { uri }));\n  }\n  /**\n   * Fetch a project by project ID\n   *\n   * @param projectId - ID of the project to fetch\n   */\n  getById(projectId2) {\n    return resourceGuard(\"projects\", this.#client.config()), lastValueFrom(\n      _request(this.#client, this.#httpRequest, { uri: `/projects/${projectId2}` })\n    );\n  }\n}\nconst generateReleaseId = customAlphabet(\n  \"abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789\",\n  8\n), getDocumentVersionId = (publishedId, releaseId) => releaseId ? getVersionId(publishedId, releaseId) : getDraftId(publishedId);\nfunction deriveDocumentVersionId(op, {\n  releaseId,\n  publishedId,\n  document\n}) {\n  if (publishedId && document._id) {\n    const versionId = getDocumentVersionId(publishedId, releaseId);\n    return validateVersionIdMatch(versionId, document), versionId;\n  }\n  if (document._id) {\n    const isDraft = isDraftId(document._id), isVersion = isVersionId(document._id);\n    if (!isDraft && !isVersion)\n      throw new Error(\n        `\\`${op}()\\` requires a document with an \\`_id\\` that is a version or draft ID`\n      );\n    if (releaseId) {\n      if (isDraft)\n        throw new Error(\n          `\\`${op}()\\` was called with a document ID (\\`${document._id}\\`) that is a draft ID, but a release ID (\\`${releaseId}\\`) was also provided.`\n        );\n      const builtVersionId = getVersionFromId(document._id);\n      if (builtVersionId !== releaseId)\n        throw new Error(\n          `\\`${op}()\\` was called with a document ID (\\`${document._id}\\`) that is a version ID, but the release ID (\\`${releaseId}\\`) does not match the document's version ID (\\`${builtVersionId}\\`).`\n        );\n    }\n    return document._id;\n  }\n  if (publishedId)\n    return getDocumentVersionId(publishedId, releaseId);\n  throw new Error(`\\`${op}()\\` requires either a publishedId or a document with an \\`_id\\``);\n}\nconst getArgs = (releaseOrOptions, maybeOptions) => {\n  if (typeof releaseOrOptions == \"object\" && releaseOrOptions !== null && (\"releaseId\" in releaseOrOptions || \"metadata\" in releaseOrOptions)) {\n    const { releaseId = generateReleaseId(), metadata = {} } = releaseOrOptions;\n    return [releaseId, metadata, maybeOptions];\n  }\n  return [generateReleaseId(), {}, releaseOrOptions];\n}, createRelease = (releaseOrOptions, maybeOptions) => {\n  const [releaseId, metadata, options] = getArgs(releaseOrOptions, maybeOptions), finalMetadata = {\n    ...metadata,\n    releaseType: metadata.releaseType || \"undecided\"\n  };\n  return { action: {\n    actionType: \"sanity.action.release.create\",\n    releaseId,\n    metadata: finalMetadata\n  }, options };\n};\nclass ObservableReleasesClient {\n  #client;\n  #httpRequest;\n  constructor(client, httpRequest) {\n    this.#client = client, this.#httpRequest = httpRequest;\n  }\n  /**\n   * @public\n   *\n   * Retrieve a release by id.\n   *\n   * @category Releases\n   *\n   * @param params - Release action parameters:\n   *   - `releaseId` - The id of the release to retrieve.\n   * @param options - Additional query options including abort signal and query tag.\n   * @returns An observable that resolves to the release document {@link ReleaseDocument}.\n   *\n   * @example Retrieving a release by id\n   * ```ts\n   * client.observable.releases.get({releaseId: 'my-release'}).pipe(\n   *   tap((release) => console.log(release)),\n   *   // {\n   *   //   _id: '_.releases.my-release',\n   *   //   name: 'my-release'\n   *   //   _type: 'system.release',\n   *   //   metadata: {releaseType: 'asap'},\n   *   //   _createdAt: '2021-01-01T00:00:00.000Z',\n   *   //   ...\n   *   // }\n   * ).subscribe()\n   * ```\n   */\n  get({ releaseId }, options) {\n    return _getDocument(\n      this.#client,\n      this.#httpRequest,\n      `_.releases.${releaseId}`,\n      options\n    );\n  }\n  create(releaseOrOptions, maybeOptions) {\n    const { action, options } = createRelease(releaseOrOptions, maybeOptions), { releaseId, metadata } = action;\n    return _action(this.#client, this.#httpRequest, action, options).pipe(\n      map$1((actionResult) => ({\n        ...actionResult,\n        releaseId,\n        metadata\n      }))\n    );\n  }\n  /**\n   * @public\n   *\n   * Edits an existing release, updating the metadata.\n   *\n   * @category Releases\n   *\n   * @param params - Release action parameters:\n   *   - `releaseId` - The id of the release to edit.\n   *   - `patch` - The patch operation to apply on the release metadata {@link PatchMutationOperation}.\n   * @param options - Additional action options.\n   * @returns An observable that resolves to the `transactionId`.\n   */\n  edit({ releaseId, patch }, options) {\n    const editAction = {\n      actionType: \"sanity.action.release.edit\",\n      releaseId,\n      patch\n    };\n    return _action(this.#client, this.#httpRequest, editAction, options);\n  }\n  /**\n   * @public\n   *\n   * Publishes all documents in a release at once. For larger releases the effect of the publish\n   * will be visible immediately when querying but the removal of the `versions.<releasesId>.*`\n   * documents and creation of the corresponding published documents with the new content may\n   * take some time.\n   *\n   * During this period both the source and target documents are locked and cannot be\n   * modified through any other means.\n   *\n   * @category Releases\n   *\n   * @param params - Release action parameters:\n   *   - `releaseId` - The id of the release to publish.\n   * @param options - Additional action options.\n   * @returns An observable that resolves to the `transactionId`.\n   */\n  publish({ releaseId }, options) {\n    const publishAction = {\n      actionType: \"sanity.action.release.publish\",\n      releaseId\n    };\n    return _action(this.#client, this.#httpRequest, publishAction, options);\n  }\n  /**\n   * @public\n   *\n   * An archive action removes an active release. The documents that comprise the release\n   * are deleted and therefore no longer queryable.\n   *\n   * While the documents remain in retention the last version can still be accessed using document history endpoint.\n   *\n   * @category Releases\n   *\n   * @param params - Release action parameters:\n   *   - `releaseId` - The id of the release to archive.\n   * @param options - Additional action options.\n   * @returns An observable that resolves to the `transactionId`.\n   */\n  archive({ releaseId }, options) {\n    const archiveAction = {\n      actionType: \"sanity.action.release.archive\",\n      releaseId\n    };\n    return _action(this.#client, this.#httpRequest, archiveAction, options);\n  }\n  /**\n   * @public\n   *\n   * An unarchive action restores an archived release and all documents\n   * with the content they had just prior to archiving.\n   *\n   * @category Releases\n   *\n   * @param params - Release action parameters:\n   *   - `releaseId` - The id of the release to unarchive.\n   * @param options - Additional action options.\n   * @returns An observable that resolves to the `transactionId`.\n   */\n  unarchive({ releaseId }, options) {\n    const unarchiveAction = {\n      actionType: \"sanity.action.release.unarchive\",\n      releaseId\n    };\n    return _action(this.#client, this.#httpRequest, unarchiveAction, options);\n  }\n  /**\n   * @public\n   *\n   * A schedule action queues a release for publishing at the given future time.\n   * The release is locked such that no documents in the release can be modified and\n   * no documents that it references can be deleted as this would make the publish fail.\n   * At the given time, the same logic as for the publish action is triggered.\n   *\n   * @category Releases\n   *\n   * @param params - Release action parameters:\n   *   - `releaseId` - The id of the release to schedule.\n   *   - `publishAt` - The serialised date and time to publish the release. If the `publishAt` is in the past, the release will be published immediately.\n   * @param options - Additional action options.\n   * @returns An observable that resolves to the `transactionId`.\n   */\n  schedule({ releaseId, publishAt }, options) {\n    const scheduleAction = {\n      actionType: \"sanity.action.release.schedule\",\n      releaseId,\n      publishAt\n    };\n    return _action(this.#client, this.#httpRequest, scheduleAction, options);\n  }\n  /**\n   * @public\n   *\n   * An unschedule action stops a release from being published.\n   * The documents in the release are considered unlocked and can be edited again.\n   * This may fail if another release is scheduled to be published after this one and\n   * has a reference to a document created by this one.\n   *\n   * @category Releases\n   *\n   * @param params - Release action parameters:\n   *   - `releaseId` - The id of the release to unschedule.\n   * @param options - Additional action options.\n   * @returns An observable that resolves to the `transactionId`.\n   */\n  unschedule({ releaseId }, options) {\n    const unscheduleAction = {\n      actionType: \"sanity.action.release.unschedule\",\n      releaseId\n    };\n    return _action(this.#client, this.#httpRequest, unscheduleAction, options);\n  }\n  /**\n   * @public\n   *\n   * A delete action removes a published or archived release.\n   * The backing system document will be removed from the dataset.\n   *\n   * @category Releases\n   *\n   * @param params - Release action parameters:\n   *   - `releaseId` - The id of the release to delete.\n   * @param options - Additional action options.\n   * @returns An observable that resolves to the `transactionId`.\n   */\n  delete({ releaseId }, options) {\n    const deleteAction = {\n      actionType: \"sanity.action.release.delete\",\n      releaseId\n    };\n    return _action(this.#client, this.#httpRequest, deleteAction, options);\n  }\n  /**\n   * @public\n   *\n   * Fetch the documents in a release by release id.\n   *\n   * @category Releases\n   *\n   * @param params - Release action parameters:\n   *   - `releaseId` - The id of the release to fetch documents for.\n   * @param options - Additional mutation options {@link BaseMutationOptions}.\n   * @returns An observable that resolves to the documents in the release.\n   */\n  fetchDocuments({ releaseId }, options) {\n    return _getReleaseDocuments(this.#client, this.#httpRequest, releaseId, options);\n  }\n}\nclass ReleasesClient {\n  #client;\n  #httpRequest;\n  constructor(client, httpRequest) {\n    this.#client = client, this.#httpRequest = httpRequest;\n  }\n  /**\n   * @public\n   *\n   * Retrieve a release by id.\n   *\n   * @category Releases\n   *\n   * @param params - Release action parameters:\n   *   - `releaseId` - The id of the release to retrieve.\n   * @param options - Additional query options including abort signal and query tag.\n   * @returns A promise that resolves to the release document {@link ReleaseDocument}.\n   *\n   * @example Retrieving a release by id\n   * ```ts\n   * const release = await client.releases.get({releaseId: 'my-release'})\n   * console.log(release)\n   * // {\n   * //   _id: '_.releases.my-release',\n   * //   name: 'my-release'\n   * //   _type: 'system.release',\n   * //   metadata: {releaseType: 'asap'},\n   * //   _createdAt: '2021-01-01T00:00:00.000Z',\n   * //   ...\n   * // }\n   * ```\n   */\n  get({ releaseId }, options) {\n    return lastValueFrom(\n      _getDocument(\n        this.#client,\n        this.#httpRequest,\n        `_.releases.${releaseId}`,\n        options\n      )\n    );\n  }\n  async create(releaseOrOptions, maybeOptions) {\n    const { action, options } = createRelease(releaseOrOptions, maybeOptions), { releaseId, metadata } = action;\n    return { ...await lastValueFrom(\n      _action(this.#client, this.#httpRequest, action, options)\n    ), releaseId, metadata };\n  }\n  /**\n   * @public\n   *\n   * Edits an existing release, updating the metadata.\n   *\n   * @category Releases\n   *\n   * @param params - Release action parameters:\n   *   - `releaseId` - The id of the release to edit.\n   *   - `patch` - The patch operation to apply on the release metadata {@link PatchMutationOperation}.\n   * @param options - Additional action options.\n   * @returns A promise that resolves to the `transactionId`.\n   */\n  edit({ releaseId, patch }, options) {\n    const editAction = {\n      actionType: \"sanity.action.release.edit\",\n      releaseId,\n      patch\n    };\n    return lastValueFrom(_action(this.#client, this.#httpRequest, editAction, options));\n  }\n  /**\n   * @public\n   *\n   * Publishes all documents in a release at once. For larger releases the effect of the publish\n   * will be visible immediately when querying but the removal of the `versions.<releasesId>.*`\n   * documents and creation of the corresponding published documents with the new content may\n   * take some time.\n   *\n   * During this period both the source and target documents are locked and cannot be\n   * modified through any other means.\n   *\n   * @category Releases\n   *\n   * @param params - Release action parameters:\n   *   - `releaseId` - The id of the release to publish.\n   * @param options - Additional action options.\n   * @returns A promise that resolves to the `transactionId`.\n   */\n  publish({ releaseId }, options) {\n    const publishAction = {\n      actionType: \"sanity.action.release.publish\",\n      releaseId\n    };\n    return lastValueFrom(_action(this.#client, this.#httpRequest, publishAction, options));\n  }\n  /**\n   * @public\n   *\n   * An archive action removes an active release. The documents that comprise the release\n   * are deleted and therefore no longer queryable.\n   *\n   * While the documents remain in retention the last version can still be accessed using document history endpoint.\n   *\n   * @category Releases\n   *\n   * @param params - Release action parameters:\n   *   - `releaseId` - The id of the release to archive.\n   * @param options - Additional action options.\n   * @returns A promise that resolves to the `transactionId`.\n   */\n  archive({ releaseId }, options) {\n    const archiveAction = {\n      actionType: \"sanity.action.release.archive\",\n      releaseId\n    };\n    return lastValueFrom(_action(this.#client, this.#httpRequest, archiveAction, options));\n  }\n  /**\n   * @public\n   *\n   * An unarchive action restores an archived release and all documents\n   * with the content they had just prior to archiving.\n   *\n   * @category Releases\n   *\n   * @param params - Release action parameters:\n   *   - `releaseId` - The id of the release to unarchive.\n   * @param options - Additional action options.\n   * @returns A promise that resolves to the `transactionId`.\n   */\n  unarchive({ releaseId }, options) {\n    const unarchiveAction = {\n      actionType: \"sanity.action.release.unarchive\",\n      releaseId\n    };\n    return lastValueFrom(_action(this.#client, this.#httpRequest, unarchiveAction, options));\n  }\n  /**\n   * @public\n   *\n   * A schedule action queues a release for publishing at the given future time.\n   * The release is locked such that no documents in the release can be modified and\n   * no documents that it references can be deleted as this would make the publish fail.\n   * At the given time, the same logic as for the publish action is triggered.\n   *\n   * @category Releases\n   *\n   * @param params - Release action parameters:\n   *   - `releaseId` - The id of the release to schedule.\n   *   - `publishAt` - The serialised date and time to publish the release. If the `publishAt` is in the past, the release will be published immediately.\n   * @param options - Additional action options.\n   * @returns A promise that resolves to the `transactionId`.\n   */\n  schedule({ releaseId, publishAt }, options) {\n    const scheduleAction = {\n      actionType: \"sanity.action.release.schedule\",\n      releaseId,\n      publishAt\n    };\n    return lastValueFrom(_action(this.#client, this.#httpRequest, scheduleAction, options));\n  }\n  /**\n   * @public\n   *\n   * An unschedule action stops a release from being published.\n   * The documents in the release are considered unlocked and can be edited again.\n   * This may fail if another release is scheduled to be published after this one and\n   * has a reference to a document created by this one.\n   *\n   * @category Releases\n   *\n   * @param params - Release action parameters:\n   *   - `releaseId` - The id of the release to unschedule.\n   * @param options - Additional action options.\n   * @returns A promise that resolves to the `transactionId`.\n   */\n  unschedule({ releaseId }, options) {\n    const unscheduleAction = {\n      actionType: \"sanity.action.release.unschedule\",\n      releaseId\n    };\n    return lastValueFrom(_action(this.#client, this.#httpRequest, unscheduleAction, options));\n  }\n  /**\n   * @public\n   *\n   * A delete action removes a published or archived release.\n   * The backing system document will be removed from the dataset.\n   *\n   * @category Releases\n   *\n   * @param params - Release action parameters:\n   *   - `releaseId` - The id of the release to delete.\n   * @param options - Additional action options.\n   * @returns A promise that resolves to the `transactionId`.\n   */\n  delete({ releaseId }, options) {\n    const deleteAction = {\n      actionType: \"sanity.action.release.delete\",\n      releaseId\n    };\n    return lastValueFrom(_action(this.#client, this.#httpRequest, deleteAction, options));\n  }\n  /**\n   * @public\n   *\n   * Fetch the documents in a release by release id.\n   *\n   * @category Releases\n   *\n   * @param params - Release action parameters:\n   *   - `releaseId` - The id of the release to fetch documents for.\n   * @param options - Additional mutation options {@link BaseMutationOptions}.\n   * @returns A promise that resolves to the documents in the release.\n   */\n  fetchDocuments({ releaseId }, options) {\n    return lastValueFrom(_getReleaseDocuments(this.#client, this.#httpRequest, releaseId, options));\n  }\n}\nclass ObservableUsersClient {\n  #client;\n  #httpRequest;\n  constructor(client, httpRequest) {\n    this.#client = client, this.#httpRequest = httpRequest;\n  }\n  /**\n   * Fetch a user by user ID\n   *\n   * @param id - User ID of the user to fetch. If `me` is provided, a minimal response including the users role is returned.\n   */\n  getById(id) {\n    return _request(\n      this.#client,\n      this.#httpRequest,\n      { uri: `/users/${id}` }\n    );\n  }\n}\nclass UsersClient {\n  #client;\n  #httpRequest;\n  constructor(client, httpRequest) {\n    this.#client = client, this.#httpRequest = httpRequest;\n  }\n  /**\n   * Fetch a user by user ID\n   *\n   * @param id - User ID of the user to fetch. If `me` is provided, a minimal response including the users role is returned.\n   */\n  getById(id) {\n    return lastValueFrom(\n      _request(this.#client, this.#httpRequest, {\n        uri: `/users/${id}`\n      })\n    );\n  }\n}\nclass ObservableSanityClient {\n  assets;\n  datasets;\n  live;\n  projects;\n  users;\n  agent;\n  releases;\n  /**\n   * Private properties\n   */\n  #clientConfig;\n  #httpRequest;\n  /**\n   * Instance properties\n   */\n  listen = _listen;\n  constructor(httpRequest, config = defaultConfig) {\n    this.config(config), this.#httpRequest = httpRequest, this.assets = new ObservableAssetsClient(this, this.#httpRequest), this.datasets = new ObservableDatasetsClient(this, this.#httpRequest), this.live = new LiveClient(this), this.projects = new ObservableProjectsClient(this, this.#httpRequest), this.users = new ObservableUsersClient(this, this.#httpRequest), this.agent = {\n      action: new ObservableAgentsActionClient(this, this.#httpRequest)\n    }, this.releases = new ObservableReleasesClient(this, this.#httpRequest);\n  }\n  /**\n   * Clone the client - returns a new instance\n   */\n  clone() {\n    return new ObservableSanityClient(this.#httpRequest, this.config());\n  }\n  config(newConfig) {\n    if (newConfig === void 0)\n      return { ...this.#clientConfig };\n    if (this.#clientConfig && this.#clientConfig.allowReconfigure === !1)\n      throw new Error(\n        \"Existing client instance cannot be reconfigured - use `withConfig(newConfig)` to return a new client\"\n      );\n    return this.#clientConfig = initConfig(newConfig, this.#clientConfig || {}), this;\n  }\n  /**\n   * Clone the client with a new (partial) configuration.\n   *\n   * @param newConfig - New client configuration properties, shallowly merged with existing configuration\n   */\n  withConfig(newConfig) {\n    const thisConfig = this.config();\n    return new ObservableSanityClient(this.#httpRequest, {\n      ...thisConfig,\n      ...newConfig,\n      stega: {\n        ...thisConfig.stega || {},\n        ...typeof newConfig?.stega == \"boolean\" ? { enabled: newConfig.stega } : newConfig?.stega || {}\n      }\n    });\n  }\n  fetch(query, params, options) {\n    return _fetch(\n      this,\n      this.#httpRequest,\n      this.#clientConfig.stega,\n      query,\n      params,\n      options\n    );\n  }\n  /**\n   * Fetch a single document with the given ID.\n   *\n   * @param id - Document ID to fetch\n   * @param options - Request options\n   */\n  getDocument(id, options) {\n    return _getDocument(this, this.#httpRequest, id, options);\n  }\n  /**\n   * Fetch multiple documents in one request.\n   * Should be used sparingly - performing a query is usually a better option.\n   * The order/position of documents is preserved based on the original array of IDs.\n   * If any of the documents are missing, they will be replaced by a `null` entry in the returned array\n   *\n   * @param ids - Document IDs to fetch\n   * @param options - Request options\n   */\n  getDocuments(ids, options) {\n    return _getDocuments(this, this.#httpRequest, ids, options);\n  }\n  create(document, options) {\n    return _create(this, this.#httpRequest, document, \"create\", options);\n  }\n  createIfNotExists(document, options) {\n    return _createIfNotExists(this, this.#httpRequest, document, options);\n  }\n  createOrReplace(document, options) {\n    return _createOrReplace(this, this.#httpRequest, document, options);\n  }\n  createVersion({\n    document,\n    publishedId,\n    releaseId\n  }, options) {\n    const documentVersionId = deriveDocumentVersionId(\"createVersion\", {\n      document,\n      publishedId,\n      releaseId\n    }), documentVersion = { ...document, _id: documentVersionId }, versionPublishedId = publishedId || getPublishedId(document._id);\n    return _createVersion(\n      this,\n      this.#httpRequest,\n      documentVersion,\n      versionPublishedId,\n      options\n    );\n  }\n  delete(selection, options) {\n    return _delete(this, this.#httpRequest, selection, options);\n  }\n  /**\n   * @public\n   *\n   * Deletes the draft or release version of a document.\n   *\n   * @remarks\n   * * Discarding a version with no `releaseId` will discard the draft version of the published document.\n   * * If the draft or release version does not exist, any error will throw.\n   *\n   * @param params - Version action parameters:\n   *   - `releaseId` - The ID of the release to discard the document from.\n   *   - `publishedId` - The published ID of the document to discard.\n   * @param purge - if `true` the document history is also discarded.\n   * @param options - Additional action options.\n   * @returns an observable that resolves to the `transactionId`.\n   *\n   * @example Discarding a release version of a document\n   * ```ts\n   * client.observable.discardVersion({publishedId: 'myDocument', releaseId: 'myRelease'})\n   * // The document with the ID `versions.myRelease.myDocument` will be discarded.\n   * ```\n   *\n   * @example Discarding a draft version of a document\n   * ```ts\n   * client.observable.discardVersion({publishedId: 'myDocument'})\n   * // The document with the ID `drafts.myDocument` will be discarded.\n   * ```\n   */\n  discardVersion({ releaseId, publishedId }, purge, options) {\n    const documentVersionId = getDocumentVersionId(publishedId, releaseId);\n    return _discardVersion(this, this.#httpRequest, documentVersionId, purge, options);\n  }\n  replaceVersion({\n    document,\n    publishedId,\n    releaseId\n  }, options) {\n    const documentVersionId = deriveDocumentVersionId(\"replaceVersion\", {\n      document,\n      publishedId,\n      releaseId\n    }), documentVersion = { ...document, _id: documentVersionId };\n    return _replaceVersion(this, this.#httpRequest, documentVersion, options);\n  }\n  /**\n   * @public\n   *\n   * Used to indicate when a document within a release should be unpublished when\n   * the release is run.\n   *\n   * @remarks\n   * * If the published document does not exist, an error will be thrown.\n   *\n   * @param params - Version action parameters:\n   *   - `releaseId` - The ID of the release to unpublish the document from.\n   *   - `publishedId` - The published ID of the document to unpublish.\n   * @param options - Additional action options.\n   * @returns an observable that resolves to the `transactionId`.\n   *\n   * @example Unpublishing a release version of a published document\n   * ```ts\n   * client.observable.unpublishVersion({publishedId: 'myDocument', releaseId: 'myRelease'})\n   * // The document with the ID `versions.myRelease.myDocument` will be unpublished. when `myRelease` is run.\n   * ```\n   */\n  unpublishVersion({ releaseId, publishedId }, options) {\n    const versionId = getVersionId(publishedId, releaseId);\n    return _unpublishVersion(this, this.#httpRequest, versionId, publishedId, options);\n  }\n  mutate(operations, options) {\n    return _mutate(this, this.#httpRequest, operations, options);\n  }\n  /**\n   * Create a new buildable patch of operations to perform\n   *\n   * @param selection - Document ID, an array of document IDs, or an object with `query` and optional `params`, defining which document(s) to patch\n   * @param operations - Optional object of patch operations to initialize the patch instance with\n   * @returns Patch instance - call `.commit()` to perform the operations defined\n   */\n  patch(selection, operations) {\n    return new ObservablePatch(selection, operations, this);\n  }\n  /**\n   * Create a new transaction of mutations\n   *\n   * @param operations - Optional array of mutation operations to initialize the transaction instance with\n   */\n  transaction(operations) {\n    return new ObservableTransaction(operations, this);\n  }\n  /**\n   * Perform action operations against the configured dataset\n   *\n   * @param operations - Action operation(s) to execute\n   * @param options - Action options\n   */\n  action(operations, options) {\n    return _action(this, this.#httpRequest, operations, options);\n  }\n  /**\n   * Perform an HTTP request against the Sanity API\n   *\n   * @param options - Request options\n   */\n  request(options) {\n    return _request(this, this.#httpRequest, options);\n  }\n  /**\n   * Get a Sanity API URL for the URI provided\n   *\n   * @param uri - URI/path to build URL for\n   * @param canUseCdn - Whether or not to allow using the API CDN for this route\n   */\n  getUrl(uri, canUseCdn) {\n    return _getUrl(this, uri, canUseCdn);\n  }\n  /**\n   * Get a Sanity API URL for the data operation and path provided\n   *\n   * @param operation - Data operation (eg `query`, `mutate`, `listen` or similar)\n   * @param path - Path to append after the operation\n   */\n  getDataUrl(operation, path) {\n    return _getDataUrl(this, operation, path);\n  }\n}\nclass SanityClient {\n  assets;\n  datasets;\n  live;\n  projects;\n  users;\n  agent;\n  releases;\n  /**\n   * Observable version of the Sanity client, with the same configuration as the promise-based one\n   */\n  observable;\n  /**\n   * Private properties\n   */\n  #clientConfig;\n  #httpRequest;\n  /**\n   * Instance properties\n   */\n  listen = _listen;\n  constructor(httpRequest, config = defaultConfig) {\n    this.config(config), this.#httpRequest = httpRequest, this.assets = new AssetsClient(this, this.#httpRequest), this.datasets = new DatasetsClient(this, this.#httpRequest), this.live = new LiveClient(this), this.projects = new ProjectsClient(this, this.#httpRequest), this.users = new UsersClient(this, this.#httpRequest), this.agent = {\n      action: new AgentActionsClient(this, this.#httpRequest)\n    }, this.releases = new ReleasesClient(this, this.#httpRequest), this.observable = new ObservableSanityClient(httpRequest, config);\n  }\n  /**\n   * Clone the client - returns a new instance\n   */\n  clone() {\n    return new SanityClient(this.#httpRequest, this.config());\n  }\n  config(newConfig) {\n    if (newConfig === void 0)\n      return { ...this.#clientConfig };\n    if (this.#clientConfig && this.#clientConfig.allowReconfigure === !1)\n      throw new Error(\n        \"Existing client instance cannot be reconfigured - use `withConfig(newConfig)` to return a new client\"\n      );\n    return this.observable && this.observable.config(newConfig), this.#clientConfig = initConfig(newConfig, this.#clientConfig || {}), this;\n  }\n  /**\n   * Clone the client with a new (partial) configuration.\n   *\n   * @param newConfig - New client configuration properties, shallowly merged with existing configuration\n   */\n  withConfig(newConfig) {\n    const thisConfig = this.config();\n    return new SanityClient(this.#httpRequest, {\n      ...thisConfig,\n      ...newConfig,\n      stega: {\n        ...thisConfig.stega || {},\n        ...typeof newConfig?.stega == \"boolean\" ? { enabled: newConfig.stega } : newConfig?.stega || {}\n      }\n    });\n  }\n  fetch(query, params, options) {\n    return lastValueFrom(\n      _fetch(\n        this,\n        this.#httpRequest,\n        this.#clientConfig.stega,\n        query,\n        params,\n        options\n      )\n    );\n  }\n  /**\n   * Fetch a single document with the given ID.\n   *\n   * @param id - Document ID to fetch\n   * @param options - Request options\n   */\n  getDocument(id, options) {\n    return lastValueFrom(_getDocument(this, this.#httpRequest, id, options));\n  }\n  /**\n   * Fetch multiple documents in one request.\n   * Should be used sparingly - performing a query is usually a better option.\n   * The order/position of documents is preserved based on the original array of IDs.\n   * If any of the documents are missing, they will be replaced by a `null` entry in the returned array\n   *\n   * @param ids - Document IDs to fetch\n   * @param options - Request options\n   */\n  getDocuments(ids, options) {\n    return lastValueFrom(_getDocuments(this, this.#httpRequest, ids, options));\n  }\n  create(document, options) {\n    return lastValueFrom(\n      _create(this, this.#httpRequest, document, \"create\", options)\n    );\n  }\n  createIfNotExists(document, options) {\n    return lastValueFrom(\n      _createIfNotExists(this, this.#httpRequest, document, options)\n    );\n  }\n  createOrReplace(document, options) {\n    return lastValueFrom(\n      _createOrReplace(this, this.#httpRequest, document, options)\n    );\n  }\n  createVersion({\n    document,\n    publishedId,\n    releaseId\n  }, options) {\n    const documentVersionId = deriveDocumentVersionId(\"createVersion\", {\n      document,\n      publishedId,\n      releaseId\n    }), documentVersion = { ...document, _id: documentVersionId }, versionPublishedId = publishedId || getPublishedId(document._id);\n    return firstValueFrom(\n      _createVersion(\n        this,\n        this.#httpRequest,\n        documentVersion,\n        versionPublishedId,\n        options\n      )\n    );\n  }\n  delete(selection, options) {\n    return lastValueFrom(_delete(this, this.#httpRequest, selection, options));\n  }\n  /**\n   * @public\n   *\n   * Deletes the draft or release version of a document.\n   *\n   * @remarks\n   * * Discarding a version with no `releaseId` will discard the draft version of the published document.\n   * * If the draft or release version does not exist, any error will throw.\n   *\n   * @param params - Version action parameters:\n   *   - `releaseId` - The ID of the release to discard the document from.\n   *   - `publishedId` - The published ID of the document to discard.\n   * @param purge - if `true` the document history is also discarded.\n   * @param options - Additional action options.\n   * @returns a promise that resolves to the `transactionId`.\n   *\n   * @example Discarding a release version of a document\n   * ```ts\n   * client.discardVersion({publishedId: 'myDocument', releaseId: 'myRelease'})\n   * // The document with the ID `versions.myRelease.myDocument` will be discarded.\n   * ```\n   *\n   * @example Discarding a draft version of a document\n   * ```ts\n   * client.discardVersion({publishedId: 'myDocument'})\n   * // The document with the ID `drafts.myDocument` will be discarded.\n   * ```\n   */\n  discardVersion({ releaseId, publishedId }, purge, options) {\n    const documentVersionId = getDocumentVersionId(publishedId, releaseId);\n    return lastValueFrom(\n      _discardVersion(this, this.#httpRequest, documentVersionId, purge, options)\n    );\n  }\n  replaceVersion({\n    document,\n    publishedId,\n    releaseId\n  }, options) {\n    const documentVersionId = deriveDocumentVersionId(\"replaceVersion\", {\n      document,\n      publishedId,\n      releaseId\n    }), documentVersion = { ...document, _id: documentVersionId };\n    return firstValueFrom(\n      _replaceVersion(this, this.#httpRequest, documentVersion, options)\n    );\n  }\n  /**\n   * @public\n   *\n   * Used to indicate when a document within a release should be unpublished when\n   * the release is run.\n   *\n   * @remarks\n   * * If the published document does not exist, an error will be thrown.\n   *\n   * @param params - Version action parameters:\n   *   - `releaseId` - The ID of the release to unpublish the document from.\n   *   - `publishedId` - The published ID of the document to unpublish.\n   * @param options - Additional action options.\n   * @returns a promise that resolves to the `transactionId`.\n   *\n   * @example Unpublishing a release version of a published document\n   * ```ts\n   * await client.unpublishVersion({publishedId: 'myDocument', releaseId: 'myRelease'})\n   * // The document with the ID `versions.myRelease.myDocument` will be unpublished. when `myRelease` is run.\n   * ```\n   */\n  unpublishVersion({ releaseId, publishedId }, options) {\n    const versionId = getVersionId(publishedId, releaseId);\n    return lastValueFrom(\n      _unpublishVersion(this, this.#httpRequest, versionId, publishedId, options)\n    );\n  }\n  mutate(operations, options) {\n    return lastValueFrom(_mutate(this, this.#httpRequest, operations, options));\n  }\n  /**\n   * Create a new buildable patch of operations to perform\n   *\n   * @param selection - Document ID, an array of document IDs, or an object with `query` and optional `params`, defining which document(s) to patch\n   * @param operations - Optional object of patch operations to initialize the patch instance with\n   * @returns Patch instance - call `.commit()` to perform the operations defined\n   */\n  patch(documentId, operations) {\n    return new Patch(documentId, operations, this);\n  }\n  /**\n   * Create a new transaction of mutations\n   *\n   * @param operations - Optional array of mutation operations to initialize the transaction instance with\n   */\n  transaction(operations) {\n    return new Transaction(operations, this);\n  }\n  /**\n   * Perform action operations against the configured dataset\n   * Returns a promise that resolves to the transaction result\n   *\n   * @param operations - Action operation(s) to execute\n   * @param options - Action options\n   */\n  action(operations, options) {\n    return lastValueFrom(_action(this, this.#httpRequest, operations, options));\n  }\n  /**\n   * Perform a request against the Sanity API\n   * NOTE: Only use this for Sanity API endpoints, not for your own APIs!\n   *\n   * @param options - Request options\n   * @returns Promise resolving to the response body\n   */\n  request(options) {\n    return lastValueFrom(_request(this, this.#httpRequest, options));\n  }\n  /**\n   * Perform an HTTP request a `/data` sub-endpoint\n   * NOTE: Considered internal, thus marked as deprecated. Use `request` instead.\n   *\n   * @deprecated - Use `request()` or your own HTTP library instead\n   * @param endpoint - Endpoint to hit (mutate, query etc)\n   * @param body - Request body\n   * @param options - Request options\n   * @internal\n   */\n  dataRequest(endpoint, body, options) {\n    return lastValueFrom(_dataRequest(this, this.#httpRequest, endpoint, body, options));\n  }\n  /**\n   * Get a Sanity API URL for the URI provided\n   *\n   * @param uri - URI/path to build URL for\n   * @param canUseCdn - Whether or not to allow using the API CDN for this route\n   */\n  getUrl(uri, canUseCdn) {\n    return _getUrl(this, uri, canUseCdn);\n  }\n  /**\n   * Get a Sanity API URL for the data operation and path provided\n   *\n   * @param operation - Data operation (eg `query`, `mutate`, `listen` or similar)\n   * @param path - Path to append after the operation\n   */\n  getDataUrl(operation, path) {\n    return _getDataUrl(this, operation, path);\n  }\n}\nfunction defineCreateClientExports(envMiddleware2, ClassConstructor) {\n  return { requester: defineHttpRequest(envMiddleware2), createClient: (config) => {\n    const clientRequester = defineHttpRequest(envMiddleware2);\n    return new ClassConstructor(\n      (options, requester2) => (requester2 || clientRequester)({\n        maxRedirects: 0,\n        maxRetries: config.maxRetries,\n        retryDelay: config.retryDelay,\n        ...options\n      }),\n      config\n    );\n  } };\n}\nfunction defineDeprecatedCreateClient(createClient2) {\n  return function(config) {\n    return printNoDefaultExport(), createClient2(config);\n  };\n}\nvar envMiddleware = [];\nconst exp = defineCreateClientExports(envMiddleware, SanityClient), requester = exp.requester, createClient = exp.createClient, deprecatedCreateClient = defineDeprecatedCreateClient(createClient);\nexport {\n  BasePatch,\n  BaseTransaction,\n  ChannelError,\n  ClientError,\n  ConnectionFailedError,\n  CorsOriginError,\n  DisconnectError,\n  MessageError,\n  MessageParseError,\n  ObservablePatch,\n  ObservableSanityClient,\n  ObservableTransaction,\n  Patch,\n  SanityClient,\n  ServerError,\n  Transaction,\n  connectEventSource,\n  createClient,\n  deprecatedCreateClient as default,\n  requester,\n  adapter as unstable__adapter,\n  environment as unstable__environment,\n  validateApiPerspective\n};\n//# sourceMappingURL=index.browser.js.map\n", "export const languages = [\r\n  {id: 'en', title: 'English'},\r\n  {id: 'fr', title: 'French'},\r\n  {id: 'de', title: 'German'},\r\n  {id: 'es', title: 'Spanish'},\r\n  {id: 'ru', title: 'Russian'},\r\n  {id: 'zh', title: 'Chinese Simplified'},\r\n  {id: 'it', title: 'Italian'},\r\n  {id: 'ja', title: 'Japanese'},\r\n  {id: 'ar', title: 'Arabic'},\r\n  {id: 'pl', title: 'Polish'},\r\n  {id: 'nl', title: 'Dutch'},\r\n  {id: 'tr', title: 'Turkish'},\r\n  {id: 'ko', title: 'Korean'},\r\n  {id: 'pt', title: 'Portuguese'},\r\n  {id: 'uk', title: 'Ukrainian'},\r\n  {id: 'vi', title: 'Vietnamese'},\r\n  {id: 'sv', title: 'Swedish'},\r\n  {id: 'cs', title: 'Czech'},\r\n  {id: 'da', title: 'Danish'},\r\n  {id: 'hu', title: 'Hungarian'},\r\n  {id: 'ro', title: 'Romanian'},\r\n  {id: 'fi', title: 'Finnish'},\r\n  {id: 'el', title: 'Greek'},\r\n  {id: 'no', title: 'Norwegian'},\r\n  {id: 'he', title: 'Hebrew'},\r\n  {id: 'af', title: 'Afrikaans'},\r\n  {id: 'ca', title: 'Catalan'},\r\n  {id: 'sr', title: 'Serbain (Cyrillic)'},\r\n  {id: 'hr', title: 'Croatian'},\r\n  {id: 'et', title: 'Estonian'},\r\n]\r\n", "import {documentEvent<PERSON>andler} from '@sanity/functions'\nimport {createClient} from '@sanity/client'\nimport {languages} from '../../languages'\n\nconst token = process.env.SANITY_API_TOKEN\n\nconst client = createClient({\n  projectId: 'dtgxqbm7',\n  dataset: 'production',\n  useCdn: false,\n  apiVersion: 'vX',\n  token: token,\n})\n\n// In a Sanity Function triggered on publish\nexport const handler = documentEventHandler(async ({context, event}) => {\n  // Only translate posts\n  // if (event.data._type !== 'post') return\n\n   console.log(\"Context: \", context)\n  console.log(\"Event: \", event)\n\n  const filteredLanguages = languages.filter(lang => lang.id !== 'en')\n\n  for (const language of filteredLanguages) {\n    await client.agent.action.translate({\n      schemaId: '_.schemas.default',\n      documentId: event.data._id,\n      targetDocument: {operation: 'create'},\n      fromLanguage: {id: 'en', title: 'English'},\n      toLanguage: language,\n      styleGuide: 'Preserve tone and technical accuracy.',\n    })\n\n    console.log(`Translated post ${event.data._id} to ${language.title}`)\n  }\n})\n"], "names": ["handler", "r", "a", "n", "t", "s", "e", "o", "c", "i", "u", "l", "d", "p", "f", "O", "E", "S", "_", "b", "P", "v", "from", "q", "Subscription", "Subscriber", "ConsumerObserver", "SafeSubscriber", "x", "Observable", "observable", "Symbol_observable", "OperatorSubscriber", "err", "Subject", "AnonymousSubject", "ReplaySubject", "last", "Action", "AsyncAction", "Scheduler", "AsyncScheduler", "Symbol_iterator", "process", "iterator", "config", "isArray", "asyncScheduler", "_a", "getIt", "retry", "jsonRequest", "jsonResponse", "progress", "document", "client", "token", "finalize$1", "map$1"], "mappings": ";AAOO,SAAS,qBAAqBA,UAAS;AAC1C,MAAI,OAAOA,aAAY;AACnB,UAAM,IAAI,UAAU,8BAA8B;AACtD,SAAOA;AACX;ACXA,MAAM,IAAE,EAAE,OAAO,YAAU,QAAM,kBAAgB,UAAU,SAAQ,IAAE,EAAC,SAAQ,IAAE,MAAI,KAAI,GAAE,IAAE,SAASC,IAAE;AAAC,QAAMC,KAAE,EAAC,GAAG,GAAE,GAAG,YAAU,OAAOD,KAAE,EAAC,KAAIA,GAAC,IAAEA,GAAC;AAAE,MAAGC,GAAE,UAAQC,IAAED,GAAE,OAAO,GAAEA,GAAE,OAAM;AAAC,UAAK,EAAC,KAAIE,IAAE,cAAaH,GAAC,IAAE,SAASG,IAAE;AAAC,YAAMH,KAAEG,GAAE,QAAQ,GAAG;AAAE,UAAG,OAAKH,GAAE,QAAM,EAAC,KAAIG,IAAE,cAAa,IAAI,kBAAe;AAAE,YAAMD,KAAEC,GAAE,MAAM,GAAEH,EAAC,GAAEC,KAAEE,GAAE,MAAMH,KAAE,CAAC;AAAE,UAAG,CAAC,EAAE,QAAM,EAAC,KAAIE,IAAE,cAAa,IAAI,gBAAgBD,EAAC,EAAC;AAAE,UAAG,cAAY,OAAO,mBAAmB,OAAM,IAAI,MAAM,kFAAkF;AAAE,YAAMG,KAAE,IAAI;AAAgB,iBAAUC,MAAKJ,GAAE,MAAM,GAAG,GAAE;AAAC,cAAK,CAACE,IAAEH,EAAC,IAAEK,GAAE,MAAM,GAAG;AAAE,QAAAF,MAAGC,GAAE,OAAOE,IAAEH,EAAC,GAAEG,IAAEN,MAAG,EAAE,CAAC;AAAA,MAAC;AAAC,aAAM,EAAC,KAAIE,IAAE,cAAaE,GAAC;AAAA,IAAC,EAAEH,GAAE,GAAG;AAAE,eAAS,CAACI,IAAEC,EAAC,KAAI,OAAO,QAAQL,GAAE,KAAK,GAAE;AAAC,UAAG,WAASK,GAAE,KAAG,MAAM,QAAQA,EAAC,EAAE,YAAUH,MAAKG,GAAE,CAAAN,GAAE,OAAOK,IAAEF,EAAC;AAAA,UAAO,CAAAH,GAAE,OAAOK,IAAEC,EAAC;AAAE,YAAMJ,KAAEF,GAAE,SAAQ;AAAG,MAAAE,OAAID,GAAE,MAAI,GAAGE,EAAC,IAAID,EAAC;AAAA,IAAG;AAAA,EAAC;AAAC,SAAOD,GAAE,SAAOA,GAAE,QAAM,CAACA,GAAE,SAAO,UAAQA,GAAE,UAAQ,OAAO,YAAa,GAACA;AAAC;AAAE,SAASK,IAAED,IAAE;AAAC,SAAO,mBAAmBA,GAAE,QAAQ,OAAM,GAAG,CAAC;AAAC;AAAC,SAASH,IAAEG,IAAE;AAAC,MAAG,UAAKA,MAAG,MAAIA,GAAE,QAAM;AAAG,MAAGA,GAAE,WAASA,GAAE,OAAO,QAAOA;AAAE,QAAML,KAAE,OAAOK,EAAC;AAAE,SAAO,MAAML,EAAC,IAAEE,IAAE,EAAE,OAAO,IAAE,EAAC,SAAQF,IAAE,QAAOA,GAAC;AAAC;AAAC,MAAMC,MAAE,iBAAgBG,MAAE,SAASC,IAAE;AAAC,MAAG,CAACJ,IAAE,KAAKI,GAAE,GAAG,EAAE,OAAM,IAAI,MAAM,IAAIA,GAAE,GAAG,sBAAsB;AAAC;AAAE,SAASE,IAAEF,IAAE;AAAC,SAAOA,MAAGA,GAAE,cAAY,OAAO,UAAU,eAAe,KAAKA,IAAE,SAAS,IAAEA,GAAE,UAAQA;AAAC;ACAvvC,MAAM,IAAE,CAAC,WAAU,YAAW,YAAW,SAAQ,OAAO,GAAE,IAAE,CAAC,kBAAiB,mBAAkB,oBAAmB,mBAAkB,aAAY,cAAa,WAAU,YAAW,WAAW;AAAE,SAASD,IAAEJ,KAAEC,IAAE;AAAC,QAAMO,KAAE,CAAE,GAACC,KAAE,EAAE,OAAQ,CAACJ,IAAEF,QAAKE,GAAEF,EAAC,IAAEE,GAAEF,EAAC,KAAG,CAAE,GAACE,KAAI,EAAC,gBAAe,CAACF,CAAC,GAAE,iBAAgB,CAACE,GAAC,EAAC,CAAC;AAAE,WAASK,GAAEL,IAAE;AAAC,UAAMF,KAAE,EAAE,OAAQ,CAACE,IAAEF,QAAKE,GAAEF,EAAC,IAAE,2BAAU;AAAC,YAAME,KAAiB,uBAAO,OAAO,IAAI;AAAE,UAAIF,KAAE;AAAE,aAAM,EAAC,SAAQ,SAASA,IAAE;AAAC,mBAAUH,MAAKK,GAAE,CAAAA,GAAEL,EAAC,EAAEG,EAAC;AAAA,MAAC,GAAE,WAAU,SAASH,IAAE;AAAC,cAAMM,KAAEH;AAAI,eAAOE,GAAEC,EAAC,IAAEN,IAAE,WAAU;AAAC,iBAAOK,GAAEC,EAAC;AAAA,QAAC;AAAA,MAAC,EAAC;AAAA,IAAC,EAAG,GAACD,KAAI,CAAA,CAAE,GAAEL,KAAG,kBAAAK,OAAG,SAASF,IAAEH,OAAKM,IAAE;AAAC,YAAMJ,KAAE,cAAYC;AAAE,UAAIC,KAAEJ;AAAE,eAAQA,KAAE,GAAEA,KAAEK,GAAEF,EAAC,EAAE,WAASC,MAAE,GAAGC,GAAEF,EAAC,EAAEH,EAAC,GAAGI,IAAE,GAAGE,EAAC,GAAE,CAACJ,MAAGE,KAAGJ,KAAI;AAAC,aAAOI;AAAA,IAAC,GAAGK,EAAC,GAAEP,KAAEF,GAAE,kBAAiBK,EAAC;AAAE,IAAAL,GAAE,mBAAkBE,EAAC;AAAE,UAAME,KAAE,EAAC,SAAQF,IAAE,UAASC,IAAE,iBAAgBH,GAAC;AAAE,QAAIQ;AAAE,UAAME,KAAEP,GAAE,QAAQ,UAAW,CAAAE,OAAG;AAAC,MAAAG,KAAEP,GAAEI,IAAG,CAACC,IAAEJ,QAAK,CAACG,IAAEC,IAAEJ,OAAI;AAAC,YAAIE,KAAEC,IAAEJ,KAAEK;AAAE,YAAG,CAACF,GAAE,KAAG;AAAC,UAAAH,KAAED,GAAE,cAAaM,IAAEJ,EAAC;AAAA,QAAC,SAAOG,IAAE;AAAC,UAAAJ,KAAE,MAAKG,KAAEC;AAAA,QAAC;AAAC,QAAAD,KAAEA,MAAGJ,GAAE,WAAUI,IAAEF,EAAC,GAAEE,KAAED,GAAE,MAAM,QAAQC,EAAC,IAAEH,MAAGE,GAAE,SAAS,QAAQF,EAAC;AAAA,MAAC,GAAGK,IAAEJ,IAAEG,EAAC,CAAG;AAAA,IAAA,CAAG;AAAC,IAAAF,GAAE,MAAM,UAAW,MAAI;AAAC,MAAAO,GAAG,GAACF,MAAGA,GAAE,MAAK;AAAA,IAAE,CAAC;AAAG,UAAMD,KAAEP,GAAE,YAAWG,IAAEC,EAAC;AAAE,WAAOG,OAAIJ,MAAGA,GAAE,QAAQ,QAAQC,EAAC,GAAEG;AAAA,EAAC;AAAC,SAAOG,GAAE,MAAI,SAASL,IAAE;AAAC,QAAG,CAACA,GAAE,OAAM,IAAI,MAAM,uDAAuD;AAAE,QAAG,cAAY,OAAOA,GAAE,OAAM,IAAI,MAAM,6FAA6F;AAAE,QAAGA,GAAE,YAAUI,GAAE,SAAS,SAAO,EAAE,OAAM,IAAI,MAAM,qHAAqH;AAAE,WAAO,EAAE,QAAS,CAAAN,OAAG;AAAC,MAAAE,GAAEF,EAAC,KAAGM,GAAEN,EAAC,EAAE,KAAKE,GAAEF,EAAC,CAAC;AAAA,IAAC,CAAC,GAAGK,GAAE,KAAKH,EAAC,GAAEK;AAAA,EAAC,GAAEA,GAAE,QAAM,MAAIN,IAAEI,IAAEP,EAAC,GAAED,IAAE,QAAQU,GAAE,GAAG,GAAEA;AAAC;AAAC,IAAIT,KAAE,GAAEQ,MAAiBT,oBAAE,WAAU;AAAC,MAAG,EAAE,QAAOC;AAAE,MAAE;AAAE,MAAII,KAAE,SAASA,IAAE;AAAC,WAAOA,GAAE,QAAQ,cAAa,EAAE;AAAA,EAAC;AAAE,SAAOJ,MAAE,SAASE,IAAE;AAAC,QAAG,CAACA,GAAE,QAAM,CAAE;AAAC,aAAQH,KAAE,IAAGM,KAAED,GAAEF,EAAC,EAAE,MAAM,IAAI,GAAED,KAAE,GAAEA,KAAEI,GAAE,QAAOJ,MAAI;AAAC,UAAIE,KAAEE,GAAEJ,EAAC,GAAED,KAAEG,GAAE,QAAQ,GAAG,GAAEI,KAAEH,GAAED,GAAE,MAAM,GAAEH,EAAC,CAAC,EAAE,YAAW,GAAGQ,KAAEJ,GAAED,GAAE,MAAMH,KAAE,CAAC,CAAC;AAAE,aAAOD,GAAEQ,EAAC,IAAE,MAAIR,GAAEQ,EAAC,IAAEC,MAAGC,KAAEV,GAAEQ,EAAC,GAAE,qBAAmB,OAAO,UAAU,SAAS,KAAKE,EAAC,IAAEV,GAAEQ,EAAC,EAAE,KAAKC,EAAC,IAAET,GAAEQ,EAAC,IAAE,CAACR,GAAEQ,EAAC,GAAEC,EAAC;AAAA,IAAE;AAAC,QAAIC;AAAE,WAAOV;AAAA,EAAC;AAAC,EAAG,CAAA;AAAE,IAAA,MAAA,MAAM,EAAC;AAAA,EAAC;AAAA,EAAQ;AAAA,EAAQ;AAAA,EAAmB;AAAA,EAAU,aAAW;AAAA,EAAE;AAAA,EAAS,eAAa;AAAA,EAAG,eAAa;AAAA,EAAG;AAAA,EAAO;AAAA,EAAW;AAAA,EAAgB;AAAA,EAAG;AAAA,EAAG;AAAA,EAAG,KAAG,CAAA;AAAA,EAAG;AAAA,EAAG,KAAG,CAAA;AAAA,EAAG;AAAA,EAAG,KAAKK,IAAEF,IAAEH,IAAE;AAAC,SAAK,KAAGK,IAAE,KAAK,KAAGF,IAAE,KAAK,KAAG,IAAG,KAAK,aAAW,GAAE,KAAK,qBAAkB,GAAK,KAAK,KAAG;AAAA,EAAM;AAAA,EAAC,QAAO;AAAC,SAAK,MAAI,KAAK,GAAG,MAAO;AAAA,EAAA;AAAA,EAAC,wBAAuB;AAAC,WAAO,KAAK;AAAA,EAAE;AAAA,EAAC,iBAAiBE,IAAEF,IAAE;AAAC,SAAK,GAAGE,EAAC,IAAEF;AAAA,EAAC;AAAA,EAAC,QAAQE,IAAEF,KAAE,MAAG;AAAC,SAAK,KAAGE,IAAE,KAAK,KAAGF;AAAA,EAAC;AAAA,EAAC,KAAKE,IAAE;AAAC,UAAMF,KAAE,kBAAgB,KAAK,cAAaH,KAAE,EAAC,GAAG,KAAK,IAAG,QAAO,KAAK,IAAG,SAAQ,KAAK,IAAG,MAAKK,GAAC;AAAE,kBAAY,OAAO,mBAAiB,KAAK,OAAK,KAAK,KAAG,IAAI,mBAAgB,OAAO,cAAY,OAAK,KAAK,GAAG,kBAAkB,gBAAcL,GAAE,SAAO,KAAK,GAAG,UAAS,OAAO,WAAS,QAAMA,GAAE,cAAY,KAAK,kBAAgB,YAAU,SAAQ,MAAM,KAAK,IAAGA,EAAC,EAAE,KAAM,CAAAK,QAAIA,GAAE,QAAQ,QAAS,CAACA,IAAEF,OAAI;AAAC,WAAK,MAAI,GAAGA,EAAC,KAAKE,EAAC;AAAA;AAAA,IAAM,CAAC,GAAG,KAAK,SAAOA,GAAE,QAAO,KAAK,aAAWA,GAAE,YAAW,KAAK,aAAW,GAAE,KAAK,qBAAkB,GAAKF,KAAEE,GAAE,KAAM,IAACA,GAAE,YAAa,EAAG,EAAC,KAAM,CAAAA,OAAG;AAAC,kBAAU,OAAOA,KAAE,KAAK,eAAaA,KAAE,KAAK,WAASA,IAAE,KAAK,aAAW,GAAE,KAAK,qBAAsB;AAAA,IAAA,CAAG,EAAC,MAAO,CAAAA,OAAG;AAAC,uBAAeA,GAAE,OAAK,KAAK,UAAUA,EAAC,IAAE,KAAK;IAAW,CAAC;AAAA,EAAE;AAAC;AAAM,MAACE,MAAE,cAAY,OAAO,iBAAe,QAAM,SAAQ,IAAE,UAAQA,MAAE,iBAAeG,KAAEC,MAAE,CAACN,IAAEF,OAAI;AAAC,QAAMH,KAAEK,GAAE,SAAQC,KAAED,GAAE,gBAAgB,mBAAkBL,EAAC,GAAEE,KAAE,CAAE,GAACE,KAAEC,GAAE,gBAAgB,oBAAmB,QAAO,EAAC,SAAQE,KAAE,SAAQF,GAAC,CAAC;AAAE,MAAGD,IAAE;AAAC,UAAMC,KAAE,WAAWF,IAAE,GAAE,MAAKC,EAAC;AAAE,WAAM,EAAC,OAAM,MAAI,aAAaC,EAAC,EAAC;AAAA,EAAC;AAAC,MAAIJ,KAAE,IAAI;AAAE,EAAAA,cAAaS,OAAG,YAAU,OAAOJ,GAAE,SAAOL,GAAE,QAAQK,GAAE,OAAMA,GAAE,kBAAgB,IAAE;AAAE,QAAME,KAAEF,GAAE,SAAQK,KAAEL,GAAE;AAAQ,MAAIM,KAAE,OAAGC,KAAE,OAAG,IAAE;AAAG,MAAGZ,GAAE,UAAQ,CAAAI,OAAG;AAAC,MAAEJ,cAAaS,MAAEL,cAAa,QAAMA,KAAE,IAAI,MAAM,8CAA8CC,GAAE,GAAG,IAAG,EAAC,OAAMD,GAAC,CAAC,IAAE,IAAI,MAAM,8CAA8CC,GAAE,GAAG,GAAGD,GAAE,mBAAiB,IAAIA,GAAE,MAAM,OAAOA,GAAE,KAAK,wBAAsB,EAAE,EAAE,CAAC;AAAA,EAAC,GAAEJ,GAAE,YAAU,CAAAI,OAAG;AAAC,MAAE,IAAI,MAAM,6CAA6CC,GAAE,GAAG,GAAGD,GAAE,mBAAiB,IAAIA,GAAE,MAAM,OAAOA,GAAE,KAAK,wBAAsB,EAAE,EAAE,CAAC;AAAA,EAAC,GAAEJ,GAAE,UAAQ,MAAI;AAAC,MAAE,IAAE,GAAEW,KAAE;AAAA,EAAE,GAAEX,GAAE,qBAAmB,WAAU;AAAC,IAAAU,OAAI,EAAC,GAAGT,GAAE,SAAO,WAAY,MAAI,EAAE,iBAAiB,GAAGS,GAAE,MAAM,IAAG,CAACC,MAAGX,MAAG,MAAIA,GAAE,cAAY,MAAIA,GAAE,UAAQ,WAAU;AAAC,UAAG,EAAEW,MAAGC,MAAG,IAAG;AAAC,YAAG,MAAIZ,GAAE,OAAO,QAAO,KAAK,EAAE,IAAI,MAAM,mBAAmB,CAAC;AAAE,aAAIY,KAAE,MAAGV,GAAE,MAAK,EAAC,MAAKF,GAAE,aAAW,OAAKA,GAAE,gBAAc,WAASA,GAAE,eAAaA,GAAE,eAAa,KAAI,KAAIK,GAAE,KAAI,QAAOA,GAAE,QAAO,SAAQG,IAAER,GAAE,sBAAuB,CAAA,GAAE,YAAWA,GAAE,QAAO,eAAcA,GAAE,WAAU,CAAC;AAAA,MAAC;AAAA,IAAC,EAAC;AAAA,EAAE,GAAEA,GAAE,KAAKK,GAAE,QAAOA,GAAE,KAAI,IAAE,GAAEL,GAAE,kBAAgB,CAAC,CAACK,GAAE,iBAAgBE,MAAGP,GAAE,iBAAiB,YAAUI,MAAKG,GAAE,CAAAA,GAAE,eAAeH,EAAC,KAAGJ,GAAE,iBAAiBI,IAAEG,GAAEH,EAAC,CAAC;AAAE,SAAOC,GAAE,YAAUL,GAAE,eAAa,gBAAeI,GAAE,gBAAgB,aAAY,EAAC,SAAQC,IAAE,SAAQC,KAAE,SAAQN,IAAE,SAAQI,GAAC,CAAC,GAAEJ,GAAE,KAAKK,GAAE,QAAM,IAAI,GAAEK,OAAIT,GAAE,UAAQ,WAAY,MAAI,EAAE,WAAW,GAAGS,GAAE,OAAO,IAAG,EAAC,OAAM,WAAU;AAAC,IAAAC,KAAE,MAAGX,MAAGA,GAAE;EAAO,EAAC;AAAE,WAAS,EAAEE,IAAE;AAAC,QAAE,MAAGF,GAAE,MAAK;AAAG,UAAMD,KAAE,IAAI,MAAM,sBAAoBG,KAAE,kCAAkCG,GAAE,GAAG,KAAG,sCAAsCA,GAAE,GAAG,EAAE;AAAE,IAAAN,GAAE,OAAKG,IAAEE,GAAE,SAAS,MAAM,QAAQL,EAAC;AAAA,EAAC;AAAC,WAAS,EAAEK,IAAE;AAAC,KAACA,MAAGO,MAAGX,MAAGA,GAAE,cAAY,KAAGC,GAAE,YAAU,aAAaA,GAAE,OAAO,GAAEA,GAAE,UAAQ,aAAaA,GAAE,MAAM;AAAA,EAAC;AAAC,WAAS,EAAEG,IAAE;AAAC,QAAGQ,GAAE;AAAO,MAAE,IAAE,GAAEA,KAAE,MAAGZ,KAAE;AAAK,UAAMD,KAAEK,MAAG,IAAI,MAAM,2CAA2CC,GAAE,GAAG,EAAE;AAAE,IAAAN,GAAE,iBAAe,MAAGA,GAAE,UAAQM,IAAEH,GAAEH,EAAC;AAAA,EAAC;AAAC,GAAEY,MAAE,CAACP,KAAE,CAAA,GAAGF,KAAEQ,QAAIP,IAAEC,IAAEF,EAAC;ACA19J,IAAC,GAAEI,KAAEE,KAAEC,IAAE,GAAE,IAAE,EAAC,SAAQ,GAAE;AAAmBL,qBAAG,MAAI,IAAE,GAAE,SAASA,IAAEF,IAAE;AAAC,EAAAA,GAAE,aAAW,SAASA,IAAE;AAAC,QAAGA,GAAE,CAAC,KAAG,KAAK,YAAU,OAAK,MAAI,KAAK,aAAW,KAAK,YAAU,QAAM,OAAKA,GAAE,CAAC,KAAG,KAAK,YAAU,QAAM,OAAK,MAAIE,GAAE,QAAQ,SAAS,KAAK,IAAI,GAAE,CAAC,KAAK,UAAU;AAAO,UAAMD,KAAE,YAAU,KAAK;AAAM,IAAAD,GAAE,OAAO,GAAE,GAAEC,IAAE,gBAAgB;AAAE,QAAIF,KAAE,GAAEF,KAAE;AAAE,IAAAG,GAAE,CAAC,EAAE,QAAQ,eAAe,CAAAE,OAAG;AAAC,eAAOA,OAAIH,MAAI,SAAOG,OAAIL,KAAEE;AAAA,IAAG,IAAIC,GAAE,OAAOH,IAAE,GAAEI,EAAC;AAAA,EAAC,GAAED,GAAE,OAAK,SAASE,IAAE;AAAC,QAAG;AAAC,MAAAA,KAAEF,GAAE,QAAQ,QAAQ,SAAQE,EAAC,IAAEF,GAAE,QAAQ,WAAW,OAAO;AAAA,IAAC,QAAM;AAAA,IAAA;AAAA,EAAE,GAAEA,GAAE,OAAK,WAAU;AAAC,QAAIE;AAAE,QAAG;AAAC,MAAAA,KAAEF,GAAE,QAAQ,QAAQ,OAAO;AAAA,IAAC,QAAM;AAAA,IAAE;AAAA,WAAM,CAACE,MAAG,OAAO,UAAQ,OAAK,SAAQ,YAAUA,KAAE,QAAQ,IAAI,QAAOA;AAAA,EAAC,GAAEF,GAAE,YAAU,WAAU;AAAC,QAAG,OAAO,SAAO,OAAK,OAAO,YAAU,eAAa,OAAO,QAAQ,QAAM,OAAO,QAAQ,QAAQ,QAAM;AAAG,QAAG,OAAO,YAAU,OAAK,UAAU,aAAW,UAAU,UAAU,YAAa,EAAC,MAAM,uBAAuB,EAAE;AAAS,QAAIE;AAAE,WAAO,OAAO,WAAS,OAAK,SAAS,mBAAiB,SAAS,gBAAgB,SAAO,SAAS,gBAAgB,MAAM,oBAAkB,OAAO,SAAO,OAAK,OAAO,YAAU,OAAO,QAAQ,WAAS,OAAO,QAAQ,aAAW,OAAO,QAAQ,UAAQ,OAAO,YAAU,OAAK,UAAU,cAAYA,KAAE,UAAU,UAAU,YAAW,EAAG,MAAM,gBAAgB,MAAI,SAASA,GAAE,CAAC,GAAE,EAAE,KAAG,MAAI,OAAO,YAAU,OAAK,UAAU,aAAW,UAAU,UAAU,cAAc,MAAM,oBAAoB;AAAA,EAAC,GAAEF,GAAE,UAAQ,WAAU;AAAC,QAAG;AAAC,aAAO;AAAA,IAAY,QAAM;AAAA,IAAA;AAAA,EAAE,EAAG,GAACA,GAAE,UAAwB,uBAAI;AAAC,QAAIE,KAAE;AAAG,WAAM,MAAI;AAAC,MAAAA,OAAIA,KAAE,MAAG,QAAQ,KAAK,uIAAuI;AAAA,IAAE;AAAA,EAAC,MAAKF,GAAE,SAAO,CAAC,WAAU,WAAU,WAAU,WAAU,WAAU,WAAU,WAAU,WAAU,WAAU,WAAU,WAAU,WAAU,WAAU,WAAU,WAAU,WAAU,WAAU,WAAU,WAAU,WAAU,WAAU,WAAU,WAAU,WAAU,WAAU,WAAU,WAAU,WAAU,WAAU,WAAU,WAAU,WAAU,WAAU,WAAU,WAAU,WAAU,WAAU,WAAU,WAAU,WAAU,WAAU,WAAU,WAAU,WAAU,WAAU,WAAU,WAAU,WAAU,WAAU,WAAU,WAAU,WAAU,WAAU,WAAU,WAAU,WAAU,WAAU,WAAU,WAAU,WAAU,WAAU,WAAU,WAAU,WAAU,WAAU,WAAU,WAAU,WAAU,WAAU,WAAU,WAAU,WAAU,WAAU,WAAU,WAAU,SAAS,GAAEA,GAAE,MAAI,QAAQ,SAAO,QAAQ,QAAM,MAAI;AAAA,EAAA,IAAIE,GAAE,WAASK,KAAED,OAAGC,KAAE,GAAED,MAAE,SAASJ,IAAE;AAAC,aAASF,GAAEE,IAAE;AAAC,UAAIH,IAAEF,IAAEM,IAAEE,KAAE;AAAK,eAASP,MAAKI,IAAE;AAAC,YAAG,CAACJ,GAAE,QAAQ;AAAO,cAAMG,KAAEH,IAAED,KAAE,OAAsB,oBAAI,MAAI,GAAEM,KAAEN,MAAGE,MAAGF;AAAG,QAAAI,GAAE,OAAKE,IAAEF,GAAE,OAAKF,IAAEE,GAAE,OAAKJ,IAAEE,KAAEF,IAAEK,GAAE,CAAC,IAAEF,GAAE,OAAOE,GAAE,CAAC,CAAC,GAAE,YAAU,OAAOA,GAAE,CAAC,KAAGA,GAAE,QAAQ,IAAI;AAAE,YAAIG,KAAE;AAAE,QAAAH,GAAE,CAAC,IAAEA,GAAE,CAAC,EAAE,QAAQ,iBAAiB,CAACH,IAAEF,OAAI;AAAC,cAAG,SAAOE,GAAE,QAAM;AAAI,UAAAM;AAAI,gBAAMF,KAAEH,GAAE,WAAWH,EAAC;AAAE,cAAG,cAAY,OAAOM,IAAE;AAAC,kBAAMH,KAAEE,GAAEG,EAAC;AAAE,YAAAN,KAAEI,GAAE,KAAKF,IAAED,EAAC,GAAEE,GAAE,OAAOG,IAAE,CAAC,GAAEA;AAAA,UAAG;AAAC,iBAAON;AAAA,QAAC,CAAG,GAACC,GAAE,WAAW,KAAKC,IAAEC,EAAC,IAAGD,GAAE,OAAKD,GAAE,KAAK,MAAMC,IAAEC,EAAC;AAAA,MAAC;AAAC,aAAOJ,GAAE,YAAUI,IAAEJ,GAAE,YAAUE,GAAE,UAAS,GAAGF,GAAE,QAAME,GAAE,YAAYE,EAAC,GAAEJ,GAAE,SAAOG,IAAEH,GAAE,UAAQE,GAAE,SAAQ,OAAO,eAAeF,IAAE,WAAU,EAAC,YAAW,MAAG,cAAa,OAAG,KAAI,MAAI,SAAOO,KAAEA,MAAGR,OAAIG,GAAE,eAAaH,KAAEG,GAAE,YAAWG,KAAEH,GAAE,QAAQE,EAAC,IAAGC,KAAG,KAAI,CAAAD,OAAG;AAAC,QAAAG,KAAEH;AAAA,MAAC,EAAC,CAAC,GAAE,cAAY,OAAOF,GAAE,QAAMA,GAAE,KAAKF,EAAC,GAAEA;AAAA,IAAC;AAAC,aAASG,GAAEC,IAAED,IAAE;AAAC,YAAMF,KAAEC,GAAE,KAAK,aAAW,OAAOC,KAAE,MAAI,MAAIA,MAAGC,EAAC;AAAE,aAAOH,GAAE,MAAI,KAAK,KAAIA;AAAA,IAAC;AAAC,aAASA,GAAEG,IAAEF,IAAE;AAAC,UAAIC,KAAE,GAAEF,KAAE,GAAEF,KAAE,IAAGM,KAAE;AAAE,aAAKF,KAAEC,GAAE,SAAQ,KAAGH,KAAEC,GAAE,WAASA,GAAED,EAAC,MAAIG,GAAED,EAAC,KAAG,QAAMD,GAAED,EAAC,GAAG,SAAMC,GAAED,EAAC,KAAGF,KAAEE,IAAEI,KAAEF,IAAEF,SAAME,MAAIF;AAAA,WAAS;AAAC,YAAG,OAAKF,GAAE,QAAM;AAAG,QAAAE,KAAEF,KAAE,GAAEM,MAAIF,KAAEE;AAAA,MAAC;AAAC,aAAKJ,KAAEC,GAAE,UAAQ,QAAMA,GAAED,EAAC,IAAG,CAAAA;AAAI,aAAOA,OAAIC,GAAE;AAAA,IAAM;AAAC,WAAOA,GAAE,QAAMA,IAAEA,GAAE,UAAQA,IAAEA,GAAE,SAAO,SAASE,IAAE;AAAC,aAAOA,cAAa,QAAMA,GAAE,SAAOA,GAAE,UAAQA;AAAA,IAAC,GAAEF,GAAE,UAAQ,WAAU;AAAC,YAAME,KAAE,CAAC,GAAGF,GAAE,OAAM,GAAGA,GAAE,MAAM,IAAK,CAAAE,OAAG,MAAIA,EAAG,CAAA,EAAE,KAAK,GAAG;AAAE,aAAOF,GAAE,OAAO,EAAE,GAAEE;AAAA,IAAC,GAAEF,GAAE,SAAO,SAASE,IAAE;AAAC,MAAAF,GAAE,KAAKE,EAAC,GAAEF,GAAE,aAAWE,IAAEF,GAAE,QAAM,IAAGA,GAAE,QAAM;AAAG,YAAMC,MAAG,YAAU,OAAOC,KAAEA,KAAE,IAAI,KAAM,EAAC,QAAQ,KAAI,GAAG,EAAE,MAAM,GAAG,EAAE,OAAO,OAAO;AAAE,iBAAUA,MAAKD,GAAE,SAAMC,GAAE,CAAC,IAAEF,GAAE,MAAM,KAAKE,GAAE,MAAM,CAAC,CAAC,IAAEF,GAAE,MAAM,KAAKE,EAAC;AAAA,IAAC,GAAEF,GAAE,UAAQ,SAASE,IAAE;AAAC,iBAAUD,MAAKD,GAAE,MAAM,KAAGD,GAAEG,IAAED,EAAC,EAAE,QAAQ;AAAC,iBAAUA,MAAKD,GAAE,MAAM,KAAGD,GAAEG,IAAED,EAAC,EAAE,QAAM;AAAG,aAAM;AAAA,IAAE,GAAED,GAAE,WAAS,WAAU;AAAC,UAAGI,IAAE,QAAO;AAAEA,YAAE;AAAE,UAAIF,KAAE,KAAIF,KAAE,KAAGE,IAAED,KAAE,KAAGD,IAAED,KAAE,KAAGE,IAAEJ,KAAE,IAAEE;AAAE,eAASI,GAAED,IAAEF,IAAEC,IAAEF,IAAE;AAAC,YAAIF,KAAEG,MAAG,MAAIC;AAAE,eAAO,KAAK,MAAMC,KAAED,EAAC,IAAE,MAAIF,MAAGF,KAAE,MAAI;AAAA,MAAG;AAAC,aAAO,IAAE,SAASQ,IAAEP,IAAE;AAAC,QAAAA,KAAEA,MAAG;AAAG,YAAIM,IAAEE,IAAEC,KAAE,OAAOF;AAAE,YAAG,aAAWE,MAAGF,GAAE,SAAO,EAAE,QAAO,SAASF,IAAE;AAAC,cAAG,GAAGA,KAAE,OAAOA,EAAC,GAAG,SAAO,MAAK;AAAC,gBAAIE,KAAE,mIAAmI,KAAKF,EAAC;AAAE,gBAAGE,IAAE;AAAC,kBAAIP,KAAE,WAAWO,GAAE,CAAC,CAAC;AAAE,uBAAQA,GAAE,CAAC,KAAG,MAAM,YAAW;gBAAI,KAAI;AAAA,gBAAQ,KAAI;AAAA,gBAAO,KAAI;AAAA,gBAAM,KAAI;AAAA,gBAAK,KAAI;AAAI,yBAAO,WAASP;AAAA,gBAAE,KAAI;AAAA,gBAAQ,KAAI;AAAA,gBAAO,KAAI;AAAI,yBAAOA,KAAED;AAAA,gBAAE,KAAI;AAAA,gBAAO,KAAI;AAAA,gBAAM,KAAI;AAAI,yBAAOC,KAAEC;AAAA,gBAAE,KAAI;AAAA,gBAAQ,KAAI;AAAA,gBAAO,KAAI;AAAA,gBAAM,KAAI;AAAA,gBAAK,KAAI;AAAI,yBAAOD,KAAEG;AAAA,gBAAE,KAAI;AAAA,gBAAU,KAAI;AAAA,gBAAS,KAAI;AAAA,gBAAO,KAAI;AAAA,gBAAM,KAAI;AAAI,yBAAOH,KAAEE;AAAA,gBAAE,KAAI;AAAA,gBAAU,KAAI;AAAA,gBAAS,KAAI;AAAA,gBAAO,KAAI;AAAA,gBAAM,KAAI;AAAI,yBAAOF,KAAEI;AAAA,gBAAE,KAAI;AAAA,gBAAe,KAAI;AAAA,gBAAc,KAAI;AAAA,gBAAQ,KAAI;AAAA,gBAAO,KAAI;AAAK,yBAAOJ;AAAA,gBAAE;AAAQ;AAAA,cAAM;AAAA,YAAC;AAAA,UAAC;AAAA,QAAC,EAAEO,EAAC;AAAE,YAAG,aAAWE,MAAG,SAASF,EAAC,EAAE,QAAOP,GAAE,QAAMM,KAAEC,KAAGC,KAAE,KAAK,IAAIF,EAAC,MAAIL,KAAEI,GAAEC,IAAEE,IAAEP,IAAE,KAAK,IAAEO,MAAGL,KAAEE,GAAEC,IAAEE,IAAEL,IAAE,MAAM,IAAEK,MAAGN,KAAEG,GAAEC,IAAEE,IAAEN,IAAE,QAAQ,IAAEM,MAAGJ,KAAEC,GAAEC,IAAEE,IAAEJ,IAAE,QAAQ,IAAEE,KAAE,SAAO,SAASP,IAAE;AAAC,cAAIM,KAAE,KAAK,IAAIN,EAAC;AAAE,iBAAOM,MAAGJ,KAAE,KAAK,MAAMF,KAAEE,EAAC,IAAE,MAAII,MAAGF,KAAE,KAAK,MAAMJ,KAAEI,EAAC,IAAE,MAAIE,MAAGH,KAAE,KAAK,MAAMH,KAAEG,EAAC,IAAE,MAAIG,MAAGD,KAAE,KAAK,MAAML,KAAEK,EAAC,IAAE,MAAIL,KAAE;AAAA,QAAI,EAAEQ,EAAC;AAAE,cAAM,IAAI,MAAM,0DAAwD,KAAK,UAAUA,EAAC,CAAC;AAAA,MAAC;AAAA,IAAC,KAAIL,GAAE,UAAQ,WAAU;AAAC,cAAQ,KAAK,uIAAuI;AAAA,IAAC,GAAE,OAAO,KAAKE,EAAC,EAAE,QAAS,CAAAD,OAAG;AAAC,MAAAD,GAAEC,EAAC,IAAEC,GAAED,EAAC;AAAA,IAAC,CAAC,GAAGD,GAAE,QAAM,CAAA,GAAGA,GAAE,QAAM,CAAA,GAAGA,GAAE,aAAW,CAAA,GAAGA,GAAE,cAAY,SAASE,IAAE;AAAC,UAAID,KAAE;AAAE,eAAQD,KAAE,GAAEA,KAAEE,GAAE,QAAOF,KAAI,CAAAC,MAAGA,MAAG,KAAGA,KAAEC,GAAE,WAAWF,EAAC,GAAEC,MAAG;AAAE,aAAOD,GAAE,OAAO,KAAK,IAAIC,EAAC,IAAED,GAAE,OAAO,MAAM;AAAA,IAAC,GAAEA,GAAE,OAAOA,GAAE,KAAM,CAAA,GAAEA;AAAA,EAAC,IAAIA,EAAC;AAAE,QAAK,EAAC,YAAWC,GAAC,IAAEC,GAAE;AAAQ,EAAAD,GAAE,IAAE,SAASC,IAAE;AAAC,QAAG;AAAC,aAAO,KAAK,UAAUA,EAAC;AAAA,IAAC,SAAOA,IAAE;AAAC,aAAM,iCAA+BA,GAAE;AAAA,IAAO;AAAA,EAAC;AAAC,EAAE,GAAE,EAAE,OAAO,IAAG,EAAE,QAAO;AAA6iE,MAAM,IAAE,OAAO,SAAO,MAAI,MAAI,QAAG,CAAAA,OAAG,OAAO,SAASA,EAAC;AAAE,SAASS,IAAET,IAAE;AAAC,SAAM,sBAAoB,OAAO,UAAU,SAAS,KAAKA,EAAC;AAAC;AAAC,SAAS,EAAEA,IAAE;AAAC,MAAG,UAAKS,IAAET,EAAC,EAAE,QAAQ;AAAC,QAAMF,KAAEE,GAAE;AAAY,MAAG,WAASF,GAAE,QAAQ;AAAC,QAAMC,KAAED,GAAE;AAAU,SAAM,EAAE,UAAKW,IAAEV,EAAC,KAAG,UAAKA,GAAE,eAAe,eAAe;AAAE;AAAC,MAAM,IAAE,CAAC,WAAU,UAAS,QAAQ;AAAE,SAAS,IAAG;AAAC,SAAM,EAAC,gBAAe,CAAAC,OAAG;AAAC,UAAMF,KAAEE,GAAE;AAAK,WAAM,CAACF,MAAG,cAAY,OAAOA,GAAE,QAAM,EAAEA,EAAC,KAAG,OAAK,EAAE,QAAQ,OAAOA,EAAC,KAAG,CAAC,MAAM,QAAQA,EAAC,KAAG,CAAC,EAAEA,EAAC,IAAEE,KAAE,OAAO,OAAO,IAAGA,IAAE,EAAC,MAAK,KAAK,UAAUA,GAAE,IAAI,GAAE,SAAQ,OAAO,OAAO,CAAE,GAACA,GAAE,SAAQ,EAAC,gBAAe,mBAAkB,CAAC,EAAC,CAAC;AAAA,EAAC,EAAC;AAAC;AAAC,SAASU,IAAEV,IAAE;AAAC,SAAM,EAAC,YAAW,CAAAD,OAAG;AAAC,UAAMF,KAAEE,GAAE,QAAQ,cAAc,KAAG,IAAGJ,KAAEK,MAAGA,GAAE,SAAO,OAAKH,GAAE,QAAQ,kBAAkB;AAAE,WAAOE,GAAE,QAAMF,MAAGF,KAAE,OAAO,OAAO,CAAE,GAACI,IAAE,EAAC,MAAKD,GAAEC,GAAE,IAAI,EAAC,CAAC,IAAEA;AAAA,EAAC,GAAE,gBAAe,CAAAC,OAAG,OAAO,OAAO,CAAE,GAACA,IAAE,EAAC,SAAQ,OAAO,OAAO,EAAC,QAAO,mBAAkB,GAAEA,GAAE,OAAO,EAAC,CAAC,EAAC;AAAE,WAASF,GAAEE,IAAE;AAAC,QAAG;AAAC,aAAO,KAAK,MAAMA,EAAC;AAAA,IAAC,SAAOA,IAAE;AAAC,YAAMA,GAAE,UAAQ,2CAA2CA,GAAE,OAAO,IAAGA;AAAA,IAAC;AAAA,EAAC;AAAC;AAA+Y,IAAI,IAAE,CAAE;AAAC,OAAO,aAAW,MAAI,IAAE,aAAW,OAAO,SAAO,MAAI,IAAE,SAAO,OAAO,SAAO,MAAI,IAAE,SAAO,OAAO,OAAK,QAAM,IAAE;AAAM,IAAI,IAAE;AAAE,SAAS,EAAEA,KAAE,CAAA,GAAG;AAAC,QAAMF,KAAEE,GAAE,kBAAgB,EAAE;AAAW,MAAG,CAACF,GAAE,OAAM,IAAI,MAAM,iFAAiF;AAAE,SAAM,EAAC,UAAS,CAACE,IAAED,OAAI,IAAID,GAAG,CAAAA,QAAIE,GAAE,MAAM,UAAW,CAAAA,OAAGF,GAAE,MAAME,EAAC,CAAG,GAACA,GAAE,SAAS,UAAW,CAAAA,OAAGF,GAAE,KAAK,OAAO,OAAO,EAAC,MAAK,WAAU,GAAEE,EAAC,CAAC,CAAG,GAACA,GAAE,SAAS,UAAW,CAAAA,OAAG;AAAC,IAAAF,GAAE,KAAK,OAAO,OAAO,EAAC,MAAK,WAAU,GAAEE,EAAC,CAAC,GAAEF,GAAE;EAAU,CAAC,GAAGE,GAAE,QAAQ,QAAQD,EAAC,GAAE,MAAIC,GAAE,MAAM,QAAS,IAAG;AAAC;AAAC,SAASW,MAAG;AAAC,SAAM,EAAC,WAAU,CAAAX,OAAG;AAAC,QAAG,UAAQA,GAAE,QAAQ;AAAO,UAAMF,KAAEE,GAAE,SAAQD,KAAEC,GAAE;AAAQ,aAASH,GAAEG,IAAE;AAAC,aAAO,CAAAF,OAAG;AAAC,cAAMD,KAAEC,GAAE,mBAAiBA,GAAE,SAAOA,GAAE,QAAM,MAAI;AAAG,QAAAC,GAAE,SAAS,SAAS,QAAQ,EAAC,OAAMC,IAAE,SAAQH,IAAE,OAAMC,GAAE,OAAM,QAAOA,GAAE,QAAO,kBAAiBA,GAAE,iBAAgB,CAAC;AAAA,MAAC;AAAA,IAAC;AAAC,gBAAWA,MAAG,gBAAeA,GAAE,WAASA,GAAE,OAAO,aAAWD,GAAE,QAAQ,IAAG,gBAAeC,OAAIA,GAAE,aAAWD,GAAE,UAAU;AAAA,EAAE,EAAC;AAAC;AAA2iC,IAAI,IAAE,CAACG,IAAEF,IAAEC,QAAK,UAAQA,GAAE,UAAQ,WAASA,GAAE,YAAUC,GAAE,kBAAgB;AAAI,SAASY,IAAEZ,IAAE;AAAC,SAAO,MAAI,KAAK,IAAI,GAAEA,EAAC,IAAE,MAAI,KAAK,OAAQ;AAAA;AAAC,MAAM,IAAE,CAACA,KAAE,QAAM,CAAAA,OAAG;AAAC,QAAMF,KAAEE,GAAE,cAAY,GAAED,KAAEC,GAAE,cAAYY,KAAEf,KAAEG,GAAE;AAAY,SAAM,EAAC,SAAQ,CAACA,IAAEL,OAAI;AAAC,UAAMM,KAAEN,GAAE,SAAQQ,KAAEF,GAAE,cAAYH,IAAEF,KAAEK,GAAE,cAAYF,IAAEG,KAAED,GAAE,eAAaJ,IAAEO,KAAEH,GAAE,iBAAe;AAAE,QAAG,UAAQI,KAAEJ,GAAE,SAAO,YAAU,OAAOI,MAAG,cAAY,OAAOA,GAAE,QAAM,CAACH,GAAEF,IAAEI,IAAEH,EAAC,KAAGG,MAAGD,GAAE,QAAOH;AAAE,QAAIK;AAAE,UAAME,KAAE,OAAO,OAAO,CAAE,GAACZ,IAAE,EAAC,SAAQ,OAAO,OAAO,IAAGM,IAAE,EAAC,eAAcG,KAAE,EAAC,CAAC,EAAC,CAAC;AAAE,WAAO,WAAY,MAAIT,GAAE,SAAS,QAAQ,QAAQY,EAAC,GAAGX,GAAEQ,EAAC,CAAC,GAAE;AAAA,EAAI,EAAC;AAAC,GAAG,EAAC,aAAY,GAAE,GAAGJ,GAAC,CAAC;AAAujB,EAAE,cAAY;ACgB/vZ,IAAI,gBAAgB,SAASM,IAAG,GAAG;AACjC,kBAAgB,OAAO,kBAClB,EAAE,WAAW,CAAA,eAAgB,SAAS,SAAUA,IAAGO,IAAG;AAAE,IAAAP,GAAE,YAAYO;AAAA,EAAE,KACzE,SAAUP,IAAGO,IAAG;AAAE,aAASN,MAAKM,GAAG,KAAI,OAAO,UAAU,eAAe,KAAKA,IAAGN,EAAC,EAAG,CAAAD,GAAEC,EAAC,IAAIM,GAAEN,EAAC;AAAA,EAAI;AACrG,SAAO,cAAcD,IAAG,CAAC;AAC3B;AAEO,SAAS,UAAUA,IAAG,GAAG;AAC9B,MAAI,OAAO,MAAM,cAAc,MAAM;AACjC,UAAM,IAAI,UAAU,yBAAyB,OAAO,CAAC,IAAI,+BAA+B;AAC5F,gBAAcA,IAAG,CAAC;AAClB,WAAS,KAAK;AAAE,SAAK,cAAcA;AAAA,EAAE;AACrC,EAAAA,GAAE,YAAY,MAAM,OAAO,OAAO,OAAO,CAAC,KAAK,GAAG,YAAY,EAAE,WAAW,IAAI,GAAE;AACnF;AAqFO,SAAS,UAAU,SAAS,YAAYQ,IAAG,WAAW;AAC3D,WAAS,MAAM,OAAO;AAAE,WAAO,iBAAiBA,KAAI,QAAQ,IAAIA,GAAE,SAAU,SAAS;AAAE,cAAQ,KAAK;AAAA,IAAI,CAAA;AAAA,EAAE;AAC1G,SAAO,KAAKA,OAAMA,KAAI,UAAU,SAAU,SAAS,QAAQ;AACvD,aAAS,UAAU,OAAO;AAAE,UAAI;AAAE,aAAK,UAAU,KAAK,KAAK,CAAC;AAAA,MAAI,SAAQd,IAAG;AAAE,eAAOA,EAAC;AAAA,MAAI;AAAA,IAAA;AACzF,aAAS,SAAS,OAAO;AAAE,UAAI;AAAE,aAAK,UAAU,OAAO,EAAE,KAAK,CAAC;AAAA,MAAI,SAAQA,IAAG;AAAE,eAAOA,EAAC;AAAA,MAAI;AAAA,IAAA;AAC5F,aAAS,KAAK,QAAQ;AAAE,aAAO,OAAO,QAAQ,OAAO,KAAK,IAAI,MAAM,OAAO,KAAK,EAAE,KAAK,WAAW,QAAQ;AAAA,IAAE;AAC5G,UAAM,YAAY,UAAU,MAAM,SAAS,cAAc,CAAA,CAAE,GAAG,MAAM;AAAA,EAC1E,CAAG;AACH;AAEO,SAAS,YAAY,SAAS,MAAM;AACzC,MAAIY,KAAI,EAAE,OAAO,GAAG,MAAM,WAAW;AAAE,QAAId,GAAE,CAAC,IAAI,EAAG,OAAMA,GAAE,CAAC;AAAG,WAAOA,GAAE,CAAC;AAAA,EAAI,GAAE,MAAM,CAAE,GAAE,KAAK,CAAA,EAAI,GAAEU,IAAG,GAAGV,IAAG,IAAI,OAAO,QAAQ,OAAO,aAAa,aAAa,WAAW,QAAQ,SAAS;AAC/L,SAAO,EAAE,OAAO,KAAK,CAAC,GAAG,EAAE,OAAO,IAAI,KAAK,CAAC,GAAG,EAAE,QAAQ,IAAI,KAAK,CAAC,GAAG,OAAO,WAAW,eAAe,EAAE,OAAO,QAAQ,IAAI,WAAW;AAAE,WAAO;AAAA,EAAK,IAAK;AAC1J,WAAS,KAAKD,IAAG;AAAE,WAAO,SAAUkB,IAAG;AAAE,aAAO,KAAK,CAAClB,IAAGkB,EAAC,CAAC;AAAA,IAAE;AAAA,EAAG;AAChE,WAAS,KAAK,IAAI;AACd,QAAIP,GAAG,OAAM,IAAI,UAAU,iCAAiC;AAC5D,WAAO,MAAM,IAAI,GAAG,GAAG,CAAC,MAAMI,KAAI,KAAKA,GAAG,KAAI;AAC1C,UAAIJ,KAAI,GAAG,MAAMV,KAAI,GAAG,CAAC,IAAI,IAAI,EAAE,QAAQ,IAAI,GAAG,CAAC,IAAI,EAAE,OAAO,OAAOA,KAAI,EAAE,QAAQ,MAAMA,GAAE,KAAK,CAAC,GAAG,KAAK,EAAE,SAAS,EAAEA,KAAIA,GAAE,KAAK,GAAG,GAAG,CAAC,CAAC,GAAG,KAAM,QAAOA;AAC3J,UAAI,IAAI,GAAGA,GAAG,MAAK,CAAC,GAAG,CAAC,IAAI,GAAGA,GAAE,KAAK;AACtC,cAAQ,GAAG,CAAC,GAAC;AAAA,QACT,KAAK;AAAA,QAAG,KAAK;AAAG,UAAAA,KAAI;AAAI;AAAA,QACxB,KAAK;AAAG,UAAAc,GAAE;AAAS,iBAAO,EAAE,OAAO,GAAG,CAAC,GAAG,MAAM,MAAO;AAAA,QACvD,KAAK;AAAG,UAAAA,GAAE;AAAS,cAAI,GAAG,CAAC;AAAG,eAAK,CAAC,CAAC;AAAG;AAAA,QACxC,KAAK;AAAG,eAAKA,GAAE,IAAI;AAAO,UAAAA,GAAE,KAAK,IAAG;AAAI;AAAA,QACxC;AACI,cAAI,EAAEd,KAAIc,GAAE,MAAMd,KAAIA,GAAE,SAAS,KAAKA,GAAEA,GAAE,SAAS,CAAC,OAAO,GAAG,CAAC,MAAM,KAAK,GAAG,CAAC,MAAM,IAAI;AAAE,YAAAc,KAAI;AAAG;AAAA,UAAS;AAC1G,cAAI,GAAG,CAAC,MAAM,MAAM,CAACd,MAAM,GAAG,CAAC,IAAIA,GAAE,CAAC,KAAK,GAAG,CAAC,IAAIA,GAAE,CAAC,IAAK;AAAE,YAAAc,GAAE,QAAQ,GAAG,CAAC;AAAG;AAAA,UAAM;AACpF,cAAI,GAAG,CAAC,MAAM,KAAKA,GAAE,QAAQd,GAAE,CAAC,GAAG;AAAE,YAAAc,GAAE,QAAQd,GAAE,CAAC;AAAG,YAAAA,KAAI;AAAI;AAAA,UAAM;AACnE,cAAIA,MAAKc,GAAE,QAAQd,GAAE,CAAC,GAAG;AAAE,YAAAc,GAAE,QAAQd,GAAE,CAAC;AAAG,YAAAc,GAAE,IAAI,KAAK,EAAE;AAAG;AAAA,UAAM;AACjE,cAAId,GAAE,CAAC,EAAG,CAAAc,GAAE,IAAI,IAAK;AACrB,UAAAA,GAAE,KAAK,IAAG;AAAI;AAAA,MAChC;AACU,WAAK,KAAK,KAAK,SAASA,EAAC;AAAA,IAC5B,SAAQZ,IAAG;AAAE,WAAK,CAAC,GAAGA,EAAC;AAAG,UAAI;AAAA,IAAI,UAAA;AAAW,MAAAQ,KAAIV,KAAI;AAAA,IAAE;AACxD,QAAI,GAAG,CAAC,IAAI,EAAG,OAAM,GAAG,CAAC;AAAG,WAAO,EAAE,OAAO,GAAG,CAAC,IAAI,GAAG,CAAC,IAAI,QAAQ,MAAM,KAAM;AAAA,EACtF;AACA;AAkBO,SAAS,SAASG,IAAG;AAC1B,MAAIF,KAAI,OAAO,WAAW,cAAc,OAAO,UAAU,IAAIA,MAAKE,GAAEF,EAAC,GAAGI,KAAI;AAC5E,MAAI,EAAG,QAAO,EAAE,KAAKF,EAAC;AACtB,MAAIA,MAAK,OAAOA,GAAE,WAAW,SAAU,QAAO;AAAA,IAC1C,MAAM,WAAY;AACd,UAAIA,MAAKE,MAAKF,GAAE,OAAQ,CAAAA,KAAI;AAC5B,aAAO,EAAE,OAAOA,MAAKA,GAAEE,IAAG,GAAG,MAAM,CAACF,GAAG;AAAA,IACjD;AAAA,EACG;AACD,QAAM,IAAI,UAAUF,KAAI,4BAA4B,iCAAiC;AACvF;AAEO,SAAS,OAAOE,IAAGJ,IAAG;AAC3B,MAAI,IAAI,OAAO,WAAW,cAAcI,GAAE,OAAO,QAAQ;AACzD,MAAI,CAAC,EAAG,QAAOA;AACf,MAAIE,KAAI,EAAE,KAAKF,EAAC,GAAGN,IAAG,KAAK,CAAA,GAAIK;AAC/B,MAAI;AACA,YAAQH,OAAM,UAAUA,OAAM,MAAM,EAAEF,KAAIQ,GAAE,KAAM,GAAE,KAAM,IAAG,KAAKR,GAAE,KAAK;AAAA,EAC/E,SACS,OAAO;AAAE,IAAAK,KAAI,EAAE,MAAY;AAAA,EAAG,UAC7B;AACJ,QAAI;AACA,UAAIL,MAAK,CAACA,GAAE,SAAS,IAAIQ,GAAE,QAAQ,GAAI,GAAE,KAAKA,EAAC;AAAA,IACzD,UACc;AAAE,UAAIH,GAAG,OAAMA,GAAE;AAAA,IAAM;AAAA,EACrC;AACE,SAAO;AACT;AAkBO,SAAS,cAAc,IAAIgB,OAAM,MAAM;AAC5C,MAAI,QAAQ,UAAU,WAAW,EAAG,UAASb,KAAI,GAAGE,KAAIW,MAAK,QAAQ,IAAIb,KAAIE,IAAGF,MAAK;AACjF,QAAI,MAAM,EAAEA,MAAKa,QAAO;AACpB,UAAI,CAAC,GAAI,MAAK,MAAM,UAAU,MAAM,KAAKA,OAAM,GAAGb,EAAC;AACnD,SAAGA,EAAC,IAAIa,MAAKb,EAAC;AAAA,IACxB;AAAA,EACA;AACE,SAAO,GAAG,OAAO,MAAM,MAAM,UAAU,MAAM,KAAKa,KAAI,CAAC;AACzD;AAEO,SAAS,QAAQD,IAAG;AACzB,SAAO,gBAAgB,WAAW,KAAK,IAAIA,IAAG,QAAQ,IAAI,QAAQA,EAAC;AACrE;AAEO,SAAS,iBAAiB,SAAS,YAAY,WAAW;AAC/D,MAAI,CAAC,OAAO,cAAe,OAAM,IAAI,UAAU,sCAAsC;AACrF,MAAI,IAAI,UAAU,MAAM,SAAS,cAAc,EAAE,GAAGZ,IAAGc,KAAI,CAAE;AAC7D,SAAOd,KAAI,OAAO,QAAQ,OAAO,kBAAkB,aAAa,gBAAgB,QAAQ,SAAS,GAAG,KAAK,MAAM,GAAG,KAAK,OAAO,GAAG,KAAK,UAAU,WAAW,GAAGA,GAAE,OAAO,aAAa,IAAI,WAAY;AAAE,WAAO;AAAA,EAAO,GAAEA;AACtN,WAAS,YAAYK,IAAG;AAAE,WAAO,SAAUO,IAAG;AAAE,aAAO,QAAQ,QAAQA,EAAC,EAAE,KAAKP,IAAG,MAAM;AAAA,IAAE;AAAA,EAAG;AAC7F,WAAS,KAAKX,IAAGW,IAAG;AAAE,QAAI,EAAEX,EAAC,GAAG;AAAE,MAAAM,GAAEN,EAAC,IAAI,SAAUkB,IAAG;AAAE,eAAO,IAAI,QAAQ,SAAUnB,IAAG,GAAG;AAAE,UAAAqB,GAAE,KAAK,CAACpB,IAAGkB,IAAGnB,IAAG,CAAC,CAAC,IAAI,KAAK,OAAOC,IAAGkB,EAAC;AAAA,SAAI;AAAA,MAAI;AAAE,UAAIP,GAAG,CAAAL,GAAEN,EAAC,IAAIW,GAAEL,GAAEN,EAAC,CAAC;AAAA,IAAI;AAAA,EAAA;AACtK,WAAS,OAAOA,IAAGkB,IAAG;AAAE,QAAI;AAAE,WAAK,EAAElB,EAAC,EAAEkB,EAAC,CAAC;AAAA,IAAE,SAAUf,IAAG;AAAE,aAAOiB,GAAE,CAAC,EAAE,CAAC,GAAGjB,EAAC;AAAA,IAAI;AAAA,EAAA;AAChF,WAAS,KAAKL,IAAG;AAAE,IAAAA,GAAE,iBAAiB,UAAU,QAAQ,QAAQA,GAAE,MAAM,CAAC,EAAE,KAAK,SAAS,MAAM,IAAI,OAAOsB,GAAE,CAAC,EAAE,CAAC,GAAGtB,EAAC;AAAA,EAAE;AACtH,WAAS,QAAQ,OAAO;AAAE,WAAO,QAAQ,KAAK;AAAA,EAAE;AAChD,WAAS,OAAO,OAAO;AAAE,WAAO,SAAS,KAAK;AAAA,EAAE;AAChD,WAAS,OAAOa,IAAGO,IAAG;AAAE,QAAIP,GAAEO,EAAC,GAAGE,GAAE,SAASA,GAAE,OAAQ,QAAOA,GAAE,CAAC,EAAE,CAAC,GAAGA,GAAE,CAAC,EAAE,CAAC,CAAC;AAAA,EAAE;AAClF;AAQO,SAAS,cAAchB,IAAG;AAC/B,MAAI,CAAC,OAAO,cAAe,OAAM,IAAI,UAAU,sCAAsC;AACrF,MAAI,IAAIA,GAAE,OAAO,aAAa,GAAGE;AACjC,SAAO,IAAI,EAAE,KAAKF,EAAC,KAAKA,KAAI,OAAO,aAAa,aAAa,SAASA,EAAC,IAAIA,GAAE,OAAO,QAAQ,EAAC,GAAIE,KAAI,CAAE,GAAE,KAAK,MAAM,GAAG,KAAK,OAAO,GAAG,KAAK,QAAQ,GAAGA,GAAE,OAAO,aAAa,IAAI,WAAY;AAAE,WAAO;AAAA,EAAO,GAAEA;AAC9M,WAAS,KAAKN,IAAG;AAAE,IAAAM,GAAEN,EAAC,IAAII,GAAEJ,EAAC,KAAK,SAAUkB,IAAG;AAAE,aAAO,IAAI,QAAQ,SAAU,SAAS,QAAQ;AAAE,QAAAA,KAAId,GAAEJ,EAAC,EAAEkB,EAAC,GAAG,OAAO,SAAS,QAAQA,GAAE,MAAMA,GAAE,KAAK;AAAA,MAAI,CAAA;AAAA,IAAE;AAAA,EAAG;AAC9J,WAAS,OAAO,SAAS,QAAQT,IAAGS,IAAG;AAAE,YAAQ,QAAQA,EAAC,EAAE,KAAK,SAASA,IAAG;AAAE,cAAQ,EAAE,OAAOA,IAAG,MAAMT,IAAG;AAAA,OAAM,MAAM;AAAA,EAAE;AAC5H;AA2EuB,OAAO,oBAAoB,aAAa,kBAAkB,SAAU,OAAO,YAAY,SAAS;AACrH,MAAIN,KAAI,IAAI,MAAM,OAAO;AACzB,SAAOA,GAAE,OAAO,mBAAmBA,GAAE,QAAQ,OAAOA,GAAE,aAAa,YAAYA;AACjF;AC1UO,SAAS,WAAW,OAAO;AAC9B,SAAO,OAAO,UAAU;AAC5B;ACFO,SAAS,iBAAiB,YAAY;AACzC,MAAI,SAAS,SAAU,UAAU;AAC7B,UAAM,KAAK,QAAQ;AACnB,aAAS,QAAQ,IAAI,MAAK,EAAG;AAAA,EAChC;AACD,MAAI,WAAW,WAAW,MAAM;AAChC,WAAS,YAAY,OAAO,OAAO,MAAM,SAAS;AAClD,WAAS,UAAU,cAAc;AACjC,SAAO;AACX;ACRO,IAAI,sBAAsB,iBAAiB,SAAU,QAAQ;AAChE,SAAO,SAAS,wBAAwB,QAAQ;AAC5C,WAAO,IAAI;AACX,SAAK,UAAU,SACT,OAAO,SAAS,8CAA8C,OAAO,IAAI,SAAU,KAAKG,IAAG;AAAE,aAAOA,KAAI,IAAI,OAAO,IAAI,SAAQ;AAAA,IAAK,CAAA,EAAE,KAAK,MAAM,IACjJ;AACN,SAAK,OAAO;AACZ,SAAK,SAAS;AAAA,EACjB;AACL,CAAC;ACVM,SAAS,UAAU,KAAK,MAAM;AACjC,MAAI,KAAK;AACL,QAAI,QAAQ,IAAI,QAAQ,IAAI;AAC5B,SAAK,SAAS,IAAI,OAAO,OAAO,CAAC;AAAA,EACzC;AACA;ACDA,IAAI,eAAgB,WAAY;AAC5B,WAASe,cAAa,iBAAiB;AACnC,SAAK,kBAAkB;AACvB,SAAK,SAAS;AACd,SAAK,aAAa;AAClB,SAAK,cAAc;AAAA,EAC3B;AACI,EAAAA,cAAa,UAAU,cAAc,WAAY;AAC7C,QAAI,KAAK,IAAI,KAAK;AAClB,QAAI;AACJ,QAAI,CAAC,KAAK,QAAQ;AACd,WAAK,SAAS;AACd,UAAI,aAAa,KAAK;AACtB,UAAI,YAAY;AACZ,aAAK,aAAa;AAClB,YAAI,MAAM,QAAQ,UAAU,GAAG;AAC3B,cAAI;AACA,qBAAS,eAAe,SAAS,UAAU,GAAG,iBAAiB,aAAa,KAAI,GAAI,CAAC,eAAe,MAAM,iBAAiB,aAAa,KAAI,GAAI;AAC5I,kBAAI,WAAW,eAAe;AAC9B,uBAAS,OAAO,IAAI;AAAA,YAChD;AAAA,UACA,SAC2B,OAAO;AAAE,kBAAM,EAAE,OAAO,MAAK;AAAA,UAAG,UAC/B;AACJ,gBAAI;AACA,kBAAI,kBAAkB,CAAC,eAAe,SAAS,KAAK,aAAa,QAAS,IAAG,KAAK,YAAY;AAAA,YAC1H,UACgC;AAAE,kBAAI,IAAK,OAAM,IAAI;AAAA,YAAM;AAAA,UAC3D;AAAA,QACA,OACqB;AACD,qBAAW,OAAO,IAAI;AAAA,QAC1C;AAAA,MACA;AACY,UAAI,mBAAmB,KAAK;AAC5B,UAAI,WAAW,gBAAgB,GAAG;AAC9B,YAAI;AACA,2BAAkB;AAAA,QACtC,SACuBlB,IAAG;AACN,mBAASA,cAAa,sBAAsBA,GAAE,SAAS,CAACA,EAAC;AAAA,QAC7E;AAAA,MACA;AACY,UAAI,cAAc,KAAK;AACvB,UAAI,aAAa;AACb,aAAK,cAAc;AACnB,YAAI;AACA,mBAAS,gBAAgB,SAAS,WAAW,GAAG,kBAAkB,cAAc,KAAI,GAAI,CAAC,gBAAgB,MAAM,kBAAkB,cAAc,KAAI,GAAI;AACnJ,gBAAI,YAAY,gBAAgB;AAChC,gBAAI;AACA,4BAAc,SAAS;AAAA,YACnD,SAC+B,KAAK;AACR,uBAAS,WAAW,QAAQ,WAAW,SAAS,SAAS,CAAE;AAC3D,kBAAI,eAAe,qBAAqB;AACpC,yBAAS,cAAc,cAAc,CAAA,GAAI,OAAO,MAAM,CAAC,GAAG,OAAO,IAAI,MAAM,CAAC;AAAA,cAC5G,OACiC;AACD,uBAAO,KAAK,GAAG;AAAA,cAC/C;AAAA,YACA;AAAA,UACA;AAAA,QACA,SACuB,OAAO;AAAE,gBAAM,EAAE,OAAO,MAAK;AAAA,QAAG,UAC/B;AACJ,cAAI;AACA,gBAAI,mBAAmB,CAAC,gBAAgB,SAAS,KAAK,cAAc,QAAS,IAAG,KAAK,aAAa;AAAA,UAC1H,UAC4B;AAAE,gBAAI,IAAK,OAAM,IAAI;AAAA,UAAM;AAAA,QACvD;AAAA,MACA;AACY,UAAI,QAAQ;AACR,cAAM,IAAI,oBAAoB,MAAM;AAAA,MACpD;AAAA,IACA;AAAA,EACK;AACD,EAAAkB,cAAa,UAAU,MAAM,SAAU,UAAU;AAC7C,QAAI;AACJ,QAAI,YAAY,aAAa,MAAM;AAC/B,UAAI,KAAK,QAAQ;AACb,sBAAc,QAAQ;AAAA,MACtC,OACiB;AACD,YAAI,oBAAoBA,eAAc;AAClC,cAAI,SAAS,UAAU,SAAS,WAAW,IAAI,GAAG;AAC9C;AAAA,UACxB;AACoB,mBAAS,WAAW,IAAI;AAAA,QAC5C;AACgB,SAAC,KAAK,eAAe,KAAK,KAAK,iBAAiB,QAAQ,OAAO,SAAS,KAAK,CAAA,GAAI,KAAK,QAAQ;AAAA,MAC9G;AAAA,IACA;AAAA,EACK;AACD,EAAAA,cAAa,UAAU,aAAa,SAAU,QAAQ;AAClD,QAAI,aAAa,KAAK;AACtB,WAAO,eAAe,UAAW,MAAM,QAAQ,UAAU,KAAK,WAAW,SAAS,MAAM;AAAA,EAC3F;AACD,EAAAA,cAAa,UAAU,aAAa,SAAU,QAAQ;AAClD,QAAI,aAAa,KAAK;AACtB,SAAK,aAAa,MAAM,QAAQ,UAAU,KAAK,WAAW,KAAK,MAAM,GAAG,cAAc,aAAa,CAAC,YAAY,MAAM,IAAI;AAAA,EAC7H;AACD,EAAAA,cAAa,UAAU,gBAAgB,SAAU,QAAQ;AACrD,QAAI,aAAa,KAAK;AACtB,QAAI,eAAe,QAAQ;AACvB,WAAK,aAAa;AAAA,IAC9B,WACiB,MAAM,QAAQ,UAAU,GAAG;AAChC,gBAAU,YAAY,MAAM;AAAA,IACxC;AAAA,EACK;AACD,EAAAA,cAAa,UAAU,SAAS,SAAU,UAAU;AAChD,QAAI,cAAc,KAAK;AACvB,mBAAe,UAAU,aAAa,QAAQ;AAC9C,QAAI,oBAAoBA,eAAc;AAClC,eAAS,cAAc,IAAI;AAAA,IACvC;AAAA,EACK;AACD,EAAAA,cAAa,QAAS,WAAY;AAC9B,QAAI,QAAQ,IAAIA,cAAc;AAC9B,UAAM,SAAS;AACf,WAAO;AAAA,EACf,EAAQ;AACJ,SAAOA;AACX;AAEO,IAAI,qBAAqB,aAAa;AACtC,SAAS,eAAe,OAAO;AAClC,SAAQ,iBAAiB,gBACpB,SAAS,YAAY,SAAS,WAAW,MAAM,MAAM,KAAK,WAAW,MAAM,GAAG,KAAK,WAAW,MAAM,WAAW;AACxH;AACA,SAAS,cAAc,WAAW;AAC9B,MAAI,WAAW,SAAS,GAAG;AACvB,cAAW;AAAA,EACnB,OACS;AACD,cAAU,YAAa;AAAA,EAC/B;AACA;AC7IO,IAAI,SAAS;AAAA,EAGhB,SAAS;AAGb;ACLO,IAAI,kBAAkB;AAAA,EACzB,YAAY,SAAUxB,UAAS,SAAS;AACpC,QAAI,OAAO,CAAE;AACb,aAAS,KAAK,GAAG,KAAK,UAAU,QAAQ,MAAM;AAC1C,WAAK,KAAK,CAAC,IAAI,UAAU,EAAE;AAAA,IACvC;AAKQ,WAAO,WAAW,MAAM,QAAQ,cAAc,CAACA,UAAS,OAAO,GAAG,OAAO,IAAI,CAAC,CAAC;AAAA,EAClF;AAAA,EACD,cAAc,SAAU,QAAQ;AAE5B,WAAuF,aAAc,MAAM;AAAA,EAC9G;AAAA,EACD,UAAU;AACd;AChBO,SAAS,qBAAqB,KAAK;AACtC,kBAAgB,WAAW,WAAY;AAK9B;AACD,YAAM;AAAA,IAClB;AAAA,EACA,CAAK;AACL;ACZO,SAAS,OAAO;AAAA;ACEhB,SAAS,aAAa,IAAI;AAexB;AACD,OAAI;AAAA,EACZ;AACA;ACXA,IAAI,aAAc,SAAU,QAAQ;AAChC,YAAUyB,aAAY,MAAM;AAC5B,WAASA,YAAW,aAAa;AAC7B,QAAI,QAAQ,OAAO,KAAK,IAAI,KAAK;AACjC,UAAM,YAAY;AAClB,QAAI,aAAa;AACb,YAAM,cAAc;AACpB,UAAI,eAAe,WAAW,GAAG;AAC7B,oBAAY,IAAI,KAAK;AAAA,MACrC;AAAA,IACA,OACa;AACD,YAAM,cAAc;AAAA,IAChC;AACQ,WAAO;AAAA,EACf;AACI,EAAAA,YAAW,SAAS,SAAU,MAAM,OAAO,UAAU;AACjD,WAAO,IAAI,eAAe,MAAM,OAAO,QAAQ;AAAA,EAClD;AACD,EAAAA,YAAW,UAAU,OAAO,SAAU,OAAO;AACzC,QAAI,KAAK,UAAW;AAAA,SAGf;AACD,WAAK,MAAM,KAAK;AAAA,IAC5B;AAAA,EACK;AACD,EAAAA,YAAW,UAAU,QAAQ,SAAU,KAAK;AACxC,QAAI,KAAK,UAAW;AAAA,SAGf;AACD,WAAK,YAAY;AACjB,WAAK,OAAO,GAAG;AAAA,IAC3B;AAAA,EACK;AACD,EAAAA,YAAW,UAAU,WAAW,WAAY;AACxC,QAAI,KAAK,UAAW;AAAA,SAGf;AACD,WAAK,YAAY;AACjB,WAAK,UAAW;AAAA,IAC5B;AAAA,EACK;AACD,EAAAA,YAAW,UAAU,cAAc,WAAY;AAC3C,QAAI,CAAC,KAAK,QAAQ;AACd,WAAK,YAAY;AACjB,aAAO,UAAU,YAAY,KAAK,IAAI;AACtC,WAAK,cAAc;AAAA,IAC/B;AAAA,EACK;AACD,EAAAA,YAAW,UAAU,QAAQ,SAAU,OAAO;AAC1C,SAAK,YAAY,KAAK,KAAK;AAAA,EAC9B;AACD,EAAAA,YAAW,UAAU,SAAS,SAAU,KAAK;AACzC,QAAI;AACA,WAAK,YAAY,MAAM,GAAG;AAAA,IACtC,UACgB;AACJ,WAAK,YAAa;AAAA,IAC9B;AAAA,EACK;AACD,EAAAA,YAAW,UAAU,YAAY,WAAY;AACzC,QAAI;AACA,WAAK,YAAY,SAAU;AAAA,IACvC,UACgB;AACJ,WAAK,YAAa;AAAA,IAC9B;AAAA,EACK;AACD,SAAOA;AACX,EAAE,YAAY;AAMd,IAAI,mBAAoB,WAAY;AAChC,WAASC,kBAAiB,iBAAiB;AACvC,SAAK,kBAAkB;AAAA,EAC/B;AACI,EAAAA,kBAAiB,UAAU,OAAO,SAAU,OAAO;AAC/C,QAAI,kBAAkB,KAAK;AAC3B,QAAI,gBAAgB,MAAM;AACtB,UAAI;AACA,wBAAgB,KAAK,KAAK;AAAA,MAC1C,SACmB,OAAO;AACV,6BAAqB,KAAK;AAAA,MAC1C;AAAA,IACA;AAAA,EACK;AACD,EAAAA,kBAAiB,UAAU,QAAQ,SAAU,KAAK;AAC9C,QAAI,kBAAkB,KAAK;AAC3B,QAAI,gBAAgB,OAAO;AACvB,UAAI;AACA,wBAAgB,MAAM,GAAG;AAAA,MACzC,SACmB,OAAO;AACV,6BAAqB,KAAK;AAAA,MAC1C;AAAA,IACA,OACa;AACD,2BAAqB,GAAG;AAAA,IACpC;AAAA,EACK;AACD,EAAAA,kBAAiB,UAAU,WAAW,WAAY;AAC9C,QAAI,kBAAkB,KAAK;AAC3B,QAAI,gBAAgB,UAAU;AAC1B,UAAI;AACA,wBAAgB,SAAU;AAAA,MAC1C,SACmB,OAAO;AACV,6BAAqB,KAAK;AAAA,MAC1C;AAAA,IACA;AAAA,EACK;AACD,SAAOA;AACX;AACA,IAAI,iBAAkB,SAAU,QAAQ;AACpC,YAAUC,iBAAgB,MAAM;AAChC,WAASA,gBAAe,gBAAgB,OAAO,UAAU;AACrD,QAAI,QAAQ,OAAO,KAAK,IAAI,KAAK;AACjC,QAAI;AACJ,QAAI,WAAW,cAAc,KAAK,CAAC,gBAAgB;AAC/C,wBAAkB;AAAA,QACd,MAAO,mBAAmB,QAAQ,mBAAmB,SAAS,iBAAiB;AAAA,QAC/E,OAAO,UAAU,QAAQ,UAAU,SAAS,QAAQ;AAAA,QACpD,UAAU,aAAa,QAAQ,aAAa,SAAS,WAAW;AAAA,MACnE;AAAA,IACb,OACa;AAWI;AACD,0BAAkB;AAAA,MAClC;AAAA,IACA;AACQ,UAAM,cAAc,IAAI,iBAAiB,eAAe;AACxD,WAAO;AAAA,EACf;AACI,SAAOA;AACX,EAAE,UAAU;AAEZ,SAAS,qBAAqB,OAAO;AAI5B;AACD,yBAAqB,KAAK;AAAA,EAClC;AACA;AACA,SAAS,oBAAoB,KAAK;AAC9B,QAAM;AACV;AAKO,IAAI,iBAAiB;AAAA,EACxB,QAAQ;AAAA,EACR,MAAM;AAAA,EACN,OAAO;AAAA,EACP,UAAU;AACd;ACtLO,IAAI,aAAc,WAAY;AAAE,SAAQ,OAAO,WAAW,cAAc,OAAO,cAAe;EAAoB;ACAlH,SAAS,SAASC,IAAG;AACxB,SAAOA;AACX;ACDO,SAAS,OAAO;AACnB,MAAI,MAAM,CAAE;AACZ,WAAS,KAAK,GAAG,KAAK,UAAU,QAAQ,MAAM;AAC1C,QAAI,EAAE,IAAI,UAAU,EAAE;AAAA,EAC9B;AACI,SAAO,cAAc,GAAG;AAC5B;AACO,SAAS,cAAc,KAAK;AAC/B,MAAI,IAAI,WAAW,GAAG;AAClB,WAAO;AAAA,EACf;AACI,MAAI,IAAI,WAAW,GAAG;AAClB,WAAO,IAAI,CAAC;AAAA,EACpB;AACI,SAAO,SAAS,MAAM,OAAO;AACzB,WAAO,IAAI,OAAO,SAAU,MAAM,IAAI;AAAE,aAAO,GAAG,IAAI;AAAA,IAAI,GAAE,KAAK;AAAA,EACpE;AACL;ACXA,IAAI,aAAc,WAAY;AAC1B,WAASC,YAAW,WAAW;AAC3B,QAAI,WAAW;AACX,WAAK,aAAa;AAAA,IAC9B;AAAA,EACA;AACI,EAAAA,YAAW,UAAU,OAAO,SAAU,UAAU;AAC5C,QAAIC,cAAa,IAAID,YAAY;AACjC,IAAAC,YAAW,SAAS;AACpB,IAAAA,YAAW,WAAW;AACtB,WAAOA;AAAA,EACV;AACD,EAAAD,YAAW,UAAU,YAAY,SAAU,gBAAgB,OAAO,UAAU;AACxE,QAAI,QAAQ;AACZ,QAAI,aAAa,aAAa,cAAc,IAAI,iBAAiB,IAAI,eAAe,gBAAgB,OAAO,QAAQ;AACnH,iBAAa,WAAY;AACrB,UAAI,KAAK,OAAO,WAAW,GAAG,UAAU,SAAS,GAAG;AACpD,iBAAW,IAAI,WAEP,SAAS,KAAK,YAAY,MAAM,IAClC,SAEM,MAAM,WAAW,UAAU,IAE3B,MAAM,cAAc,UAAU,CAAC;AAAA,IACvD,CAAS;AACD,WAAO;AAAA,EACV;AACD,EAAAA,YAAW,UAAU,gBAAgB,SAAU,MAAM;AACjD,QAAI;AACA,aAAO,KAAK,WAAW,IAAI;AAAA,IACvC,SACe,KAAK;AACR,WAAK,MAAM,GAAG;AAAA,IAC1B;AAAA,EACK;AACD,EAAAA,YAAW,UAAU,UAAU,SAAU,MAAM,aAAa;AACxD,QAAI,QAAQ;AACZ,kBAAc,eAAe,WAAW;AACxC,WAAO,IAAI,YAAY,SAAU,SAAS,QAAQ;AAC9C,UAAI,aAAa,IAAI,eAAe;AAAA,QAChC,MAAM,SAAU,OAAO;AACnB,cAAI;AACA,iBAAK,KAAK;AAAA,UAClC,SAC2B,KAAK;AACR,mBAAO,GAAG;AACV,uBAAW,YAAa;AAAA,UAChD;AAAA,QACiB;AAAA,QACD,OAAO;AAAA,QACP,UAAU;AAAA,MAC1B,CAAa;AACD,YAAM,UAAU,UAAU;AAAA,IACtC,CAAS;AAAA,EACJ;AACD,EAAAA,YAAW,UAAU,aAAa,SAAU,YAAY;AACpD,QAAI;AACJ,YAAQ,KAAK,KAAK,YAAY,QAAQ,OAAO,SAAS,SAAS,GAAG,UAAU,UAAU;AAAA,EACzF;AACD,EAAAA,YAAW,UAAUE,UAAiB,IAAI,WAAY;AAClD,WAAO;AAAA,EACV;AACD,EAAAF,YAAW,UAAU,OAAO,WAAY;AACpC,QAAI,aAAa,CAAE;AACnB,aAAS,KAAK,GAAG,KAAK,UAAU,QAAQ,MAAM;AAC1C,iBAAW,EAAE,IAAI,UAAU,EAAE;AAAA,IACzC;AACQ,WAAO,cAAc,UAAU,EAAE,IAAI;AAAA,EACxC;AACD,EAAAA,YAAW,UAAU,YAAY,SAAU,aAAa;AACpD,QAAI,QAAQ;AACZ,kBAAc,eAAe,WAAW;AACxC,WAAO,IAAI,YAAY,SAAU,SAAS,QAAQ;AAC9C,UAAI;AACJ,YAAM,UAAU,SAAUD,IAAG;AAAE,eAAQ,QAAQA;AAAA,MAAK,GAAE,SAAU,KAAK;AAAE,eAAO,OAAO,GAAG;AAAA,SAAM,WAAY;AAAE,eAAO,QAAQ,KAAK;AAAA,OAAI;AAAA,IAChJ,CAAS;AAAA,EACJ;AACD,EAAAC,YAAW,SAAS,SAAU,WAAW;AACrC,WAAO,IAAIA,YAAW,SAAS;AAAA,EAClC;AACD,SAAOA;AACX;AAEA,SAAS,eAAe,aAAa;AACjC,MAAI;AACJ,UAAQ,KAAK,gBAAgB,QAAQ,gBAAgB,SAAS,cAAc,OAAO,aAAa,QAAQ,OAAO,SAAS,KAAK;AACjI;AACA,SAAS,WAAW,OAAO;AACvB,SAAO,SAAS,WAAW,MAAM,IAAI,KAAK,WAAW,MAAM,KAAK,KAAK,WAAW,MAAM,QAAQ;AAClG;AACA,SAAS,aAAa,OAAO;AACzB,SAAQ,SAAS,iBAAiB,cAAgB,WAAW,KAAK,KAAK,eAAe,KAAK;AAC/F;ACnGO,SAAS,QAAQ,QAAQ;AAC5B,SAAO,WAAW,WAAW,QAAQ,WAAW,SAAS,SAAS,OAAO,IAAI;AACjF;AACO,SAAS,QAAQ,MAAM;AAC1B,SAAO,SAAU,QAAQ;AACrB,QAAI,QAAQ,MAAM,GAAG;AACjB,aAAO,OAAO,KAAK,SAAU,cAAc;AACvC,YAAI;AACA,iBAAO,KAAK,cAAc,IAAI;AAAA,QAClD,SACuB,KAAK;AACR,eAAK,MAAM,GAAG;AAAA,QAClC;AAAA,MACA,CAAa;AAAA,IACb;AACQ,UAAM,IAAI,UAAU,wCAAwC;AAAA,EAC/D;AACL;AChBO,SAAS,yBAAyB,aAAa,QAAQ,YAAY,SAAS,YAAY;AAC3F,SAAO,IAAI,mBAAmB,aAAa,QAAQ,YAAY,SAAS,UAAU;AACtF;AACA,IAAI,qBAAsB,SAAU,QAAQ;AACxC,YAAUG,qBAAoB,MAAM;AACpC,WAASA,oBAAmB,aAAa,QAAQ,YAAY,SAAS,YAAY,mBAAmB;AACjG,QAAI,QAAQ,OAAO,KAAK,MAAM,WAAW,KAAK;AAC9C,UAAM,aAAa;AACnB,UAAM,oBAAoB;AAC1B,UAAM,QAAQ,SACR,SAAU,OAAO;AACf,UAAI;AACA,eAAO,KAAK;AAAA,MAChC,SACuB,KAAK;AACR,oBAAY,MAAM,GAAG;AAAA,MACzC;AAAA,IACA,IACc,OAAO,UAAU;AACvB,UAAM,SAAS,UACT,SAAU,KAAK;AACb,UAAI;AACA,gBAAQ,GAAG;AAAA,MAC/B,SACuBC,MAAK;AACR,oBAAY,MAAMA,IAAG;AAAA,MACzC,UACwB;AACJ,aAAK,YAAa;AAAA,MACtC;AAAA,IACA,IACc,OAAO,UAAU;AACvB,UAAM,YAAY,aACZ,WAAY;AACV,UAAI;AACA,mBAAY;AAAA,MAChC,SACuB,KAAK;AACR,oBAAY,MAAM,GAAG;AAAA,MACzC,UACwB;AACJ,aAAK,YAAa;AAAA,MACtC;AAAA,IACA,IACc,OAAO,UAAU;AACvB,WAAO;AAAA,EACf;AACI,EAAAD,oBAAmB,UAAU,cAAc,WAAY;AACnD,QAAI;AACJ,QAAI,CAAC,KAAK,qBAAqB,KAAK,kBAAiB,GAAI;AACrD,UAAI,WAAW,KAAK;AACpB,aAAO,UAAU,YAAY,KAAK,IAAI;AACtC,OAAC,cAAc,KAAK,KAAK,gBAAgB,QAAQ,OAAO,SAAS,SAAS,GAAG,KAAK,IAAI;AAAA,IAClG;AAAA,EACK;AACD,SAAOA;AACX,EAAE,UAAU;ACzDL,IAAI,0BAA0B,iBAAiB,SAAU,QAAQ;AACpE,SAAO,SAAS,8BAA8B;AAC1C,WAAO,IAAI;AACX,SAAK,OAAO;AACZ,SAAK,UAAU;AAAA,EAClB;AACL,CAAC;ACDD,IAAI,UAAW,SAAU,QAAQ;AAC7B,YAAUE,UAAS,MAAM;AACzB,WAASA,WAAU;AACf,QAAI,QAAQ,OAAO,KAAK,IAAI,KAAK;AACjC,UAAM,SAAS;AACf,UAAM,mBAAmB;AACzB,UAAM,YAAY,CAAE;AACpB,UAAM,YAAY;AAClB,UAAM,WAAW;AACjB,UAAM,cAAc;AACpB,WAAO;AAAA,EACf;AACI,EAAAA,SAAQ,UAAU,OAAO,SAAU,UAAU;AACzC,QAAI,UAAU,IAAI,iBAAiB,MAAM,IAAI;AAC7C,YAAQ,WAAW;AACnB,WAAO;AAAA,EACV;AACD,EAAAA,SAAQ,UAAU,iBAAiB,WAAY;AAC3C,QAAI,KAAK,QAAQ;AACb,YAAM,IAAI,wBAAyB;AAAA,IAC/C;AAAA,EACK;AACD,EAAAA,SAAQ,UAAU,OAAO,SAAU,OAAO;AACtC,QAAI,QAAQ;AACZ,iBAAa,WAAY;AACrB,UAAI,KAAK;AACT,YAAM,eAAgB;AACtB,UAAI,CAAC,MAAM,WAAW;AAClB,YAAI,CAAC,MAAM,kBAAkB;AACzB,gBAAM,mBAAmB,MAAM,KAAK,MAAM,SAAS;AAAA,QACvE;AACgB,YAAI;AACA,mBAAS,KAAK,SAAS,MAAM,gBAAgB,GAAG,KAAK,GAAG,KAAM,GAAE,CAAC,GAAG,MAAM,KAAK,GAAG,QAAQ;AACtF,gBAAI,WAAW,GAAG;AAClB,qBAAS,KAAK,KAAK;AAAA,UAC3C;AAAA,QACA,SACuB,OAAO;AAAE,gBAAM,EAAE,OAAO,MAAK;AAAA,QAAG,UAC/B;AACJ,cAAI;AACA,gBAAI,MAAM,CAAC,GAAG,SAAS,KAAK,GAAG,QAAS,IAAG,KAAK,EAAE;AAAA,UAC1E,UAC4B;AAAE,gBAAI,IAAK,OAAM,IAAI;AAAA,UAAM;AAAA,QACvD;AAAA,MACA;AAAA,IACA,CAAS;AAAA,EACJ;AACD,EAAAA,SAAQ,UAAU,QAAQ,SAAU,KAAK;AACrC,QAAI,QAAQ;AACZ,iBAAa,WAAY;AACrB,YAAM,eAAgB;AACtB,UAAI,CAAC,MAAM,WAAW;AAClB,cAAM,WAAW,MAAM,YAAY;AACnC,cAAM,cAAc;AACpB,YAAI,YAAY,MAAM;AACtB,eAAO,UAAU,QAAQ;AACrB,oBAAU,MAAK,EAAG,MAAM,GAAG;AAAA,QAC/C;AAAA,MACA;AAAA,IACA,CAAS;AAAA,EACJ;AACD,EAAAA,SAAQ,UAAU,WAAW,WAAY;AACrC,QAAI,QAAQ;AACZ,iBAAa,WAAY;AACrB,YAAM,eAAgB;AACtB,UAAI,CAAC,MAAM,WAAW;AAClB,cAAM,YAAY;AAClB,YAAI,YAAY,MAAM;AACtB,eAAO,UAAU,QAAQ;AACrB,oBAAU,MAAO,EAAC,SAAU;AAAA,QAChD;AAAA,MACA;AAAA,IACA,CAAS;AAAA,EACJ;AACD,EAAAA,SAAQ,UAAU,cAAc,WAAY;AACxC,SAAK,YAAY,KAAK,SAAS;AAC/B,SAAK,YAAY,KAAK,mBAAmB;AAAA,EAC5C;AACD,SAAO,eAAeA,SAAQ,WAAW,YAAY;AAAA,IACjD,KAAK,WAAY;AACb,UAAI;AACJ,eAAS,KAAK,KAAK,eAAe,QAAQ,OAAO,SAAS,SAAS,GAAG,UAAU;AAAA,IACnF;AAAA,IACD,YAAY;AAAA,IACZ,cAAc;AAAA,EACtB,CAAK;AACD,EAAAA,SAAQ,UAAU,gBAAgB,SAAU,YAAY;AACpD,SAAK,eAAgB;AACrB,WAAO,OAAO,UAAU,cAAc,KAAK,MAAM,UAAU;AAAA,EAC9D;AACD,EAAAA,SAAQ,UAAU,aAAa,SAAU,YAAY;AACjD,SAAK,eAAgB;AACrB,SAAK,wBAAwB,UAAU;AACvC,WAAO,KAAK,gBAAgB,UAAU;AAAA,EACzC;AACD,EAAAA,SAAQ,UAAU,kBAAkB,SAAU,YAAY;AACtD,QAAI,QAAQ;AACZ,QAAI,KAAK,MAAM,WAAW,GAAG,UAAU,YAAY,GAAG,WAAW,YAAY,GAAG;AAChF,QAAI,YAAY,WAAW;AACvB,aAAO;AAAA,IACnB;AACQ,SAAK,mBAAmB;AACxB,cAAU,KAAK,UAAU;AACzB,WAAO,IAAI,aAAa,WAAY;AAChC,YAAM,mBAAmB;AACzB,gBAAU,WAAW,UAAU;AAAA,IAC3C,CAAS;AAAA,EACJ;AACD,EAAAA,SAAQ,UAAU,0BAA0B,SAAU,YAAY;AAC9D,QAAI,KAAK,MAAM,WAAW,GAAG,UAAU,cAAc,GAAG,aAAa,YAAY,GAAG;AACpF,QAAI,UAAU;AACV,iBAAW,MAAM,WAAW;AAAA,IACxC,WACiB,WAAW;AAChB,iBAAW,SAAU;AAAA,IACjC;AAAA,EACK;AACD,EAAAA,SAAQ,UAAU,eAAe,WAAY;AACzC,QAAIJ,cAAa,IAAI,WAAY;AACjC,IAAAA,YAAW,SAAS;AACpB,WAAOA;AAAA,EACV;AACD,EAAAI,SAAQ,SAAS,SAAU,aAAa,QAAQ;AAC5C,WAAO,IAAI,iBAAiB,aAAa,MAAM;AAAA,EAClD;AACD,SAAOA;AACX,EAAE,UAAU;AAEZ,IAAI,mBAAoB,SAAU,QAAQ;AACtC,YAAUC,mBAAkB,MAAM;AAClC,WAASA,kBAAiB,aAAa,QAAQ;AAC3C,QAAI,QAAQ,OAAO,KAAK,IAAI,KAAK;AACjC,UAAM,cAAc;AACpB,UAAM,SAAS;AACf,WAAO;AAAA,EACf;AACI,EAAAA,kBAAiB,UAAU,OAAO,SAAU,OAAO;AAC/C,QAAI,IAAI;AACR,KAAC,MAAM,KAAK,KAAK,iBAAiB,QAAQ,OAAO,SAAS,SAAS,GAAG,UAAU,QAAQ,OAAO,SAAS,SAAS,GAAG,KAAK,IAAI,KAAK;AAAA,EACrI;AACD,EAAAA,kBAAiB,UAAU,QAAQ,SAAU,KAAK;AAC9C,QAAI,IAAI;AACR,KAAC,MAAM,KAAK,KAAK,iBAAiB,QAAQ,OAAO,SAAS,SAAS,GAAG,WAAW,QAAQ,OAAO,SAAS,SAAS,GAAG,KAAK,IAAI,GAAG;AAAA,EACpI;AACD,EAAAA,kBAAiB,UAAU,WAAW,WAAY;AAC9C,QAAI,IAAI;AACR,KAAC,MAAM,KAAK,KAAK,iBAAiB,QAAQ,OAAO,SAAS,SAAS,GAAG,cAAc,QAAQ,OAAO,SAAS,SAAS,GAAG,KAAK,EAAE;AAAA,EAClI;AACD,EAAAA,kBAAiB,UAAU,aAAa,SAAU,YAAY;AAC1D,QAAI,IAAI;AACR,YAAQ,MAAM,KAAK,KAAK,YAAY,QAAQ,OAAO,SAAS,SAAS,GAAG,UAAU,UAAU,OAAO,QAAQ,OAAO,SAAS,KAAK;AAAA,EACnI;AACD,SAAOA;AACX,EAAE,OAAO;AC/JF,IAAI,wBAAwB;AAAA,EAC/B,KAAK,WAAY;AACb,YAAQ,sBAAsB,YAAY,MAAM,IAAK;AAAA,EACxD;AAAA,EACD,UAAU;AACd;ACFA,IAAI,gBAAiB,SAAU,QAAQ;AACnC,YAAUC,gBAAe,MAAM;AAC/B,WAASA,eAAc,aAAa,aAAa,oBAAoB;AACjE,QAAI,gBAAgB,QAAQ;AAAE,oBAAc;AAAA,IAAS;AACrD,QAAI,gBAAgB,QAAQ;AAAE,oBAAc;AAAA,IAAS;AACrD,QAAI,uBAAuB,QAAQ;AAAE,2BAAqB;AAAA,IAAsB;AAChF,QAAI,QAAQ,OAAO,KAAK,IAAI,KAAK;AACjC,UAAM,cAAc;AACpB,UAAM,cAAc;AACpB,UAAM,qBAAqB;AAC3B,UAAM,UAAU,CAAE;AAClB,UAAM,sBAAsB;AAC5B,UAAM,sBAAsB,gBAAgB;AAC5C,UAAM,cAAc,KAAK,IAAI,GAAG,WAAW;AAC3C,UAAM,cAAc,KAAK,IAAI,GAAG,WAAW;AAC3C,WAAO;AAAA,EACf;AACI,EAAAA,eAAc,UAAU,OAAO,SAAU,OAAO;AAC5C,QAAI,KAAK,MAAM,YAAY,GAAG,WAAW,UAAU,GAAG,SAAS,sBAAsB,GAAG,qBAAqB,qBAAqB,GAAG,oBAAoB,cAAc,GAAG;AAC1K,QAAI,CAAC,WAAW;AACZ,cAAQ,KAAK,KAAK;AAClB,OAAC,uBAAuB,QAAQ,KAAK,mBAAmB,IAAK,IAAG,WAAW;AAAA,IACvF;AACQ,SAAK,YAAa;AAClB,WAAO,UAAU,KAAK,KAAK,MAAM,KAAK;AAAA,EACzC;AACD,EAAAA,eAAc,UAAU,aAAa,SAAU,YAAY;AACvD,SAAK,eAAgB;AACrB,SAAK,YAAa;AAClB,QAAI,eAAe,KAAK,gBAAgB,UAAU;AAClD,QAAI,KAAK,MAAM,sBAAsB,GAAG,qBAAqB,UAAU,GAAG;AAC1E,QAAI,OAAO,QAAQ,MAAO;AAC1B,aAAS3B,KAAI,GAAGA,KAAI,KAAK,UAAU,CAAC,WAAW,QAAQA,MAAK,sBAAsB,IAAI,GAAG;AACrF,iBAAW,KAAK,KAAKA,EAAC,CAAC;AAAA,IACnC;AACQ,SAAK,wBAAwB,UAAU;AACvC,WAAO;AAAA,EACV;AACD,EAAA2B,eAAc,UAAU,cAAc,WAAY;AAC9C,QAAI,KAAK,MAAM,cAAc,GAAG,aAAa,qBAAqB,GAAG,oBAAoB,UAAU,GAAG,SAAS,sBAAsB,GAAG;AACxI,QAAI,sBAAsB,sBAAsB,IAAI,KAAK;AACzD,kBAAc,YAAY,qBAAqB,QAAQ,UAAU,QAAQ,OAAO,GAAG,QAAQ,SAAS,kBAAkB;AACtH,QAAI,CAAC,qBAAqB;AACtB,UAAI,MAAM,mBAAmB,IAAK;AAClC,UAAIC,QAAO;AACX,eAAS5B,KAAI,GAAGA,KAAI,QAAQ,UAAU,QAAQA,EAAC,KAAK,KAAKA,MAAK,GAAG;AAC7D,QAAA4B,QAAO5B;AAAA,MACvB;AACY,MAAA4B,SAAQ,QAAQ,OAAO,GAAGA,QAAO,CAAC;AAAA,IAC9C;AAAA,EACK;AACD,SAAOD;AACX,EAAE,OAAO;ACrDT,IAAI,SAAU,SAAU,QAAQ;AAC5B,YAAUE,SAAQ,MAAM;AACxB,WAASA,QAAO,WAAW,MAAM;AAC7B,WAAO,OAAO,KAAK,IAAI,KAAK;AAAA,EACpC;AACI,EAAAA,QAAO,UAAU,WAAW,SAAU,OAAO,OAAO;AAEhD,WAAO;AAAA,EACV;AACD,SAAOA;AACX,EAAE,YAAY;ACXP,IAAI,mBAAmB;AAAA,EAC1B,aAAa,SAAUtC,UAAS,SAAS;AACrC,QAAI,OAAO,CAAE;AACb,aAAS,KAAK,GAAG,KAAK,UAAU,QAAQ,MAAM;AAC1C,WAAK,KAAK,CAAC,IAAI,UAAU,EAAE;AAAA,IACvC;AAKQ,WAAO,YAAY,MAAM,QAAQ,cAAc,CAACA,UAAS,OAAO,GAAG,OAAO,IAAI,CAAC,CAAC;AAAA,EACnF;AAAA,EACD,eAAe,SAAU,QAAQ;AAE7B,WAAwF,cAAe,MAAM;AAAA,EAChH;AAAA,EACD,UAAU;AACd;ACdA,IAAI,cAAe,SAAU,QAAQ;AACjC,YAAUuC,cAAa,MAAM;AAC7B,WAASA,aAAY,WAAW,MAAM;AAClC,QAAI,QAAQ,OAAO,KAAK,MAAM,WAAW,IAAI,KAAK;AAClD,UAAM,YAAY;AAClB,UAAM,OAAO;AACb,UAAM,UAAU;AAChB,WAAO;AAAA,EACf;AACI,EAAAA,aAAY,UAAU,WAAW,SAAU,OAAO,OAAO;AACrD,QAAI;AACJ,QAAI,UAAU,QAAQ;AAAE,cAAQ;AAAA,IAAE;AAClC,QAAI,KAAK,QAAQ;AACb,aAAO;AAAA,IACnB;AACQ,SAAK,QAAQ;AACb,QAAI,KAAK,KAAK;AACd,QAAI,YAAY,KAAK;AACrB,QAAI,MAAM,MAAM;AACZ,WAAK,KAAK,KAAK,eAAe,WAAW,IAAI,KAAK;AAAA,IAC9D;AACQ,SAAK,UAAU;AACf,SAAK,QAAQ;AACb,SAAK,MAAM,KAAK,KAAK,QAAQ,QAAQ,OAAO,SAAS,KAAK,KAAK,eAAe,WAAW,KAAK,IAAI,KAAK;AACvG,WAAO;AAAA,EACV;AACD,EAAAA,aAAY,UAAU,iBAAiB,SAAU,WAAW,KAAK,OAAO;AACpE,QAAI,UAAU,QAAQ;AAAE,cAAQ;AAAA,IAAE;AAClC,WAAO,iBAAiB,YAAY,UAAU,MAAM,KAAK,WAAW,IAAI,GAAG,KAAK;AAAA,EACnF;AACD,EAAAA,aAAY,UAAU,iBAAiB,SAAU,YAAY,IAAI,OAAO;AACpE,QAAI,UAAU,QAAQ;AAAE,cAAQ;AAAA,IAAE;AAClC,QAAI,SAAS,QAAQ,KAAK,UAAU,SAAS,KAAK,YAAY,OAAO;AACjE,aAAO;AAAA,IACnB;AACQ,QAAI,MAAM,MAAM;AACZ,uBAAiB,cAAc,EAAE;AAAA,IAC7C;AACQ,WAAO;AAAA,EACV;AACD,EAAAA,aAAY,UAAU,UAAU,SAAU,OAAO,OAAO;AACpD,QAAI,KAAK,QAAQ;AACb,aAAO,IAAI,MAAM,8BAA8B;AAAA,IAC3D;AACQ,SAAK,UAAU;AACf,QAAI,QAAQ,KAAK,SAAS,OAAO,KAAK;AACtC,QAAI,OAAO;AACP,aAAO;AAAA,IACnB,WACiB,KAAK,YAAY,SAAS,KAAK,MAAM,MAAM;AAChD,WAAK,KAAK,KAAK,eAAe,KAAK,WAAW,KAAK,IAAI,IAAI;AAAA,IACvE;AAAA,EACK;AACD,EAAAA,aAAY,UAAU,WAAW,SAAU,OAAO,QAAQ;AACtD,QAAI,UAAU;AACd,QAAI;AACJ,QAAI;AACA,WAAK,KAAK,KAAK;AAAA,IAC3B,SACejC,IAAG;AACN,gBAAU;AACV,mBAAaA,KAAIA,KAAI,IAAI,MAAM,oCAAoC;AAAA,IAC/E;AACQ,QAAI,SAAS;AACT,WAAK,YAAa;AAClB,aAAO;AAAA,IACnB;AAAA,EACK;AACD,EAAAiC,aAAY,UAAU,cAAc,WAAY;AAC5C,QAAI,CAAC,KAAK,QAAQ;AACd,UAAI,KAAK,MAAM,KAAK,GAAG,IAAI,YAAY,GAAG;AAC1C,UAAI,UAAU,UAAU;AACxB,WAAK,OAAO,KAAK,QAAQ,KAAK,YAAY;AAC1C,WAAK,UAAU;AACf,gBAAU,SAAS,IAAI;AACvB,UAAI,MAAM,MAAM;AACZ,aAAK,KAAK,KAAK,eAAe,WAAW,IAAI,IAAI;AAAA,MACjE;AACY,WAAK,QAAQ;AACb,aAAO,UAAU,YAAY,KAAK,IAAI;AAAA,IAClD;AAAA,EACK;AACD,SAAOA;AACX,EAAE,MAAM;ACtFR,IAAI,YAAa,WAAY;AACzB,WAASC,WAAU,qBAAqB,KAAK;AACzC,QAAI,QAAQ,QAAQ;AAAE,YAAMA,WAAU;AAAA,IAAI;AAC1C,SAAK,sBAAsB;AAC3B,SAAK,MAAM;AAAA,EACnB;AACI,EAAAA,WAAU,UAAU,WAAW,SAAU,MAAM,OAAO,OAAO;AACzD,QAAI,UAAU,QAAQ;AAAE,cAAQ;AAAA,IAAE;AAClC,WAAO,IAAI,KAAK,oBAAoB,MAAM,IAAI,EAAE,SAAS,OAAO,KAAK;AAAA,EACxE;AACD,EAAAA,WAAU,MAAM,sBAAsB;AACtC,SAAOA;AACX;ACXA,IAAI,iBAAkB,SAAU,QAAQ;AACpC,YAAUC,iBAAgB,MAAM;AAChC,WAASA,gBAAe,iBAAiB,KAAK;AAC1C,QAAI,QAAQ,QAAQ;AAAE,YAAM,UAAU;AAAA,IAAI;AAC1C,QAAI,QAAQ,OAAO,KAAK,MAAM,iBAAiB,GAAG,KAAK;AACvD,UAAM,UAAU,CAAE;AAClB,UAAM,UAAU;AAChB,WAAO;AAAA,EACf;AACI,EAAAA,gBAAe,UAAU,QAAQ,SAAU,QAAQ;AAC/C,QAAI,UAAU,KAAK;AACnB,QAAI,KAAK,SAAS;AACd,cAAQ,KAAK,MAAM;AACnB;AAAA,IACZ;AACQ,QAAI;AACJ,SAAK,UAAU;AACf,OAAG;AACC,UAAK,QAAQ,OAAO,QAAQ,OAAO,OAAO,OAAO,KAAK,GAAI;AACtD;AAAA,MAChB;AAAA,IACA,SAAkB,SAAS,QAAQ,MAAO;AAClC,SAAK,UAAU;AACf,QAAI,OAAO;AACP,aAAQ,SAAS,QAAQ,SAAU;AAC/B,eAAO,YAAa;AAAA,MACpC;AACY,YAAM;AAAA,IAClB;AAAA,EACK;AACD,SAAOA;AACX,EAAE,SAAS;AC/BJ,IAAI,iBAAiB,IAAI,eAAe,WAAW;AACnD,IAAI,QAAQ;ACFZ,IAAI,QAAQ,IAAI,WAAW,SAAU,YAAY;AAAE,SAAO,WAAW,SAAU;CAAG;ACAlF,SAAS,YAAY,OAAO;AAC/B,SAAO,SAAS,WAAW,MAAM,QAAQ;AAC7C;ACDA,SAAS,KAAK,KAAK;AACf,SAAO,IAAI,IAAI,SAAS,CAAC;AAC7B;AACO,SAAS,kBAAkB,MAAM;AACpC,SAAO,WAAW,KAAK,IAAI,CAAC,IAAI,KAAK,IAAG,IAAK;AACjD;AACO,SAAS,aAAa,MAAM;AAC/B,SAAO,YAAY,KAAK,IAAI,CAAC,IAAI,KAAK,IAAG,IAAK;AAClD;AACO,SAAS,UAAU,MAAM,cAAc;AAC1C,SAAO,OAAO,KAAK,IAAI,MAAM,WAAW,KAAK,IAAG,IAAK;AACzD;ACbO,IAAI,cAAe,SAAUb,IAAG;AAAE,SAAOA,MAAK,OAAOA,GAAE,WAAW,YAAY,OAAOA,OAAM;AAAW;ACCtG,SAAS,UAAU,OAAO;AAC7B,SAAO,WAAW,UAAU,QAAQ,UAAU,SAAS,SAAS,MAAM,IAAI;AAC9E;ACDO,SAAS,oBAAoB,OAAO;AACvC,SAAO,WAAW,MAAMG,UAAiB,CAAC;AAC9C;ACHO,SAAS,gBAAgB,KAAK;AACjC,SAAO,OAAO,iBAAiB,WAAW,QAAQ,QAAQ,QAAQ,SAAS,SAAS,IAAI,OAAO,aAAa,CAAC;AACjH;ACHO,SAAS,iCAAiC,OAAO;AACpD,SAAO,IAAI,UAAU,mBAAmB,UAAU,QAAQ,OAAO,UAAU,WAAW,sBAAsB,MAAM,QAAQ,OAAO,0HAA0H;AAC/P;ACFO,SAAS,oBAAoB;AAChC,MAAI,OAAO,WAAW,cAAc,CAAC,OAAO,UAAU;AAClD,WAAO;AAAA,EACf;AACI,SAAO,OAAO;AAClB;AACO,IAAI,WAAW,kBAAmB;ACJlC,SAAS,WAAW,OAAO;AAC9B,SAAO,WAAW,UAAU,QAAQ,UAAU,SAAS,SAAS,MAAMW,QAAe,CAAC;AAC1F;ACFO,SAAS,mCAAmC,gBAAgB;AAC/D,SAAO,iBAAiB,MAAM,WAAW,SAAS,uCAAuC;AACrF,QAAI,QAAQ,IAAI,OAAO;AACvB,WAAO,YAAY,MAAM,SAAU,IAAI;AACnC,cAAQ,GAAG,OAAK;AAAA,QACZ,KAAK;AACD,mBAAS,eAAe,UAAW;AACnC,aAAG,QAAQ;AAAA,QACf,KAAK;AACD,aAAG,KAAK,KAAK,CAAC,GAAK,EAAA,GAAG,EAAE,CAAC;AACzB,aAAG,QAAQ;AAAA,QACf,KAAK;AAED,iBAAO,CAAC,GAAG,QAAQ,OAAO,KAAM,CAAA,CAAC;AAAA,QACrC,KAAK;AACD,eAAK,GAAG,QAAQ,QAAQ,GAAG,OAAO,OAAO,GAAG;AAC5C,cAAI,CAAC,KAAM,QAAO,CAAC,GAAG,CAAC;AACvB,iBAAO,CAAC,GAAG,QAAQ,MAAM,CAAC;AAAA,QAC9B,KAAK;AAAG,iBAAO,CAAC,GAAG,GAAG,KAAI,CAAE;AAAA,QAC5B,KAAK;AAAG,iBAAO,CAAC,GAAG,QAAQ,KAAK,CAAC;AAAA,QACjC,KAAK;AAAG,iBAAO,CAAC,GAAG,GAAG,KAAI,CAAE;AAAA,QAC5B,KAAK;AACD,aAAG,KAAM;AACT,iBAAO,CAAC,GAAG,CAAC;AAAA,QAChB,KAAK;AAAG,iBAAO,CAAC,GAAG,EAAE;AAAA,QACrB,KAAK;AACD,iBAAO,YAAa;AACpB,iBAAO,CAAC,CAAC;AAAA,QACb,KAAK;AAAI,iBAAO,CAAC,CAAC;AAAA,MAClC;AAAA,IACA,CAAS;AAAA,EACT,CAAK;AACL;AACO,SAAS,qBAAqB,KAAK;AACtC,SAAO,WAAW,QAAQ,QAAQ,QAAQ,SAAS,SAAS,IAAI,SAAS;AAC7E;ACzBO,SAAS,UAAU,OAAO;AAC7B,MAAI,iBAAiB,YAAY;AAC7B,WAAO;AAAA,EACf;AACI,MAAI,SAAS,MAAM;AACf,QAAI,oBAAoB,KAAK,GAAG;AAC5B,aAAO,sBAAsB,KAAK;AAAA,IAC9C;AACQ,QAAI,YAAY,KAAK,GAAG;AACpB,aAAO,cAAc,KAAK;AAAA,IACtC;AACQ,QAAI,UAAU,KAAK,GAAG;AAClB,aAAO,YAAY,KAAK;AAAA,IACpC;AACQ,QAAI,gBAAgB,KAAK,GAAG;AACxB,aAAO,kBAAkB,KAAK;AAAA,IAC1C;AACQ,QAAI,WAAW,KAAK,GAAG;AACnB,aAAO,aAAa,KAAK;AAAA,IACrC;AACQ,QAAI,qBAAqB,KAAK,GAAG;AAC7B,aAAO,uBAAuB,KAAK;AAAA,IAC/C;AAAA,EACA;AACI,QAAM,iCAAiC,KAAK;AAChD;AACO,SAAS,sBAAsB,KAAK;AACvC,SAAO,IAAI,WAAW,SAAU,YAAY;AACxC,QAAI,MAAM,IAAIX,UAAiB,EAAG;AAClC,QAAI,WAAW,IAAI,SAAS,GAAG;AAC3B,aAAO,IAAI,UAAU,UAAU;AAAA,IAC3C;AACQ,UAAM,IAAI,UAAU,gEAAgE;AAAA,EAC5F,CAAK;AACL;AACO,SAAS,cAAc,OAAO;AACjC,SAAO,IAAI,WAAW,SAAU,YAAY;AACxC,aAAStB,KAAI,GAAGA,KAAI,MAAM,UAAU,CAAC,WAAW,QAAQA,MAAK;AACzD,iBAAW,KAAK,MAAMA,EAAC,CAAC;AAAA,IACpC;AACQ,eAAW,SAAU;AAAA,EAC7B,CAAK;AACL;AACO,SAAS,YAAY,SAAS;AACjC,SAAO,IAAI,WAAW,SAAU,YAAY;AACxC,YACK,KAAK,SAAU,OAAO;AACvB,UAAI,CAAC,WAAW,QAAQ;AACpB,mBAAW,KAAK,KAAK;AACrB,mBAAW,SAAU;AAAA,MACrC;AAAA,IACA,GAAW,SAAU,KAAK;AAAE,aAAO,WAAW,MAAM,GAAG;AAAA,IAAI,CAAA,EAC9C,KAAK,MAAM,oBAAoB;AAAA,EAC5C,CAAK;AACL;AACO,SAAS,aAAa,UAAU;AACnC,SAAO,IAAI,WAAW,SAAU,YAAY;AACxC,QAAI,KAAK;AACT,QAAI;AACA,eAAS,aAAa,SAAS,QAAQ,GAAG,eAAe,WAAW,KAAI,GAAI,CAAC,aAAa,MAAM,eAAe,WAAW,KAAI,GAAI;AAC9H,YAAI,QAAQ,aAAa;AACzB,mBAAW,KAAK,KAAK;AACrB,YAAI,WAAW,QAAQ;AACnB;AAAA,QACpB;AAAA,MACA;AAAA,IACA,SACe,OAAO;AAAE,YAAM,EAAE,OAAO,MAAK;AAAA,IAAG,UAC/B;AACJ,UAAI;AACA,YAAI,gBAAgB,CAAC,aAAa,SAAS,KAAK,WAAW,QAAS,IAAG,KAAK,UAAU;AAAA,MACtG,UACoB;AAAE,YAAI,IAAK,OAAM,IAAI;AAAA,MAAM;AAAA,IAC/C;AACQ,eAAW,SAAU;AAAA,EAC7B,CAAK;AACL;AACO,SAAS,kBAAkB,eAAe;AAC7C,SAAO,IAAI,WAAW,SAAU,YAAY;AACxCkC,cAAQ,eAAe,UAAU,EAAE,MAAM,SAAU,KAAK;AAAE,aAAO,WAAW,MAAM,GAAG;AAAA,IAAE,CAAE;AAAA,EACjG,CAAK;AACL;AACO,SAAS,uBAAuB,gBAAgB;AACnD,SAAO,kBAAkB,mCAAmC,cAAc,CAAC;AAC/E;AACA,SAASA,UAAQ,eAAe,YAAY;AACxC,MAAI,iBAAiB;AACrB,MAAI,KAAK;AACT,SAAO,UAAU,MAAM,QAAQ,QAAQ,WAAY;AAC/C,QAAI,OAAO;AACX,WAAO,YAAY,MAAM,SAAU,IAAI;AACnC,cAAQ,GAAG,OAAK;AAAA,QACZ,KAAK;AACD,aAAG,KAAK,KAAK,CAAC,GAAG,GAAG,GAAG,EAAE,CAAC;AAC1B,4BAAkB,cAAc,aAAa;AAC7C,aAAG,QAAQ;AAAA,QACf,KAAK;AAAG,iBAAO,CAAC,GAAG,gBAAgB,KAAI,CAAE;AAAA,QACzC,KAAK;AACD,cAAI,EAAE,oBAAoB,GAAG,QAAQ,CAAC,kBAAkB,MAAO,QAAO,CAAC,GAAG,CAAC;AAC3E,kBAAQ,kBAAkB;AAC1B,qBAAW,KAAK,KAAK;AACrB,cAAI,WAAW,QAAQ;AACnB,mBAAO,CAAC,CAAC;AAAA,UACjC;AACoB,aAAG,QAAQ;AAAA,QACf,KAAK;AAAG,iBAAO,CAAC,GAAG,CAAC;AAAA,QACpB,KAAK;AAAG,iBAAO,CAAC,GAAG,EAAE;AAAA,QACrB,KAAK;AACD,kBAAQ,GAAG,KAAM;AACjB,gBAAM,EAAE,OAAO,MAAO;AACtB,iBAAO,CAAC,GAAG,EAAE;AAAA,QACjB,KAAK;AACD,aAAG,KAAK,KAAK,CAAC,GAAK,EAAA,GAAG,EAAE,CAAC;AACzB,cAAI,EAAE,qBAAqB,CAAC,kBAAkB,SAAS,KAAK,gBAAgB,SAAU,QAAO,CAAC,GAAG,CAAC;AAClG,iBAAO,CAAC,GAAG,GAAG,KAAK,eAAe,CAAC;AAAA,QACvC,KAAK;AACD,aAAG,KAAM;AACT,aAAG,QAAQ;AAAA,QACf,KAAK;AAAG,iBAAO,CAAC,GAAG,EAAE;AAAA,QACrB,KAAK;AACD,cAAI,IAAK,OAAM,IAAI;AACnB,iBAAO,CAAC,CAAC;AAAA,QACb,KAAK;AAAI,iBAAO,CAAC,CAAC;AAAA,QAClB,KAAK;AACD,qBAAW,SAAU;AACrB,iBAAO,CAAC,CAAC;AAAA,MAC7B;AAAA,IACA,CAAS;AAAA,EACT,CAAK;AACL;AC7IO,SAAS,gBAAgB,oBAAoB,WAAW,MAAM,OAAO,QAAQ;AAChF,MAAI,UAAU,QAAQ;AAAE,YAAQ;AAAA,EAAE;AAClC,MAAI,WAAW,QAAQ;AAAE,aAAS;AAAA,EAAM;AACxC,MAAI,uBAAuB,UAAU,SAAS,WAAY;AACtD,SAAM;AACN,QAAI,QAAQ;AACR,yBAAmB,IAAI,KAAK,SAAS,MAAM,KAAK,CAAC;AAAA,IAC7D,OACa;AACD,WAAK,YAAa;AAAA,IAC9B;AAAA,EACK,GAAE,KAAK;AACR,qBAAmB,IAAI,oBAAoB;AAC3C,MAAI,CAAC,QAAQ;AACT,WAAO;AAAA,EACf;AACA;ACbO,SAAS,UAAU,WAAW,OAAO;AACxC,MAAI,UAAU,QAAQ;AAAE,YAAQ;AAAA,EAAE;AAClC,SAAO,QAAQ,SAAU,QAAQ,YAAY;AACzC,WAAO,UAAU,yBAAyB,YAAY,SAAU,OAAO;AAAE,aAAO,gBAAgB,YAAY,WAAW,WAAY;AAAE,eAAO,WAAW,KAAK,KAAK;AAAA,SAAM,KAAK;AAAA,IAAE,GAAI,WAAY;AAAE,aAAO,gBAAgB,YAAY,WAAW,WAAY;AAAE,eAAO,WAAW,SAAQ;AAAA,MAAK,GAAE,KAAK;AAAA,OAAM,SAAU,KAAK;AAAE,aAAO,gBAAgB,YAAY,WAAW,WAAY;AAAE,eAAO,WAAW,MAAM,GAAG;AAAA,SAAM,KAAK;AAAA,IAAE,CAAE,CAAC;AAAA,EACxa,CAAK;AACL;ACPO,SAAS,YAAY,WAAW,OAAO;AAC1C,MAAI,UAAU,QAAQ;AAAE,YAAQ;AAAA,EAAE;AAClC,SAAO,QAAQ,SAAU,QAAQ,YAAY;AACzC,eAAW,IAAI,UAAU,SAAS,WAAY;AAAE,aAAO,OAAO,UAAU,UAAU;AAAA,IAAI,GAAE,KAAK,CAAC;AAAA,EACtG,CAAK;AACL;ACHO,SAAS,mBAAmB,OAAO,WAAW;AACjD,SAAO,UAAU,KAAK,EAAE,KAAK,YAAY,SAAS,GAAG,UAAU,SAAS,CAAC;AAC7E;ACFO,SAAS,gBAAgB,OAAO,WAAW;AAC9C,SAAO,UAAU,KAAK,EAAE,KAAK,YAAY,SAAS,GAAG,UAAU,SAAS,CAAC;AAC7E;ACJO,SAAS,cAAc,OAAO,WAAW;AAC5C,SAAO,IAAI,WAAW,SAAU,YAAY;AACxC,QAAIlC,KAAI;AACR,WAAO,UAAU,SAAS,WAAY;AAClC,UAAIA,OAAM,MAAM,QAAQ;AACpB,mBAAW,SAAU;AAAA,MACrC,OACiB;AACD,mBAAW,KAAK,MAAMA,IAAG,CAAC;AAC1B,YAAI,CAAC,WAAW,QAAQ;AACpB,eAAK,SAAU;AAAA,QACnC;AAAA,MACA;AAAA,IACA,CAAS;AAAA,EACT,CAAK;AACL;ACZO,SAAS,iBAAiB,OAAO,WAAW;AAC/C,SAAO,IAAI,WAAW,SAAU,YAAY;AACxC,QAAImC;AACJ,oBAAgB,YAAY,WAAW,WAAY;AAC/CA,mBAAW,MAAMF,QAAe,EAAG;AACnC,sBAAgB,YAAY,WAAW,WAAY;AAC/C,YAAI;AACJ,YAAI;AACJ,YAAI;AACJ,YAAI;AACA,UAAC,KAAKE,WAAS,QAAQ,QAAQ,GAAG,OAAO,OAAO,GAAG;AAAA,QACvE,SACuB,KAAK;AACR,qBAAW,MAAM,GAAG;AACpB;AAAA,QACpB;AACgB,YAAI,MAAM;AACN,qBAAW,SAAU;AAAA,QACzC,OACqB;AACD,qBAAW,KAAK,KAAK;AAAA,QACzC;AAAA,MACA,GAAe,GAAG,IAAI;AAAA,IACtB,CAAS;AACD,WAAO,WAAY;AAAE,aAAO,WAAWA,eAAa,QAAQA,eAAa,SAAS,SAASA,WAAS,MAAM,KAAKA,WAAS,OAAQ;AAAA,IAAG;AAAA,EAC3I,CAAK;AACL;AC5BO,SAAS,sBAAsB,OAAO,WAAW;AACpD,MAAI,CAAC,OAAO;AACR,UAAM,IAAI,MAAM,yBAAyB;AAAA,EACjD;AACI,SAAO,IAAI,WAAW,SAAU,YAAY;AACxC,oBAAgB,YAAY,WAAW,WAAY;AAC/C,UAAIA,YAAW,MAAM,OAAO,aAAa,EAAG;AAC5C,sBAAgB,YAAY,WAAW,WAAY;AAC/C,QAAAA,UAAS,KAAI,EAAG,KAAK,SAAU,QAAQ;AACnC,cAAI,OAAO,MAAM;AACb,uBAAW,SAAU;AAAA,UAC7C,OACyB;AACD,uBAAW,KAAK,OAAO,KAAK;AAAA,UACpD;AAAA,QACA,CAAiB;AAAA,MACjB,GAAe,GAAG,IAAI;AAAA,IACtB,CAAS;AAAA,EACT,CAAK;AACL;ACnBO,SAAS,2BAA2B,OAAO,WAAW;AACzD,SAAO,sBAAsB,mCAAmC,KAAK,GAAG,SAAS;AACrF;ACSO,SAAS,UAAU,OAAO,WAAW;AACxC,MAAI,SAAS,MAAM;AACf,QAAI,oBAAoB,KAAK,GAAG;AAC5B,aAAO,mBAAmB,OAAO,SAAS;AAAA,IACtD;AACQ,QAAI,YAAY,KAAK,GAAG;AACpB,aAAO,cAAc,OAAO,SAAS;AAAA,IACjD;AACQ,QAAI,UAAU,KAAK,GAAG;AAClB,aAAO,gBAAgB,OAAO,SAAS;AAAA,IACnD;AACQ,QAAI,gBAAgB,KAAK,GAAG;AACxB,aAAO,sBAAsB,OAAO,SAAS;AAAA,IACzD;AACQ,QAAI,WAAW,KAAK,GAAG;AACnB,aAAO,iBAAiB,OAAO,SAAS;AAAA,IACpD;AACQ,QAAI,qBAAqB,KAAK,GAAG;AAC7B,aAAO,2BAA2B,OAAO,SAAS;AAAA,IAC9D;AAAA,EACA;AACI,QAAM,iCAAiC,KAAK;AAChD;ACjCO,SAAS,KAAK,OAAO,WAAW;AACnC,SAAO,YAAY,UAAU,OAAO,SAAS,IAAI,UAAU,KAAK;AACpE;ACFO,SAAS,KAAK;AACjB,MAAI,OAAO,CAAE;AACb,WAAS,KAAK,GAAG,KAAK,UAAU,QAAQ,MAAM;AAC1C,SAAK,EAAE,IAAI,UAAU,EAAE;AAAA,EAC/B;AACI,MAAI,YAAY,aAAa,IAAI;AACjC,SAAO,KAAK,MAAM,SAAS;AAC/B;ACPO,SAAS,WAAW,qBAAqB,WAAW;AACvD,MAAI,eAAe,WAAW,mBAAmB,IAAI,sBAAsB,WAAY;AAAE,WAAO;AAAA,EAAsB;AACtH,MAAI,OAAO,SAAU,YAAY;AAAE,WAAO,WAAW,MAAM,aAAc,CAAA;AAAA,EAAI;AAC7E,SAAO,IAAI,WAAmG,IAAI;AACtH;ACJO,SAAS,aAAa,KAAK;AAC9B,SAAO,CAAC,CAAC,QAAQ,eAAe,cAAe,WAAW,IAAI,IAAI,KAAK,WAAW,IAAI,SAAS;AACnG;ACHO,IAAI,aAAa,iBAAiB,SAAU,QAAQ;AACvD,SAAO,SAAS,iBAAiB;AAC7B,WAAO,IAAI;AACX,SAAK,OAAO;AACZ,SAAK,UAAU;AAAA,EAClB;AACL,CAAC;ACNM,SAAS,cAAc,QAAQC,SAAQ;AAE1C,SAAO,IAAI,QAAQ,SAAU,SAAS,QAAQ;AAC1C,QAAI,YAAY;AAChB,QAAI;AACJ,WAAO,UAAU;AAAA,MACb,MAAM,SAAU,OAAO;AACnB,iBAAS;AACT,oBAAY;AAAA,MACf;AAAA,MACD,OAAO;AAAA,MACP,UAAU,WAAY;AAClB,YAAI,WAAW;AACX,kBAAQ,MAAM;AAAA,QAClC,OAIqB;AACD,iBAAO,IAAI,YAAY;AAAA,QAC3C;AAAA,MACa;AAAA,IACb,CAAS;AAAA,EACT,CAAK;AACL;ACvBO,SAAS,eAAe,QAAQA,SAAQ;AAE3C,SAAO,IAAI,QAAQ,SAAU,SAAS,QAAQ;AAC1C,QAAI,aAAa,IAAI,eAAe;AAAA,MAChC,MAAM,SAAU,OAAO;AACnB,gBAAQ,KAAK;AACb,mBAAW,YAAa;AAAA,MAC3B;AAAA,MACD,OAAO;AAAA,MACP,UAAU,WAAY;AAIb;AACD,iBAAO,IAAI,YAAY;AAAA,QAC3C;AAAA,MACa;AAAA,IACb,CAAS;AACD,WAAO,UAAU,UAAU;AAAA,EACnC,CAAK;AACL;ACtBO,SAAS,YAAY,OAAO;AAC/B,SAAO,iBAAiB,QAAQ,CAAC,MAAM,KAAK;AAChD;ACAO,SAAS,IAAI,SAAS,SAAS;AAClC,SAAO,QAAQ,SAAU,QAAQ,YAAY;AACzC,QAAI,QAAQ;AACZ,WAAO,UAAU,yBAAyB,YAAY,SAAU,OAAO;AACnE,iBAAW,KAAK,QAAQ,KAAK,SAAS,OAAO,OAAO,CAAC;AAAA,IACjE,CAAS,CAAC;AAAA,EACV,CAAK;AACL;ACPA,IAAIC,YAAU,MAAM;AACpB,SAAS,YAAY,IAAI,MAAM;AAC3B,SAAOA,UAAQ,IAAI,IAAI,GAAG,MAAM,QAAQ,cAAc,CAAE,GAAE,OAAO,IAAI,CAAC,CAAC,IAAI,GAAG,IAAI;AACtF;AACO,SAAS,iBAAiB,IAAI;AACjC,SAAO,IAAI,SAAU,MAAM;AAAE,WAAO,YAAY,IAAI,IAAI;AAAA,GAAI;AAChE;ACmBO,SAAS,kBAAkB,aAAa,WAAW,gBAAgB;AACtE,MAAI,mBAAmB,QAAQ;AAAE,qBAAiB;AAAA,EAAS;AAC3D,SAAO,SAAU,YAAY;AACzB,kBAAc,WAAW,WAAY;AACjC,UAAI,SAAS,YAAY;AACzB,UAAI,SAAS,IAAI,MAAM,MAAM;AAC7B,UAAI,SAAS;AACb,UAAI,uBAAuB;AAC3B,UAAI,UAAU,SAAUrC,IAAG;AACvB,sBAAc,WAAW,WAAY;AACjC,cAAI,SAAS,KAAK,YAAYA,EAAC,GAAG,SAAS;AAC3C,cAAI,gBAAgB;AACpB,iBAAO,UAAU,yBAAyB,YAAY,SAAU,OAAO;AACnE,mBAAOA,EAAC,IAAI;AACZ,gBAAI,CAAC,eAAe;AAChB,8BAAgB;AAChB;AAAA,YAC5B;AACwB,gBAAI,CAAC,sBAAsB;AACvB,yBAAW,KAAK,eAAe,OAAO,MAAO,CAAA,CAAC;AAAA,YAC1E;AAAA,UACA,GAAuB,WAAY;AACX,gBAAI,CAAC,EAAE,QAAQ;AACX,yBAAW,SAAU;AAAA,YACjD;AAAA,UACA,CAAqB,CAAC;AAAA,QACL,GAAE,UAAU;AAAA,MAChB;AACD,eAASA,KAAI,GAAGA,KAAI,QAAQA,MAAK;AAC7B,gBAAQA,EAAC;AAAA,MACzB;AAAA,IACA,CAAqB;AAAA,EAChB;AACL;AACA,SAAS,cAAc,WAAW,SAAS,cAAc;AAIhD;AACD,YAAS;AAAA,EACjB;AACA;ACjEO,SAAS,eAAe,QAAQ,YAAY,SAAS,YAAY,cAAc,QAAQ,mBAAmB,qBAAqB;AAClI,MAAI,SAAS,CAAE;AACf,MAAI,SAAS;AACb,MAAI,QAAQ;AACZ,MAAI,aAAa;AACjB,MAAI,gBAAgB,WAAY;AAC5B,QAAI,cAAc,CAAC,OAAO,UAAU,CAAC,QAAQ;AACzC,iBAAW,SAAU;AAAA,IACjC;AAAA,EACK;AACD,MAAI,YAAY,SAAU,OAAO;AAAE,WAAQ,SAAS,aAAa,WAAW,KAAK,IAAI,OAAO,KAAK,KAAK;AAAA,EAAK;AAC3G,MAAI,aAAa,SAAU,OAAO;AAE9B;AACA,QAAI,gBAAgB;AACpB,cAAU,QAAQ,OAAO,OAAO,CAAC,EAAE,UAAU,yBAAyB,YAAY,SAAU,YAAY;AAK/F;AACD,mBAAW,KAAK,UAAU;AAAA,MAC1C;AAAA,IACA,GAAW,WAAY;AACX,sBAAgB;AAAA,IACnB,GAAE,QAAW,WAAY;AACtB,UAAI,eAAe;AACf,YAAI;AACA;AACA,cAAI,UAAU,WAAY;AACtB,gBAAI,gBAAgB,OAAO,MAAO;AAClC,gBAAI,kBAAmB;AAAA,iBAGlB;AACD,yBAAW,aAAa;AAAA,YACpD;AAAA,UACqB;AACD,iBAAO,OAAO,UAAU,SAAS,YAAY;AACzC,oBAAS;AAAA,UACjC;AACoB,wBAAe;AAAA,QACnC,SACuB,KAAK;AACR,qBAAW,MAAM,GAAG;AAAA,QACxC;AAAA,MACA;AAAA,IACA,CAAS,CAAC;AAAA,EACL;AACD,SAAO,UAAU,yBAAyB,YAAY,WAAW,WAAY;AACzE,iBAAa;AACb,kBAAe;AAAA,EACvB,CAAK,CAAC;AACF,SAAO,WAAY;AAAA,EAElB;AACL;ACtDO,SAAS,SAAS,SAAS,gBAAgB,YAAY;AAC1D,MAAI,eAAe,QAAQ;AAAE,iBAAa;AAAA,EAAS;AACnD,MAAI,WAAW,cAAc,GAAG;AAC5B,WAAO,SAAS,SAAUP,IAAGO,IAAG;AAAE,aAAO,IAAI,SAAU,GAAG,IAAI;AAAE,eAAO,eAAeP,IAAG,GAAGO,IAAG,EAAE;AAAA,MAAI,CAAA,EAAE,UAAU,QAAQP,IAAGO,EAAC,CAAC,CAAC;AAAA,IAAE,GAAI,UAAU;AAAA,EACvJ,WACa,OAAO,mBAAmB,UAAU;AACzC,iBAAa;AAAA,EACrB;AACI,SAAO,QAAQ,SAAU,QAAQ,YAAY;AAAE,WAAO,eAAe,QAAQ,YAAY,SAAS,UAAU;AAAA,EAAE,CAAE;AACpH;ACZO,SAAS,SAAS,YAAY;AACjC,MAAI,eAAe,QAAQ;AAAE,iBAAa;AAAA,EAAS;AACnD,SAAO,SAAS,UAAU,UAAU;AACxC;ACJO,SAAS,YAAY;AACxB,SAAO,SAAS,CAAC;AACrB;ACAO,SAAS,SAAS;AACrB,MAAI,OAAO,CAAE;AACb,WAAS,KAAK,GAAG,KAAK,UAAU,QAAQ,MAAM;AAC1C,SAAK,EAAE,IAAI,UAAU,EAAE;AAAA,EAC/B;AACI,SAAO,UAAW,EAAC,KAAK,MAAM,aAAa,IAAI,CAAC,CAAC;AACrD;ACPO,SAAS,MAAM,mBAAmB;AACrC,SAAO,IAAI,WAAW,SAAU,YAAY;AACxC,cAAU,kBAAiB,CAAE,EAAE,UAAU,UAAU;AAAA,EAC3D,CAAK;AACL;ACFO,SAAS,MAAM,SAAS,qBAAqB,WAAW;AAE3D,MAAI,cAAc,QAAQ;AAAE,gBAAYsC;AAAAA,EAAe;AAUvD,SAAO,IAAI,WAAW,SAAU,YAAY;AACxC,QAAI,MAAM,YAAY,OAAO,IAAI,MAAW,UAAU,IAAG,IAAK;AAC9D,QAAI,MAAM,GAAG;AACT,YAAM;AAAA,IAClB;AACQ,QAAI5C,KAAI;AACR,WAAO,UAAU,SAAS,WAAY;AAClC,UAAI,CAAC,WAAW,QAAQ;AACpB,mBAAW,KAAKA,IAAG;AAId;AACD,qBAAW,SAAU;AAAA,QACzC;AAAA,MACA;AAAA,IACS,GAAE,GAAG;AAAA,EACd,CAAK;AACL;AC7BO,SAAS,QAAQ;AACpB,MAAI,OAAO,CAAE;AACb,WAAS,KAAK,GAAG,KAAK,UAAU,QAAQ,MAAM;AAC1C,SAAK,EAAE,IAAI,UAAU,EAAE;AAAA,EAC/B;AACI,MAAI,YAAY,aAAa,IAAI;AACjC,MAAI,aAAa,UAAU,MAAM,QAAQ;AACzC,MAAI,UAAU;AACd,SAAO,CAAC,QAAQ,SAER,QACF,QAAQ,WAAW,IAEb,UAAU,QAAQ,CAAC,CAAC,IAEpB,SAAS,UAAU,EAAE,KAAK,SAAS,SAAS,CAAC;AAC7D;ACrBA,IAAI,UAAU,MAAM;AACb,SAAS,eAAe,MAAM;AACjC,SAAO,KAAK,WAAW,KAAK,QAAQ,KAAK,CAAC,CAAC,IAAI,KAAK,CAAC,IAAI;AAC7D;ACDO,SAAS,OAAO,WAAW,SAAS;AACvC,SAAO,QAAQ,SAAU,QAAQ,YAAY;AACzC,QAAI,QAAQ;AACZ,WAAO,UAAU,yBAAyB,YAAY,SAAU,OAAO;AAAE,aAAO,UAAU,KAAK,SAAS,OAAO,OAAO,KAAK,WAAW,KAAK,KAAK;AAAA,IAAE,CAAE,CAAC;AAAA,EAC7J,CAAK;AACL;ACJO,SAAS,WAAW,UAAU;AACjC,SAAO,QAAQ,SAAU,QAAQ,YAAY;AACzC,QAAI,WAAW;AACf,QAAI,YAAY;AAChB,QAAI;AACJ,eAAW,OAAO,UAAU,yBAAyB,YAAY,QAAW,QAAW,SAAU,KAAK;AAClG,sBAAgB,UAAU,SAAS,KAAK,WAAW,QAAQ,EAAE,MAAM,CAAC,CAAC;AACrE,UAAI,UAAU;AACV,iBAAS,YAAa;AACtB,mBAAW;AACX,sBAAc,UAAU,UAAU;AAAA,MAClD,OACiB;AACD,oBAAY;AAAA,MAC5B;AAAA,IACA,CAAS,CAAC;AACF,QAAI,WAAW;AACX,eAAS,YAAa;AACtB,iBAAW;AACX,oBAAc,UAAU,UAAU;AAAA,IAC9C;AAAA,EACA,CAAK;AACL;AClBO,SAAS,gBAAgB;AAC5B,MAAI,OAAO,CAAE;AACb,WAAS,KAAK,GAAG,KAAK,UAAU,QAAQ,MAAM;AAC1C,SAAK,EAAE,IAAI,UAAU,EAAE;AAAA,EAC/B;AACI,MAAI,iBAAiB,kBAAkB,IAAI;AAC3C,SAAO,iBACD,KAAK,cAAc,MAAM,QAAQ,cAAc,CAAA,GAAI,OAAO,IAAI,CAAC,CAAC,GAAG,iBAAiB,cAAc,CAAC,IACnG,QAAQ,SAAU,QAAQ,YAAY;AACpC,sBAAkB,cAAc,CAAC,MAAM,GAAG,OAAO,eAAe,IAAI,CAAC,CAAC,CAAC,EAAE,UAAU;AAAA,EAC/F,CAAS;AACT;AChBO,SAAS,oBAAoB;AAChC,MAAI,eAAe,CAAE;AACrB,WAAS,KAAK,GAAG,KAAK,UAAU,QAAQ,MAAM;AAC1C,iBAAa,EAAE,IAAI,UAAU,EAAE;AAAA,EACvC;AACI,SAAO,cAAc,MAAM,QAAQ,cAAc,CAAA,GAAI,OAAO,YAAY,CAAC,CAAC;AAC9E;ACPO,SAAS,SAAS,UAAU;AAC/B,SAAO,QAAQ,SAAU,QAAQ,YAAY;AACzC,QAAI;AACA,aAAO,UAAU,UAAU;AAAA,IACvC,UACgB;AACJ,iBAAW,IAAI,QAAQ;AAAA,IACnC;AAAA,EACA,CAAK;AACL;ACLO,SAAS,MAAM,SAAS;AAC3B,MAAI,YAAY,QAAQ;AAAE,cAAU,CAAA;AAAA,EAAG;AACvC,MAAI,KAAK,QAAQ,WAAW,YAAY,OAAO,SAAS,WAAY;AAAE,WAAO,IAAI,QAAS;AAAA,EAAG,IAAG,IAAI,KAAK,QAAQ,cAAc,eAAe,OAAO,SAAS,OAAO,IAAI,KAAK,QAAQ,iBAAiB,kBAAkB,OAAO,SAAS,OAAO,IAAI,KAAK,QAAQ,qBAAqB,sBAAsB,OAAO,SAAS,OAAO;AACnU,SAAO,SAAU,eAAe;AAC5B,QAAI;AACJ,QAAI;AACJ,QAAI;AACJ,QAAI,WAAW;AACf,QAAI,eAAe;AACnB,QAAI,aAAa;AACjB,QAAI,cAAc,WAAY;AAC1B,0BAAoB,QAAQ,oBAAoB,SAAS,SAAS,gBAAgB,YAAa;AAC/F,wBAAkB;AAAA,IACrB;AACD,QAAI,QAAQ,WAAY;AACpB,kBAAa;AACb,mBAAa,UAAU;AACvB,qBAAe,aAAa;AAAA,IAC/B;AACD,QAAI,sBAAsB,WAAY;AAClC,UAAI,OAAO;AACX,YAAO;AACP,eAAS,QAAQ,SAAS,SAAS,SAAS,KAAK,YAAa;AAAA,IACjE;AACD,WAAO,QAAQ,SAAU,QAAQ,YAAY;AACzC;AACA,UAAI,CAAC,cAAc,CAAC,cAAc;AAC9B,oBAAa;AAAA,MAC7B;AACY,UAAI,OAAQ,UAAU,YAAY,QAAQ,YAAY,SAAS,UAAU;AACzE,iBAAW,IAAI,WAAY;AACvB;AACA,YAAI,aAAa,KAAK,CAAC,cAAc,CAAC,cAAc;AAChD,4BAAkB,YAAY,qBAAqB,mBAAmB;AAAA,QAC1F;AAAA,MACA,CAAa;AACD,WAAK,UAAU,UAAU;AACzB,UAAI,CAAC,cACD,WAAW,GAAG;AACd,qBAAa,IAAI,eAAe;AAAA,UAC5B,MAAM,SAAU,OAAO;AAAE,mBAAO,KAAK,KAAK,KAAK;AAAA,UAAI;AAAA,UACnD,OAAO,SAAU,KAAK;AAClB,yBAAa;AACb,wBAAa;AACb,8BAAkB,YAAY,OAAO,cAAc,GAAG;AACtD,iBAAK,MAAM,GAAG;AAAA,UACjB;AAAA,UACD,UAAU,WAAY;AAClB,2BAAe;AACf,wBAAa;AACb,8BAAkB,YAAY,OAAO,eAAe;AACpD,iBAAK,SAAU;AAAA,UAClB;AAAA,QACrB,CAAiB;AACD,kBAAU,MAAM,EAAE,UAAU,UAAU;AAAA,MACtD;AAAA,IACS,CAAA,EAAE,aAAa;AAAA,EACnB;AACL;AACA,SAAS,YAAY,OAAO,IAAI;AAC5B,MAAI,OAAO,CAAE;AACb,WAAS,KAAK,GAAG,KAAK,UAAU,QAAQ,MAAM;AAC1C,SAAK,KAAK,CAAC,IAAI,UAAU,EAAE;AAAA,EACnC;AACI,MAAI,OAAO,MAAM;AACb,UAAO;AACP;AAAA,EACR;AACI,MAAI,OAAO,OAAO;AACd;AAAA,EACR;AACI,MAAI,eAAe,IAAI,eAAe;AAAA,IAClC,MAAM,WAAY;AACd,mBAAa,YAAa;AAC1B,YAAO;AAAA,IACV;AAAA,EACT,CAAK;AACD,SAAO,UAAU,GAAG,MAAM,QAAQ,cAAc,CAAA,GAAI,OAAO,IAAI,CAAC,CAAC,CAAC,EAAE,UAAU,YAAY;AAC9F;ACjFO,SAAS,YAAY,oBAAoB,YAAY,WAAW;AAEnE,MAAI;AACJ,MAAI,WAAW;AAIV;AACD,iBAA6E;AAAA,EACrF;AACI,SAAO,MAAM;AAAA,IACT,WAAW,WAAY;AAAE,aAAO,IAAI,cAAc,YAAY,YAAY,SAAS;AAAA,IAAI;AAAA,IACvF,cAAc;AAAA,IACd,iBAAiB;AAAA,IACjB,qBAAqB;AAAA,EAC7B,CAAK;AACL;ACdO,SAAS,IAAI,gBAAgB,OAAO,UAAU;AACjD,MAAI,cAAc,WAAW,cAAc,KAAK,SAAS,WAEjD,EAAE,MAAM,gBAAgB,OAAc,SAAkB,IAC1D;AACN,SAAO,cACD,QAAQ,SAAU,QAAQ,YAAY;AACpC,QAAI;AACJ,KAAC,KAAK,YAAY,eAAe,QAAQ,OAAO,SAAS,SAAS,GAAG,KAAK,WAAW;AACrF,QAAI,UAAU;AACd,WAAO,UAAU,yBAAyB,YAAY,SAAU,OAAO;AACnE,UAAI6C;AACJ,OAACA,MAAK,YAAY,UAAU,QAAQA,QAAO,SAAS,SAASA,IAAG,KAAK,aAAa,KAAK;AACvF,iBAAW,KAAK,KAAK;AAAA,IACrC,GAAe,WAAY;AACX,UAAIA;AACJ,gBAAU;AACV,OAACA,MAAK,YAAY,cAAc,QAAQA,QAAO,SAAS,SAASA,IAAG,KAAK,WAAW;AACpF,iBAAW,SAAU;AAAA,IACxB,GAAE,SAAU,KAAK;AACd,UAAIA;AACJ,gBAAU;AACV,OAACA,MAAK,YAAY,WAAW,QAAQA,QAAO,SAAS,SAASA,IAAG,KAAK,aAAa,GAAG;AACtF,iBAAW,MAAM,GAAG;AAAA,IACpC,GAAe,WAAY;AACX,UAAIA,KAAI;AACR,UAAI,SAAS;AACT,SAACA,MAAK,YAAY,iBAAiB,QAAQA,QAAO,SAAS,SAASA,IAAG,KAAK,WAAW;AAAA,MAC3G;AACgB,OAAC,KAAK,YAAY,cAAc,QAAQ,OAAO,SAAS,SAAS,GAAG,KAAK,WAAW;AAAA,IACpG,CAAa,CAAC;AAAA,EACL,CAAA,IAEG;AACZ;ACtCA,IAAI,IAAI,EAAE,GAAG,MAAM,GAAG,MAAM,GAAG,MAAM,GAAG,MAAM,GAAG,MAAM,GAAG,MAAM,GAAG,OAAO,GAAG,MAAM,GAAG,QAAQ,GAAG,QAAQ,GAAG,QAAQ,GAAG,QAAQ,GAAG,QAAQ,GAAG,QAAQ,GAAG,QAAQ,GAAG,OAAQ,GAAE,IAAI,EAAE,GAAG,MAAM,GAAG,MAAM,GAAG,MAAM,GAAG,MAAO,GAAE,IAAI,IAAI,MAAM,CAAC,EAAE,KAAK,OAAO,cAAc,EAAE,CAAC,CAAC,CAAC,EAAE,KAAK,EAAE;AACnR,SAAS,EAAE5C,IAAG;AACZ,MAAIE,KAAI,KAAK,UAAUF,EAAC;AACxB,SAAO,GAAG,CAAC,GAAG,MAAM,KAAKE,EAAC,EAAE,IAAI,CAACL,OAAM;AACrC,QAAIE,KAAIF,GAAE,WAAW,CAAC;AACtB,QAAIE,KAAI,IAAK,OAAM,IAAI,MAAM,mEAAmEG,EAAC,iBAAiBL,EAAC,KAAKE,EAAC,GAAG;AAC5H,WAAO,MAAM,KAAKA,GAAE,SAAS,CAAC,EAAE,SAAS,GAAG,GAAG,CAAC,EAAE,IAAI,CAACI,OAAM,OAAO,cAAc,EAAEA,EAAC,CAAC,CAAC,EAAE,KAAK,EAAE;AAAA,EACpG,CAAG,EAAE,KAAK,EAAE,CAAC;AACb;AACA,SAAS,EAAEH,IAAG;AACZ,SAAO,CAAC,OAAO,MAAM,OAAOA,EAAC,CAAC,KAAK,SAAS,KAAKA,EAAC,KAAK,CAAC,2DAA2D,KAAKA,EAAC,IAAI,QAAK,CAAC,CAAC,KAAK,MAAMA,EAAC;AAClJ;AACA,SAAS,EAAEA,IAAG;AACZ,MAAI;AACF,QAAI,IAAIA,IAAGA,GAAE,WAAW,GAAG,IAAI,qBAAqB,MAAM;AAAA,EAC9D,QAAU;AACN,WAAO;AAAA,EACX;AACE,SAAO;AACT;AACA,SAAS,EAAEA,IAAGE,IAAGL,KAAI,QAAQ;AAC3B,SAAOA,OAAM,QAAMA,OAAM,WAAW,EAAEG,EAAC,KAAK,EAAEA,EAAC,KAAKA,KAAI,GAAGA,EAAC,GAAG,EAAEE,EAAC,CAAC;AACrE;AACA,OAAO,YAAY,OAAO,QAAQ,CAAC,EAAE,IAAI,CAACF,OAAMA,GAAE,QAAO,CAAE,CAAC;AAC5D,OAAO,YAAY,OAAO,QAAQ,CAAC,EAAE,IAAI,CAACA,OAAMA,GAAE,QAAO,CAAE,CAAC;AAC5D,IAAI,IAAI,GAAG,OAAO,OAAO,CAAC,EAAE,IAAI,CAACA,OAAM,OAAOA,GAAE,SAAS,EAAE,CAAC,GAAG,EAAE,KAAK,EAAE,CAAC,IAAI,IAAI,IAAI,OAAO,IAAI,CAAC,SAAS,IAAI;AAC9G,SAAS,EAAEA,IAAG;AACZ,MAAIE;AACJ,SAAO,EAAE,SAASF,GAAE,QAAQ,GAAG,EAAE,GAAG,WAAWE,KAAIF,GAAE,MAAM,CAAC,MAAM,OAAO,SAASE,GAAE,CAAC,MAAM,GAAI;AACjG;AACA,SAAS,EAAEF,IAAG;AACZ,SAAOA,MAAK,KAAK,MAAM,EAAE,KAAK,UAAUA,EAAC,CAAC,EAAE,OAAO;AACrD;AACA,SAAS,WAAW,QAAQ;AAC1B,SAAO,EAAE,MAAM;AACjB;ACmDA,MAAM,gBAAgB,UAAU,iBAAiB,YAAY,iBAAiB,KAAK,gBAAgB,GAAG,aAAa,GAAG,cAAc,IAAI,iBAAiB,GAAG,cAAc,GAAG,cAAc;AAC3L,SAAS,UAAU,IAAI;AACrB,SAAO,GAAG,WAAW,aAAa;AACpC;AACA,SAAS,YAAY,IAAI;AACvB,SAAO,GAAG,WAAW,cAAc;AACrC;AAIA,SAAS,WAAW,IAAI;AACtB,MAAI,YAAY,EAAE,GAAG;AACnB,UAAM,cAAc,eAAe,EAAE;AACrC,WAAO,gBAAgB;AAAA,EAC3B;AACE,SAAO,UAAU,EAAE,IAAI,KAAK,gBAAgB;AAC9C;AACA,SAAS,aAAa,IAAI,SAAS;AACjC,MAAI,YAAY,YAAY,YAAY;AACtC,UAAM,IAAI,MAAM,4CAA4C;AAC9D,SAAO,GAAG,cAAc,GAAG,OAAO,GAAG,cAAc,GAAG,eAAe,EAAE,CAAC;AAC1E;AACA,SAAS,iBAAiB,IAAI;AAC5B,MAAI,CAAC,YAAY,EAAE,EAAG;AACtB,QAAM,CAAC,gBAAgB,WAAW,GAAG,YAAY,IAAI,GAAG,MAAM,cAAc;AAC5E,SAAO;AACT;AACA,SAAS,eAAe,IAAI;AAC1B,SAAO,YAAY,EAAE,IAAI,GAAG,MAAM,cAAc,EAAE,MAAM,CAAC,EAAE,KAAK,cAAc,IAAI,UAAU,EAAE,IAAI,GAAG,MAAM,cAAc,MAAM,IAAI;AACrI;ACjHA,MAAM,uBAAuB;AAC7B,IAAI,MAAM;AACV,IAAI,WAAW,WAAS;AACtB,MAAI,CAAC,QAAQ,KAAK,SAAS,OAAO;AAChC,WAAO,OAAO,YAAY,QAAQ,oBAAoB;AACtD,WAAO,eAAe,IAAI;AAC1B,iBAAa;AAAA,EACd,WAAU,aAAa,QAAQ,KAAK,QAAQ;AAC3C,WAAO,eAAe,IAAI;AAC1B,iBAAa;AAAA,EACjB;AACE,gBAAc;AAChB;AACA,IAAI,SAAS,WAAS;AACpB,WAAU,SAAS,CAAC;AACpB,SAAO,KAAK,SAAS,aAAa,OAAO,UAAU;AACrD;AACA,IAAI,eAAe,CAAC,UAAU,aAAa,cAAc;AACvD,MAAI,QAAQ,KAAM,KAAK,KAAK,MAAO,SAAS,SAAS,IAAK,CAAC,KAAM;AACjE,MAAI,OAAO,KAAK,KAAM,MAAM,OAAO,cAAe,SAAS,MAAM;AACjE,SAAO,CAAC,OAAO,gBAAgB;AAC7B,QAAI,KAAK;AACT,WAAO,MAAM;AACX,UAAI,QAAQ,UAAU,IAAI;AAC1B,UAAIK,KAAI;AACR,aAAOA,MAAK;AACV,cAAM,SAAS,MAAMA,EAAC,IAAI,IAAI,KAAK;AACnC,YAAI,GAAG,WAAW,KAAM,QAAO;AAAA,MACvC;AAAA,IACA;AAAA,EACA;AACA;AACA,IAAI,iBAAiB,CAAC,UAAU,OAAO,OACrC,aAAa,UAAU,MAAM,MAAM;AC3BrC,MAAM,oBAAoB,MAAM;AAAA,EAC9B;AAAA,EACA,aAAa;AAAA,EACb;AAAA,EACA;AAAA,EACA,YAAY,KAAK;AACf,UAAM,QAAQ,kBAAkB,GAAG;AACnC,UAAM,MAAM,OAAO,GAAG,OAAO,OAAO,MAAM,KAAK;AAAA,EACnD;AACA;AACA,MAAM,oBAAoB,MAAM;AAAA,EAC9B;AAAA,EACA,aAAa;AAAA,EACb;AAAA,EACA;AAAA,EACA,YAAY,KAAK;AACf,UAAM,QAAQ,kBAAkB,GAAG;AACnC,UAAM,MAAM,OAAO,GAAG,OAAO,OAAO,MAAM,KAAK;AAAA,EACnD;AACA;AACA,SAAS,kBAAkB,KAAK;AAC9B,QAAM,OAAO,IAAI,MAAM,QAAQ;AAAA,IAC7B,UAAU;AAAA,IACV,YAAY,IAAI;AAAA,IAChB,cAAc,cAAc,MAAM,GAAG;AAAA,IACrC,SAAS;AAAA,IACT,SAAS;AAAA,EACV;AACD,MAAI,KAAK,SAAS,KAAK;AACrB,WAAO,MAAM,UAAU,GAAG,KAAK,KAAK,MAAM,KAAK,OAAO,IAAI;AAC5D,MAAI,gBAAgB,IAAI,KAAK,cAAc,IAAI,GAAG;AAChD,UAAM,WAAW,KAAK,MAAM,SAAS,CAAA,GAAI,QAAQ,SAAS,MAAM,GAAG,CAAC,EAAE,IAAI,CAAC,SAAS,KAAK,OAAO,WAAW,EAAE,OAAO,OAAO;AAC3H,QAAI,WAAW,MAAM,SAAS;AAAA,IAC9B,MAAM,KAAK;AAAA,GACZ,CAAC,KAAK;AACL,WAAO,SAAS,SAAS,MAAM,YAAY;AAAA,SACtC,SAAS,SAAS,CAAC,UAAU,MAAM,UAAU,GAAG,KAAK,MAAM,WAAW,GAAG,QAAQ,IAAI,MAAM,UAAU,KAAK,OAAO;AAAA,EAC1H;AACE,SAAO,KAAK,SAAS,KAAK,MAAM,eAAe,MAAM,UAAU,KAAK,MAAM,aAAa,MAAM,UAAU,KAAK,OAAO,UAAU,MAAM,UAAU,KAAK,SAAS,KAAK,WAAW,iBAAiB,GAAG,GAAG;AACpM;AACA,SAAS,gBAAgB,MAAM;AAC7B,SAAO,cAAc,IAAI,KAAK,cAAc,KAAK,KAAK,KAAK,KAAK,MAAM,SAAS,mBAAmB,OAAO,KAAK,MAAM,eAAe;AACrI;AACA,SAAS,cAAc,MAAM;AAC3B,SAAO,cAAc,IAAI,KAAK,cAAc,KAAK,KAAK,KAAK,KAAK,MAAM,SAAS,iBAAiB,OAAO,KAAK,MAAM,eAAe;AACnI;AACA,SAAS,cAAc,KAAK;AAC1B,SAAO,OAAO,OAAO,YAAY,QAAQ,QAAQ,CAAC,MAAM,QAAQ,GAAG;AACrE;AACA,SAAS,iBAAiB,KAAK;AAC7B,QAAM,gBAAgB,IAAI,gBAAgB,IAAI,IAAI,aAAa,KAAK;AACpE,SAAO,GAAG,IAAI,MAAM,eAAe,IAAI,GAAG,qBAAqB,IAAI,UAAU,GAAG,aAAa;AAC/F;AACA,SAAS,cAAc,MAAM,KAAK;AAChC,UAAQ,IAAI,QAAQ,cAAc,KAAK,IAAI,YAAW,EAAG,QAAQ,kBAAkB,MAAM,KAAK,KAAK,UAAU,MAAM,MAAM,CAAC,IAAI;AAChI;AACA,MAAM,wBAAwB,MAAM;AAAA,EAClC;AAAA,EACA;AAAA,EACA,YAAY,EAAE,WAAW,cAAc;AACrC,UAAM,iBAAiB,GAAG,KAAK,OAAO,mBAAmB,KAAK,YAAY;AAC1E,UAAM,MAAM,IAAI,IAAI,oCAAoC,UAAU,MAAM;AACxE,QAAI,OAAO,WAAW,KAAK;AACzB,YAAM,EAAE,OAAM,IAAK;AACnB,UAAI,aAAa,IAAI,QAAQ,KAAK,GAAG,IAAI,aAAa,IAAI,UAAU,MAAM,GAAG,KAAK,eAAe,KAAK,KAAK,UAAU,sFAAsF,GAAG;AAAA,IAC/M;AACC,WAAK,UAAU,yGAAyG,GAAG;AAAA,EACjI;AACA;AACA,MAAM,YAAY;AAAA,EAChB,YAAY,CAAC,QAAQ;AACnB,QAAI,IAAI,cAAc;AACpB,YAAM,IAAI,YAAY,GAAG;AAC3B,QAAI,IAAI,cAAc;AACpB,YAAM,IAAI,YAAY,GAAG;AAC3B,WAAO;AAAA,EACX;AACA;AACA,SAAS,gBAAgB;AACvB,QAAM,OAAO,CAAE;AACf,SAAO;AAAA,IACL,YAAY,CAAC,QAAQ;AACnB,YAAM,OAAO,IAAI,QAAQ,kBAAkB,GAAG,WAAW,MAAM,QAAQ,IAAI,IAAI,OAAO,CAAC,IAAI;AAC3F,iBAAW,OAAO;AAChB,SAAC,OAAO,KAAK,GAAG,MAAM,KAAK,GAAG,IAAI,MAAI,QAAQ,KAAK,GAAG;AACxD,aAAO;AAAA,IACb;AAAA,EACG;AACH;AACA,SAAS,kBAAkB,gBAAgB;AACzC,SAAOwC,IAAM;AAAA,IACXC,EAAM,EAAE,aAAa;AAAA,IACrB,GAAG;AAAA,IACH,cAAe;AAAA,IACfC,EAAa;AAAA,IACbC,IAAc;AAAA,IACdC,IAAU;AAAA,IACV;AAAA,IACAvB,EAAW,EAAE,gBAAgB,WAAY,CAAA;AAAA,EAC7C,CAAG;AACH;AACA,SAAS,YAAY,KAAK,SAAS,SAAS;AAC1C,MAAI,QAAQ,eAAe,EAAG,QAAO;AACrC,QAAM,SAAS,QAAQ,WAAW,SAAS,QAAQ,WAAW,QAAQ,YAAY,QAAQ,OAAO,QAAQ,KAAK,WAAW,aAAa,GAAG,sBAAsB,IAAI,aAAa,IAAI,SAAS,eAAe,OAAO,IAAI,SAAS,eAAe,OAAO,IAAI,SAAS,eAAe;AAClR,UAAQ,UAAU,aAAa,sBAAsB,OAAKoB,EAAM,YAAY,KAAK,SAAS,OAAO;AACnG;AACA,MAAM,WAAW;AACjB,SAAS,gBAAgB,MAAM;AAC7B,SAAO,WAAW;AACpB;AACA,MAAM,oBAAoB,CAAC,SAAS,MAAM,GAAG,yBAAyB,CAAC,UAAU,SAAS,SAAS,GAAG,UAAU,CAAC,SAAS;AACxH,MAAI,CAAC,qDAAqD,KAAK,IAAI;AACjE,UAAM,IAAI;AAAA,MACR;AAAA,IACD;AACL,GAAG,YAAY,CAAC,OAAO;AACrB,MAAI,CAAC,gBAAgB,KAAK,EAAE;AAC1B,UAAM,IAAI,MAAM,uDAAuD;AAC3E,GAAG,oBAAoB,CAAC,SAAS;AAC/B,MAAI,kBAAkB,QAAQ,IAAI,MAAM;AACtC,UAAM,IAAI,MAAM,uBAAuB,IAAI,oBAAoB,kBAAkB,KAAK,IAAI,CAAC,EAAE;AACjG,GAAG,iBAAiB,CAAC,IAAI,QAAQ;AAC/B,MAAI,QAAQ,QAAQ,OAAO,OAAO,YAAY,MAAM,QAAQ,GAAG;AAC7D,UAAM,IAAI,MAAM,GAAG,EAAE,kCAAkC;AAC3D,GAAG,qBAAqB,CAAC,IAAI,OAAO;AAClC,MAAI,OAAO,MAAM,YAAY,CAAC,iCAAiC,KAAK,EAAE,KAAK,GAAG,SAAS,IAAI;AACzF,UAAM,IAAI,MAAM,GAAG,EAAE,QAAQ,EAAE,8BAA8B;AACjE,GAAG,oBAAoB,CAAC,IAAI,QAAQ;AAClC,MAAI,CAAC,IAAI;AACP,UAAM,IAAI,MAAM,GAAG,EAAE,+DAA+D;AACtF,qBAAmB,IAAI,IAAI,GAAG;AAChC,GAAG,uBAAuB,CAAC,IAAI,SAAS;AACtC,MAAI,OAAO,QAAQ;AACjB,UAAM,IAAI,MAAM,KAAK,EAAE,WAAW,IAAI,iCAAiC;AAC3E,GAAG,sBAAsB,CAAC,IAAI,QAAQ;AACpC,MAAI,CAAC,IAAI;AACP,UAAM,IAAI,MAAM,KAAK,EAAE,sEAAsE;AAC/F,uBAAqB,IAAI,IAAI,KAAK;AACpC,GAAG,yBAAyB,CAAC,gBAAgBI,cAAa;AACxD,MAAIA,UAAS,OAAOA,UAAS,QAAQ;AACnC,UAAM,IAAI;AAAA,MACR,+BAA+BA,UAAS,GAAG,kDAAkD,cAAc;AAAA,IAC5G;AACL,GAAG,iBAAiB,CAAC,IAAI,UAAU,UAAU;AAC3C,QAAM,YAAY;AAClB,MAAI,uBAAuB,QAAQ,EAAE,MAAM,IAAI;AAC7C,UAAM,QAAQ,uBAAuB,IAAI,CAAC,QAAQ,IAAI,GAAG,GAAG,EAAE,KAAK,IAAI;AACvE,UAAM,IAAI,MAAM,GAAG,SAAS,4CAA4C,KAAK,EAAE;AAAA,EACnF;AACE,MAAI,OAAO,YAAY;AACrB,UAAM,IAAI,MAAM,GAAG,SAAS,qDAAqD;AACnF,MAAI,CAAC,MAAM,QAAQ,KAAK;AACtB,UAAM,IAAI,MAAM,GAAG,SAAS,mDAAmD;AACnF,GAAG,aAAa,CAACT,YAAW;AAC1B,MAAI,CAACA,QAAO;AACV,UAAM,IAAI,MAAM,+CAA+C;AACjE,SAAOA,QAAO,WAAW;AAC3B,GAAG,aAAa,CAAC,QAAQ;AACvB,MAAI,OAAO,OAAO,YAAY,CAAC,uBAAuB,KAAK,GAAG;AAC5D,UAAM,IAAI;AAAA,MACR;AAAA,IACD;AACH,SAAO;AACT,GAAG,iBAAiB,CAACA,YAAW;AAC9B,MAAI,CAACA,QAAO,wBAAwB;AAClC,UAAM,IAAI,MAAM,yDAAyD;AAC3E,QAAM,EAAE,MAAM,OAAOA,QAAO,wBAAwB;AACpD,UAAQ,MAAI;AAAA,IACV,KAAK,WAAW;AACd,UAAI,GAAG,MAAM,GAAG,EAAE,WAAW;AAC3B,cAAM,IAAI,MAAM,6DAA6D;AAC/E;AAAA,IACN;AAAA,IACI,KAAK;AAAA,IACL,KAAK;AAAA,IACL,KAAK;AACH;AAAA,IACF;AACE,YAAM,IAAI,MAAM,8BAA8B,KAAK,SAAU,CAAA,EAAE;AAAA,EACrE;AACA,GAAG,gBAAgB,CAAC,SAASA,YAAW;AACtC,MAAIA,QAAO,wBAAwB;AACjC,UAAM,IAAI,MAAM,KAAK,OAAO,+CAA+C;AAC/E;AACA,SAAS,KAAK,IAAI;AAChB,MAAI,UAAU,OAAI;AAClB,SAAO,IAAI,UAAU,YAAY,cAAc,GAAG,GAAG,IAAI,GAAG,UAAU,OAAK;AAC7E;AACK,MAAC,uBAAuB,CAAC;AAAA;AAAA,EAE5B,KAAK,IAAI,SAAS,QAAQ,KAAK,QAAQ,KAAK,GAAG,GAAG,GAAG,IAAI,CAAC;AAAA,GACzD,oCAAoC,qBAAqB;AAAA,EAC1D;AAAA,EACA;AACF,CAAC,GAAG,kBAAkB,qBAAqB;AAAA,EACzC;AAAA,EACA;AAAA,EACA;AACF,CAAC,GAAG,+BAA+B,qBAAqB;AAAA,EACtD;AAAA,EACA;AACF,CAAC,GAAG,uCAAuC,qBAAqB;AAAA,EAC9D;AACF,CAAC,GAAG,2BAA2B,qBAAqB;AAAA,EAClD;AAAA,EACA,OAAO;AAAA,IACL;AAAA,EACJ,CAAG;AACH,CAAC,GAAG,gCAAgC,qBAAqB;AAAA,EACvD;AAAA,EACA;AACF,CAAC,GAAG,oCAAoC,qBAAqB;AAAA,EAC3D;AAAA,EACA,OAAO,gBAAgB,uBAAuB,CAAC;AACjD,CAAC,GAEG,iBAAiB,oBAAoB,gBAAgB;AAAA,EACvD,SAAS;AAAA,EACT,YAAY;AAAA,EACZ,oBAAoB;AAAA,EACpB,OAAO,EAAE,SAAS,MAAE;AACtB,GAAG,aAAa,CAAC,aAAa,aAAa,SAAS,GAAG,UAAU,CAAC,SAAS,WAAW,QAAQ,IAAI,MAAM;AACxG,SAAS,mBAAmB,YAAY;AACtC,MAAI,eAAe,OAAO,eAAe;AACvC;AACF,QAAM,UAAU,IAAI,KAAK,UAAU;AACnC,MAAI,EAAE,sBAAsB,KAAK,UAAU,KAAK,mBAAmB,QAAQ,QAAQ,QAAS,IAAG;AAC7F,UAAM,IAAI,MAAM,yEAAyE;AAC7F;AACA,SAAS,uBAAuB,aAAa;AAC3C,MAAI,MAAM,QAAQ,WAAW,KAAK,YAAY,SAAS,KAAK,YAAY,SAAS,KAAK;AACpF,UAAM,IAAI;AAAA,MACR;AAAA,IACD;AACL;AACA,MAAM,aAAa,CAACA,SAAQ,eAAe;AACzC,QAAM,kBAAkB;AAAA,IACtB,GAAG;AAAA,IACH,GAAGA;AAAA,IACH,OAAO;AAAA,MACL,GAAG,OAAO,WAAW,SAAS,YAAY,EAAE,SAAS,WAAW,UAAU,WAAW,SAAS,cAAc;AAAA,MAC5G,GAAG,OAAOA,QAAO,SAAS,YAAY,EAAE,SAASA,QAAO,MAAK,IAAKA,QAAO,SAAS,CAAA;AAAA,IACxF;AAAA,EACG;AACD,kBAAgB,cAAc,kCAAmC;AACjE,QAAM,YAAY;AAAA,IAChB,GAAG;AAAA,IACH,GAAG;AAAA,EACJ,GAAE,eAAe,UAAU,sBAAsB,CAAC,UAAU,wBAAwB;AACrF,MAAI,OAAO,UAAU,KAAK;AACxB,UAAM,UAAU,gBAAgB,4BAA4B;AAC5D,UAAM,IAAI,MAAM,iEAAiE,OAAO,EAAE;AAAA,EAC9F;AACE,MAAI,gBAAgB,CAAC,UAAU;AAC7B,UAAM,IAAI,MAAM,wCAAwC;AAC1D,MAAI,UAAU,wBAAwB,KAAK,eAAe,SAAS,GAAG,OAAO,UAAU,cAAc,OAAO,uBAAuB,UAAU,WAAW,GAAG,qBAAqB;AAC9K,UAAM,IAAI;AAAA,MACR;AAAA,IACD;AACH,MAAI,2BAA2B;AAC7B,UAAM,IAAI;AAAA,MACR;AAAA,IACD;AACH,MAAI,OAAO,UAAU,MAAM,WAAW;AACpC,UAAM,IAAI,MAAM,6CAA6C,UAAU,MAAM,OAAO,EAAE;AACxF,MAAI,UAAU,MAAM,WAAW,UAAU,MAAM,cAAc;AAC3D,UAAM,IAAI,MAAM,4DAA4D;AAC9E,MAAI,UAAU,MAAM,WAAW,OAAO,UAAU,MAAM,aAAa,YAAY,OAAO,UAAU,MAAM,aAAa;AACjH,UAAM,IAAI;AAAA,MACR,4DAA4D,UAAU,MAAM,SAAS;AAAA,IACtF;AACH,QAAM,YAAY,OAAO,SAAS,OAAO,OAAO,YAAY,OAAO,SAAS,UAAU,cAAc,aAAa,QAAQ,OAAO,SAAS,QAAQ,GAAG,WAAW,CAAC,CAAC,UAAU;AAC3K,YAAU,mBAAmB,aAAa,8BAA+B,GAAE,UAAU,kBAAkB,QAAK,aAAa,eAAe,YAAY,UAAU,8BAA8B,OAAK,6BAA6B,OAAO,UAAU,SAAS,OAAO,mBAAmB,gBAAgB,UAAU,UAAU,SAAS,GAAG,UAAU,WAAW,QAAQ,UAAU,OAAO,GAAG,sBAAsB,cAAc,UAAU,mBAAmB,UAAU,mBAAmB,WAAW,UAAU,gBAAgB,EAAE,QAAQ,QAAQ,EAAE,IAAI,SAAS,UAAU,aAAa,GAAG,UAAU,UAAU,GAAG,QAAQ,MAAM,EAAE,GAAG,UAAU,eAAe,UAAU,YAAY,cAAc,SAAS,UAAU,WAAW,QAAM,UAAU,mBAAmB,kCAAiC,GAAI,UAAU,SAAS,UAAU,WAAW,SAAM,CAAC,UAAU,iBAAiB,mBAAmB,UAAU,UAAU;AAC/2B,QAAM,YAAY,UAAU,QAAQ,MAAM,OAAO,CAAC,GAAG,WAAW,UAAU,CAAC,GAAG,OAAO,UAAU,CAAC,GAAG,UAAU,UAAU,eAAe,iBAAiB;AACvJ,SAAO,gBAAgB,UAAU,MAAM,GAAG,QAAQ,MAAM,UAAU,SAAS,IAAI,IAAI,KAAK,UAAU,UAAU,IAAI,UAAU,SAAS,GAAG,QAAQ,MAAM,UAAU,SAAS,IAAI,OAAO,KAAK,UAAU,UAAU,OAAO,UAAU,MAAM,GAAG,UAAU,OAAO,KAAK,UAAU,UAAU,IAAI,UAAU,SAAS,UAAU,MAAM;AACxT;AACA,MAAM,8BAA8B,MAAM;AAAA,EACxC,OAAO;AACT;AACA,MAAM,wBAAwB,MAAM;AAAA,EAClC,OAAO;AAAA,EACP;AAAA,EACA,YAAY,SAAS,QAAQ,UAAU,CAAA,GAAI;AACzC,UAAM,SAAS,OAAO,GAAG,KAAK,SAAS;AAAA,EAC3C;AACA;AACA,MAAM,qBAAqB,MAAM;AAAA,EAC/B,OAAO;AAAA,EACP;AAAA,EACA,YAAY,SAAS,MAAM;AACzB,UAAM,OAAO,GAAG,KAAK,OAAO;AAAA,EAChC;AACA;AACA,MAAM,qBAAqB,MAAM;AAAA,EAC/B,OAAO;AAAA,EACP;AAAA,EACA,YAAY,SAAS,MAAM,UAAU,CAAA,GAAI;AACvC,UAAM,SAAS,OAAO,GAAG,KAAK,OAAO;AAAA,EACzC;AACA;AACA,MAAM,0BAA0B,MAAM;AAAA,EACpC,OAAO;AACT;AACA,MAAM,kBAAkB,CAAC,gBAAgB,YAAY;AACrD,SAAS,mBAAmB,iBAAiB,QAAQ;AACnD,SAAO,MAAM,MAAM;AACjB,UAAM,KAAK,gBAAiB;AAC5B,WAAO,aAAa,EAAE,IAAI,KAAK,GAAG,EAAE;AAAA,EACxC,CAAG,EAAE,KAAK,SAAS,CAAC,OAAO,sBAAsB,IAAI,MAAM,CAAC,CAAC;AAC7D;AACA,SAAS,sBAAsB,IAAI,QAAQ;AACzC,SAAO,IAAI,WAAW,CAAC,aAAa;AAClC,UAAM,WAAW,OAAO,SAAS,MAAM,GAAG,gBAAgB,OAAO,SAAS,WAAW;AACrF,aAAS,QAAQ,KAAK;AACpB,UAAI,UAAU,KAAK;AACjB,cAAM,CAAC,YAAY,KAAK,IAAI,WAAW,GAAG;AAC1C,iBAAS;AAAA,UACP,aAAa,IAAI,kBAAkB,6CAA6C,EAAE,OAAO,MAAK,CAAE,IAAI,IAAI,cAAc,OAAO,MAAM,SAAS,KAAK;AAAA,QAClJ;AACD;AAAA,MACR;AACM,SAAG,eAAe,GAAG,SAAS,SAAS,MAAM,IAAI,sBAAsB,+BAA+B,CAAC,IAAI,iBAAiB,SAAS,KAAK,EAAE,MAAM,aAAa;AAAA,IACrK;AACI,aAAS,SAAS;AAChB,eAAS,KAAK,EAAE,MAAM,OAAM,CAAE;AAAA,IACpC;AACI,aAAS,UAAU,SAAS;AAC1B,YAAM,CAAC,YAAY,KAAK,IAAI,WAAW,OAAO;AAC9C,UAAI,YAAY;AACd,iBAAS;AAAA,UACP,IAAI,kBAAkB,uCAAuC,EAAE,OAAO,WAAY,CAAA;AAAA,QACnF;AACD;AAAA,MACR;AACM,UAAI,QAAQ,SAAS,gBAAgB;AACnC,iBAAS,MAAM,IAAI,aAAa,oBAAoB,OAAO,IAAI,GAAG,MAAM,IAAI,CAAC;AAC7E;AAAA,MACR;AACM,UAAI,QAAQ,SAAS,cAAc;AACjC,iBAAS;AAAA,UACP,IAAI;AAAA,YACF,+BAA+B,MAAM,MAAM,UAAU,eAAe;AAAA,UAChF;AAAA,QACS;AACD;AAAA,MACR;AACM,eAAS,KAAK;AAAA,QACZ,MAAM,QAAQ;AAAA,QACd,IAAI,QAAQ;AAAA,QACZ,GAAG,MAAM,OAAO,EAAE,MAAM,MAAM,KAAI,IAAK,CAAA;AAAA,MAC/C,CAAO;AAAA,IACP;AACI,OAAG,iBAAiB,SAAS,OAAO,GAAG,YAAY,GAAG,iBAAiB,QAAQ,MAAM;AACrF,UAAM,gBAAgB,CAAC,GAAmB,oBAAI,IAAI,CAAC,GAAG,iBAAiB,GAAG,MAAM,CAAC,CAAC,EAAE,OAAO,CAAC,SAAS,SAAS,WAAW,SAAS,UAAU,SAAS,WAAW;AAChK,WAAO,cAAc,QAAQ,CAAC,SAAS,GAAG,iBAAiB,MAAM,SAAS,CAAC,GAAG,MAAM;AAClF,SAAG,oBAAoB,SAAS,OAAO,GAAG,YAAY,GAAG,oBAAoB,QAAQ,MAAM,GAAG,cAAc,QAAQ,CAAC,SAAS,GAAG,oBAAoB,MAAM,SAAS,CAAC,GAAG,GAAG,MAAO;AAAA,IACnL;AAAA,EACL,CAAG;AACH;AACA,SAAS,WAAW,SAAS;AAC3B,MAAI;AACF,UAAM,OAAO,OAAO,QAAQ,QAAQ,YAAY,KAAK,MAAM,QAAQ,IAAI;AACvE,WAAO;AAAA,MACL;AAAA,MACA;AAAA,QACE,MAAM,QAAQ;AAAA,QACd,IAAI,QAAQ;AAAA,QACZ,GAAG,cAAc,IAAI,IAAI,CAAA,IAAK,EAAE,KAAI;AAAA,MAC5C;AAAA,IACK;AAAA,EACF,SAAQ,KAAK;AACZ,WAAO,CAAC,KAAK,IAAI;AAAA,EACrB;AACA;AACA,SAAS,oBAAoB,KAAK;AAChC,SAAO,IAAI,QAAQ,IAAI,MAAM,cAAc,IAAI,MAAM,cAAc,OAAO,IAAI,SAAS,WAAW,IAAI,QAAQ,KAAK,UAAU,IAAI,OAAO,MAAM,CAAC,IAAI,IAAI,WAAW;AACpK;AACA,SAAS,cAAc,MAAM;AAC3B,aAAW3B,MAAK;AACd,WAAO;AACT,SAAO;AACT;AACA,SAAS,aAAa,KAAK;AACzB,MAAI,OAAO,OAAO;AAChB,WAAO,EAAE,IAAI,IAAK;AACpB,MAAI,MAAM,QAAQ,GAAG;AACnB,WAAO,EAAE,OAAO,kBAAkB,QAAQ,EAAE,KAAK,MAAO;AAC1D,MAAI,OAAO,OAAO,YAAY,QAAQ,QAAQ,WAAW,OAAO,OAAO,IAAI,SAAS;AAClF,WAAO,YAAY,OAAO,OAAO,IAAI,UAAU,YAAY,IAAI,WAAW,OAAO,EAAE,OAAO,IAAI,OAAO,QAAQ,IAAI,OAAM,IAAK,EAAE,OAAO,IAAI,MAAO;AAClJ,QAAM,gBAAgB;AAAA,IACpB;AAAA,IACA;AAAA,IACA;AAAA,EACD,EAAC,KAAK;AAAA,CACR;AACC,QAAM,IAAI,MAAM;AAAA;AAAA,EAEhB,aAAa,EAAE;AACjB;AACA,MAAM,UAAU;AAAA,EACd;AAAA,EACA;AAAA,EACA,YAAY,WAAW,aAAa,IAAI;AACtC,SAAK,YAAY,WAAW,KAAK,aAAa;AAAA,EAClD;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAOE,IAAI,OAAO;AACT,WAAO,KAAK,QAAQ,OAAO,KAAK;AAAA,EACpC;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAOE,aAAa,OAAO;AAClB,WAAO,KAAK,QAAQ,gBAAgB,KAAK;AAAA,EAC7C;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAOE,eAAe,OAAO;AACpB,WAAO,eAAe,kBAAkB,KAAK,GAAG,KAAK,QAAQ,kBAAkB,KAAK;AAAA,EACxF;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAOE,MAAM,OAAO;AACX,QAAI,CAAC,MAAM,QAAQ,KAAK;AACtB,YAAM,IAAI,MAAM,qEAAqE;AACvF,WAAO,KAAK,aAAa,OAAO,OAAO,CAAE,GAAE,KAAK,YAAY,EAAE,OAAO,MAAK,CAAE,GAAG;AAAA,EACnF;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAME,IAAI,OAAO;AACT,WAAO,KAAK,QAAQ,OAAO,KAAK;AAAA,EACpC;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAME,IAAI,OAAO;AACT,WAAO,KAAK,QAAQ,OAAO,KAAK;AAAA,EACpC;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAQE,OAAO,IAAI,UAAU,OAAO;AAC1B,WAAO,eAAe,IAAI,UAAU,KAAK,GAAG,KAAK,QAAQ,UAAU,EAAE,CAAC,EAAE,GAAG,UAAU,MAAK,CAAE;AAAA,EAChG;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAOE,OAAO,UAAU,OAAO;AACtB,WAAO,KAAK,OAAO,SAAS,GAAG,QAAQ,QAAQ,KAAK;AAAA,EACxD;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAOE,QAAQ,UAAU,OAAO;AACvB,WAAO,KAAK,OAAO,UAAU,GAAG,QAAQ,OAAO,KAAK;AAAA,EACxD;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EASE,OAAO,UAAU,OAAO,aAAa,OAAO;AAC1C,UAAM,SAAS,OAAO,cAAc,OAAO,gBAAgB,IAAI,aAAa,QAAQ,IAAI,QAAQ,IAAI,OAAO,WAAW,SAAS,KAAK,KAAK,IAAI,GAAG,QAAQ,WAAW,GAAG,WAAW,aAAa,KAAK,YAAY,IAAI,KAAK,UAAU,gBAAgB,GAAG,QAAQ,IAAI,UAAU,IAAI,QAAQ;AACvR,WAAO,KAAK,OAAO,WAAW,eAAe,SAAS,CAAA,CAAE;AAAA,EAC5D;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAME,aAAa,KAAK;AAChB,WAAO,KAAK,WAAW,eAAe,KAAK;AAAA,EAC/C;AAAA;AAAA;AAAA;AAAA,EAIE,YAAY;AACV,WAAO,EAAE,GAAG,aAAa,KAAK,SAAS,GAAG,GAAG,KAAK,WAAY;AAAA,EAClE;AAAA;AAAA;AAAA;AAAA,EAIE,SAAS;AACP,WAAO,KAAK,UAAW;AAAA,EAC3B;AAAA;AAAA;AAAA;AAAA,EAIE,QAAQ;AACN,WAAO,KAAK,aAAa,CAAA,GAAI;AAAA,EACjC;AAAA,EACE,QAAQ,IAAI,OAAO,SAAS,MAAI;AAC9B,WAAO,eAAe,IAAI,KAAK,GAAG,KAAK,aAAa,OAAO,OAAO,IAAI,KAAK,YAAY;AAAA,MACrF,CAAC,EAAE,GAAG,OAAO,OAAO,IAAI,UAAU,KAAK,WAAW,EAAE,KAAK,CAAA,GAAI,KAAK;AAAA,IACnE,CAAA,GAAG;AAAA,EACR;AAAA,EACE,KAAK,IAAI,OAAO;AACd,WAAO,KAAK,QAAQ,IAAI,OAAO,KAAE;AAAA,EACrC;AACA;AACA,MAAM,wBAAwB,UAAU;AAAA,EACtC;AAAA,EACA,YAAY,WAAW,YAAYqC,SAAQ;AACzC,UAAM,WAAW,UAAU,GAAG,KAAK,UAAUA;AAAA,EACjD;AAAA;AAAA;AAAA;AAAA,EAIE,QAAQ;AACN,WAAO,IAAI,gBAAgB,KAAK,WAAW,EAAE,GAAG,KAAK,WAAU,GAAI,KAAK,OAAO;AAAA,EACnF;AAAA,EACE,OAAO,SAAS;AACd,QAAI,CAAC,KAAK;AACR,YAAM,IAAI;AAAA,QACR;AAAA,MACD;AACH,UAAM,cAAc,OAAO,KAAK,aAAa,UAAU,OAAO,OAAO,OAAO,EAAE,aAAa,iBAAiB,KAAE,GAAI,OAAO;AACzH,WAAO,KAAK,QAAQ,OAAO,EAAE,OAAO,KAAK,YAAa,GAAE,IAAI;AAAA,EAChE;AACA;AACA,MAAM,cAAc,UAAU;AAAA,EAC5B;AAAA,EACA,YAAY,WAAW,YAAYA,SAAQ;AACzC,UAAM,WAAW,UAAU,GAAG,KAAK,UAAUA;AAAA,EACjD;AAAA;AAAA;AAAA;AAAA,EAIE,QAAQ;AACN,WAAO,IAAI,MAAM,KAAK,WAAW,EAAE,GAAG,KAAK,WAAU,GAAI,KAAK,OAAO;AAAA,EACzE;AAAA,EACE,OAAO,SAAS;AACd,QAAI,CAAC,KAAK;AACR,YAAM,IAAI;AAAA,QACR;AAAA,MACD;AACH,UAAM,cAAc,OAAO,KAAK,aAAa,UAAU,OAAO,OAAO,OAAO,EAAE,aAAa,iBAAiB,KAAE,GAAI,OAAO;AACzH,WAAO,KAAK,QAAQ,OAAO,EAAE,OAAO,KAAK,YAAa,GAAE,IAAI;AAAA,EAChE;AACA;AACA,MAAM,uBAAuB,EAAE,iBAAiB,MAAI;AACpD,MAAM,gBAAgB;AAAA,EACpB;AAAA,EACA;AAAA,EACA,YAAY,aAAa,CAAE,GAAE,eAAe;AAC1C,SAAK,aAAa,YAAY,KAAK,QAAQ;AAAA,EAC/C;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAOE,OAAO,KAAK;AACV,WAAO,eAAe,UAAU,GAAG,GAAG,KAAK,KAAK,EAAE,QAAQ,KAAK;AAAA,EACnE;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAOE,kBAAkB,KAAK;AACrB,UAAM,KAAK;AACX,WAAO,eAAe,IAAI,GAAG,GAAG,kBAAkB,IAAI,GAAG,GAAG,KAAK,KAAK,EAAE,CAAC,EAAE,GAAG,IAAG,CAAE;AAAA,EACvF;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAOE,gBAAgB,KAAK;AACnB,UAAM,KAAK;AACX,WAAO,eAAe,IAAI,GAAG,GAAG,kBAAkB,IAAI,GAAG,GAAG,KAAK,KAAK,EAAE,CAAC,EAAE,GAAG,IAAG,CAAE;AAAA,EACvF;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAOE,OAAO,YAAY;AACjB,WAAO,mBAAmB,UAAU,UAAU,GAAG,KAAK,KAAK,EAAE,QAAQ,EAAE,IAAI,WAAY,EAAA,CAAE;AAAA,EAC7F;AAAA,EACE,cAAc,IAAI;AAChB,WAAO,MAAM,KAAK,QAAQ,IAAI,QAAQ,KAAK;AAAA,EAC/C;AAAA;AAAA;AAAA;AAAA,EAIE,YAAY;AACV,WAAO,CAAC,GAAG,KAAK,UAAU;AAAA,EAC9B;AAAA;AAAA;AAAA;AAAA,EAIE,SAAS;AACP,WAAO,KAAK,UAAW;AAAA,EAC3B;AAAA;AAAA;AAAA;AAAA,EAIE,QAAQ;AACN,WAAO,KAAK,aAAa,CAAA,GAAI;AAAA,EACjC;AAAA,EACE,KAAK,KAAK;AACR,WAAO,KAAK,WAAW,KAAK,GAAG,GAAG;AAAA,EACtC;AACA;AACA,MAAM,oBAAoB,gBAAgB;AAAA,EACxC;AAAA,EACA,YAAY,YAAYA,SAAQ,eAAe;AAC7C,UAAM,YAAY,aAAa,GAAG,KAAK,UAAUA;AAAA,EACrD;AAAA;AAAA;AAAA;AAAA,EAIE,QAAQ;AACN,WAAO,IAAI,YAAY,CAAC,GAAG,KAAK,UAAU,GAAG,KAAK,SAAS,KAAK,KAAK;AAAA,EACzE;AAAA,EACE,OAAO,SAAS;AACd,QAAI,CAAC,KAAK;AACR,YAAM,IAAI;AAAA,QACR;AAAA,MACD;AACH,WAAO,KAAK,QAAQ;AAAA,MAClB,KAAK,UAAW;AAAA,MAChB,OAAO,OAAO,EAAE,eAAe,KAAK,SAAS,sBAAsB,WAAW,CAAE,CAAA;AAAA,IACjF;AAAA,EACL;AAAA,EACE,MAAM,mBAAmB,UAAU;AACjC,UAAM,YAAY,OAAO,YAAY,YAAY,UAAU,OAAO,qBAAqB,YAAY,6BAA6B,OAAO,sBAAsB,OAAO,qBAAqB,aAAa,WAAW,qBAAqB,QAAQ;AAC9O,QAAI;AACF,aAAO,KAAK,KAAK,EAAE,OAAO,kBAAkB,UAAS,GAAI;AAC3D,QAAI,WAAW;AACb,YAAM,QAAQ,SAAS,IAAI,MAAM,mBAAmB,IAAI,KAAK,OAAO,CAAC;AACrE,UAAI,EAAE,iBAAiB;AACrB,cAAM,IAAI,MAAM,oDAAoD;AACtE,aAAO,KAAK,KAAK,EAAE,OAAO,MAAM,UAAS,GAAI;AAAA,IACnD;AACI,QAAI,qBAAqB;AACvB,YAAM,QAAQ,IAAI,MAAM,mBAAmB,YAAY,CAAE,GAAE,KAAK,OAAO;AACvE,aAAO,KAAK,KAAK,EAAE,OAAO,MAAM,UAAS,GAAI;AAAA,IACnD;AACI,WAAO,KAAK,KAAK,EAAE,OAAO,EAAE,IAAI,mBAAmB,GAAG,SAAQ,GAAI;AAAA,EACtE;AACA;AACA,MAAM,8BAA8B,gBAAgB;AAAA,EAClD;AAAA,EACA,YAAY,YAAYA,SAAQ,eAAe;AAC7C,UAAM,YAAY,aAAa,GAAG,KAAK,UAAUA;AAAA,EACrD;AAAA;AAAA;AAAA;AAAA,EAIE,QAAQ;AACN,WAAO,IAAI,sBAAsB,CAAC,GAAG,KAAK,UAAU,GAAG,KAAK,SAAS,KAAK,KAAK;AAAA,EACnF;AAAA,EACE,OAAO,SAAS;AACd,QAAI,CAAC,KAAK;AACR,YAAM,IAAI;AAAA,QACR;AAAA,MACD;AACH,WAAO,KAAK,QAAQ;AAAA,MAClB,KAAK,UAAW;AAAA,MAChB,OAAO,OAAO,EAAE,eAAe,KAAK,SAAS,sBAAsB,WAAW,CAAE,CAAA;AAAA,IACjF;AAAA,EACL;AAAA,EACE,MAAM,mBAAmB,UAAU;AACjC,UAAM,YAAY,OAAO,YAAY;AACrC,QAAI,OAAO,qBAAqB,YAAY,6BAA6B;AACvE,aAAO,KAAK,KAAK,EAAE,OAAO,kBAAkB,UAAS,GAAI;AAC3D,QAAI,WAAW;AACb,YAAM,QAAQ,SAAS,IAAI,gBAAgB,mBAAmB,IAAI,KAAK,OAAO,CAAC;AAC/E,UAAI,EAAE,iBAAiB;AACrB,cAAM,IAAI,MAAM,oDAAoD;AACtE,aAAO,KAAK,KAAK,EAAE,OAAO,MAAM,UAAS,GAAI;AAAA,IACnD;AACI,WAAO,KAAK,KAAK,EAAE,OAAO,EAAE,IAAI,mBAAmB,GAAG,SAAQ,GAAI;AAAA,EACtE;AACA;AACA,MAAM,gBAAgB;AACtB,SAAS,eAAeV,SAAQ,YAAY,IAAI;AAC9C,QAAM,UAAU,CAAE;AAClB,EAAAA,QAAO,WAAW,OAAO,OAAO,SAASA,QAAO,OAAO;AACvD,QAAMW,SAAQ,UAAU,SAASX,QAAO;AACxC,EAAAW,WAAU,QAAQ,gBAAgB,UAAUA,MAAK,KAAK,CAAC,UAAU,gBAAgB,CAACX,QAAO,sBAAsBA,QAAO,cAAc,QAAQ,aAAa,IAAIA,QAAO;AACpK,QAAM,kBAAkB,CAAC,EAAE,OAAO,UAAU,kBAAkB,MAAMA,QAAO,kBAAkB,UAAU,kBAAkB,UAAU,OAAO,UAAU,UAAU,MAAMA,QAAO,UAAU,UAAU;AAC/L,SAAO,OAAO,OAAO,CAAE,GAAE,WAAW;AAAA,IAClC,SAAS,OAAO,OAAO,CAAA,GAAI,SAAS,UAAU,WAAW,EAAE;AAAA,IAC3D,SAAS,OAAO,UAAU,MAAM,IAAI,KAAK,MAAM;AAAA,IAC/C,OAAO,UAAU,SAASA,QAAO;AAAA,IACjC,MAAM;AAAA,IACN;AAAA,IACA,OAAO,OAAO,UAAU,SAAS,YAAY,OAAOA,QAAO,SAAS,WAAW,EAAE,GAAGA,QAAO,OAAO,GAAG,UAAU,MAAK,IAAK,UAAU,SAASA,QAAO;AAAA,EACvJ,CAAG;AACH;AACA,MAAM,oBAAoB,CAAC;AAAA,EACzB;AAAA,EACA,SAAS,CAAE;AAAA,EACX,UAAU,CAAA;AACZ,MAAM;AACJ,QAAM,eAAe,IAAI,gBAAe,GAAI,EAAE,KAAK,kBAAkB,aAAa,GAAG,KAAI,IAAK;AAC9F,SAAO,aAAa,OAAO,OAAO,GAAG,GAAG,aAAa,OAAO,SAAS,KAAK;AAC1E,aAAW,CAAC,KAAK,KAAK,KAAK,OAAO,QAAQ,MAAM;AAC9C,iBAAa,OAAO,IAAI,GAAG,IAAI,KAAK,UAAU,KAAK,CAAC;AACtD,aAAW,CAAC,KAAK,KAAK,KAAK,OAAO,QAAQ,IAAI;AAC5C,aAAS,aAAa,OAAO,KAAK,GAAG,KAAK,EAAE;AAC9C,SAAO,gBAAgB,SAAM,aAAa,OAAO,eAAe,OAAO,GAAG,qBAAqB,SAAM,aAAa,OAAO,oBAAoB,OAAO,GAAG,IAAI,YAAY;AACzK,GAAG,gBAAgB,CAAC,OAAO,aAAa,UAAU,QAAK,SAAS,OAAO,QAAQ,MAAM,WAAW,OAAO,mBAAmB,CAAC,UAAU,QAAQ;AAAA,EAC3I,QAAQ,QAAQ;AAAA,EAChB,WAAW;AAAA,EACX,iBAAiB,cAAc,QAAQ,iBAAiB,IAAE;AAAA,EAC1D,YAAY,QAAQ,cAAc;AAAA,EAClC,uBAAuB,QAAQ;AAAA,EAC/B,qCAAqC,QAAQ;AAC/C,IAAI,aAAa,CAAC,UAAU,MAAM,SAAS,YAAY,UAAU,CAAC,UAAU,MAAM,MAAM,UAAU,CAAC,MAAM,SAAS,KAAK,OAAO,CAAC,SAAS,SAAS,QAAQ,KAAK,GAAG,CAAC,IAAI,KAAK,UAA0B,uBAAO,OAAO,IAAI,CAAC,GAAG,oBAAoB;AAC/O,SAAS,OAAOU,SAAQ,aAAa,QAAQ,OAAO,UAAU,CAAA,GAAI,UAAU,IAAI;AAC9E,QAAM,QAAQ,WAAW,UAAU;AAAA,IACjC,GAAG,UAAU,CAAE;AAAA,IACf,GAAG,OAAO,QAAQ,SAAS,YAAY,EAAE,SAAS,QAAQ,MAAK,IAAK,QAAQ,SAAS,CAAA;AAAA,EACtF,IAAG,QAAQ,SAAS,MAAM,UAAU,WAAW,OAAO,IAAI,SAAS,cAAc,QAAQ,mBAAmB,QAAK,CAAC,QAAQ,MAAM,CAAC,QAAQ,IAAI,QAAQ,EAAE,OAAO,MAAM,GAAG,SAAS;AAAA;AAAA;AAAA,IAG/K,gBAAgB,OAAO,QAAQ,SAAS;AAAA;AAAA,IAExC,iBAAiB,MAAM,UAAU,yBAAyB,QAAQ;AAAA,IAClE,GAAG;AAAA;AAAA;AAAA,IAGH,aAAa,QAAQ,mBAAmB,SAAM,QAAQ,gBAAgB;AAAA,EACvE,GAAE,UAAU,OAAO,QAAQ,OAAO,OAAO,OAAO,MAAM,EAAE,GAAG,MAAM,OAAO,EAAE,OAAO,KAAM,EAAA,IAAK,MAAM,WAAW,aAAaA,SAAQ,aAAa,SAAS,EAAE,OAAO,OAAM,GAAI,OAAO;AACnL,SAAO,MAAM,UAAU,SAAS;AAAA,IAC9B;AAAA,MACE;AAAA,QACE,OAAO,2CAAsC,EAAE,KAAK,SAASpD,IAAG;AAC9D,iBAAOA,GAAE;AAAA,QACV,CAAA,EAAE;AAAA,UACD,CAAC,EAAE,qBAAoB,MAAO;AAAA,QACxC;AAAA,MACA;AAAA,IACK;AAAA,IACD;AAAA,MACE,CAAC,CAAC,KAAK,oBAAoB,MAAM;AAC/B,cAAM,SAAS,qBAAqB,IAAI,QAAQ,IAAI,iBAAiB,KAAK;AAC1E,eAAO,YAAY,EAAE,GAAG,KAAK,OAAM,CAAE;AAAA,MAC7C;AAAA,IACA;AAAA,EACG,IAAG,SAAS,KAAK,IAAI,WAAW,CAAC;AACpC;AACA,SAAS,aAAaoD,SAAQ,aAAa,IAAI,OAAO,CAAA,GAAI;AACxD,QAAM,SAAS,MAAM;AACnB,QAAI,CAAC,KAAK;AACR,aAAO;AACT,UAAM,YAAY,iBAAiB,EAAE;AACrC,QAAI,CAAC,WAAW;AACd,UAAI,UAAU,EAAE;AACd,cAAM,IAAI;AAAA,UACR,sBAAsB,EAAE,yDAAyD,KAAK,SAAS;AAAA,QAChG;AACH,aAAO,aAAa,IAAI,KAAK,SAAS;AAAA,IAC5C;AACI,QAAI,cAAc,KAAK;AACrB,YAAM,IAAI;AAAA,QACR,sBAAsB,EAAE,iCAAiC,SAAS,6EAA6E,KAAK,SAAS;AAAA,MAC9J;AACH,WAAO;AAAA,EACR,GAAA,GAAK,UAAU;AAAA,IACd,KAAK,YAAYA,SAAQ,OAAO,KAAK;AAAA,IACrC,MAAM;AAAA,IACN,KAAK,KAAK;AAAA,IACV,QAAQ,KAAK;AAAA,EACd;AACD,SAAO,mBAAmBA,SAAQ,aAAa,OAAO,EAAE;AAAA,IACtD,OAAO,UAAU;AAAA,IACjB,IAAI,CAAC,UAAU,MAAM,KAAK,aAAa,MAAM,KAAK,UAAU,CAAC,CAAC;AAAA,EAC/D;AACH;AACA,SAAS,cAAcA,SAAQ,aAAa,KAAK,OAAO,CAAA,GAAI;AAC1D,QAAM,UAAU;AAAA,IACd,KAAK,YAAYA,SAAQ,OAAO,IAAI,KAAK,GAAG,CAAC;AAAA,IAC7C,MAAM;AAAA,IACN,KAAK,KAAK;AAAA,IACV,QAAQ,KAAK;AAAA,EACd;AACD,SAAO,mBAAmBA,SAAQ,aAAa,OAAO,EAAE;AAAA,IACtD,OAAO,UAAU;AAAA,IACjB,IAAI,CAAC,UAAU;AACb,YAAM,UAAU,QAAQ,MAAM,KAAK,aAAa,CAAA,GAAI,CAAC,QAAQ,IAAI,GAAG;AACpE,aAAO,IAAI,IAAI,CAAC,OAAO,QAAQ,EAAE,KAAK,IAAI;AAAA,IAC3C,CAAA;AAAA,EACF;AACH;AACA,SAAS,qBAAqBA,SAAQ,aAAa,WAAW,OAAO,CAAA,GAAI;AACvE,SAAO;AAAA,IACLA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,MACE,OAAO;AAAA,MACP,QAAQ;AAAA,QACN;AAAA,MACR;AAAA,IACK;AAAA,IACD;AAAA,EACD;AACH;AACA,SAAS,mBAAmBA,SAAQ,aAAa,KAAK,SAAS;AAC7D,SAAO,kBAAkB,qBAAqB,GAAG,GAAG,QAAQA,SAAQ,aAAa,KAAK,qBAAqB,OAAO;AACpH;AACA,SAAS,iBAAiBA,SAAQ,aAAa,KAAK,SAAS;AAC3D,SAAO,kBAAkB,mBAAmB,GAAG,GAAG,QAAQA,SAAQ,aAAa,KAAK,mBAAmB,OAAO;AAChH;AACA,SAAS,eAAeA,SAAQ,aAAa,KAAK,aAAa,SAAS;AACtE,SAAO,kBAAkB,iBAAiB,GAAG,GAAG,oBAAoB,iBAAiB,GAAG,GAAG,QAAQA,SAAQ,aAAa;AAAA,IACtH,YAAY;AAAA,IACZ;AAAA,IACA,UAAU;AAAA,EACX,GAAE,OAAO;AACZ;AACA,SAAS,QAAQA,SAAQ,aAAa,WAAW,SAAS;AACxD,SAAO;AAAA,IACLA;AAAA,IACA;AAAA,IACA;AAAA,IACA,EAAE,WAAW,CAAC,EAAE,QAAQ,aAAa,SAAS,EAAC,CAAE,EAAG;AAAA,IACpD;AAAA,EACD;AACH;AACA,SAAS,gBAAgBA,SAAQ,aAAa,WAAW,QAAQ,OAAI,SAAS;AAC5E,SAAO,QAAQA,SAAQ,aAAa;AAAA,IAClC,YAAY;AAAA,IACZ;AAAA,IACA;AAAA,EACD,GAAE,OAAO;AACZ;AACA,SAAS,gBAAgBA,SAAQ,aAAa,KAAK,SAAS;AAC1D,SAAO,kBAAkB,kBAAkB,GAAG,GAAG,oBAAoB,kBAAkB,GAAG,GAAG,QAAQA,SAAQ,aAAa;AAAA,IACxH,YAAY;AAAA,IACZ,UAAU;AAAA,EACX,GAAE,OAAO;AACZ;AACA,SAAS,kBAAkBA,SAAQ,aAAa,WAAW,aAAa,SAAS;AAC/E,SAAO,QAAQA,SAAQ,aAAa;AAAA,IAClC,YAAY;AAAA,IACZ;AAAA,IACA;AAAA,EACD,GAAE,OAAO;AACZ;AACA,SAAS,QAAQA,SAAQ,aAAa,WAAW,SAAS;AACxD,MAAI;AACJ,uBAAqB,SAAS,qBAAqB,kBAAkB,MAAM,EAAE,OAAO,UAAU,UAAW,EAAA,IAAK,qBAAqB,eAAe,qBAAqB,wBAAwB,MAAM,UAAU,UAAS,IAAK,MAAM;AACnO,QAAM,OAAO,MAAM,QAAQ,GAAG,IAAI,MAAM,CAAC,GAAG,GAAG,gBAAgB,WAAW,QAAQ,iBAAiB;AACnG,SAAO,aAAaA,SAAQ,aAAa,UAAU,EAAE,WAAW,MAAM,cAAe,GAAE,OAAO;AAChG;AACA,SAAS,QAAQA,SAAQ,aAAa,SAAS,SAAS;AACtD,QAAM,OAAO,MAAM,QAAQ,OAAO,IAAI,UAAU,CAAC,OAAO,GAAG,gBAAgB,WAAW,QAAQ,iBAAiB,QAAQ,sCAAsC,WAAW,QAAQ,uCAAuC,QAAQ,SAAS,WAAW,QAAQ,UAAU;AACrQ,SAAO;AAAA,IACLA;AAAA,IACA;AAAA,IACA;AAAA,IACA,EAAE,SAAS,MAAM,eAAe,qCAAqC,OAAQ;AAAA,IAC7E;AAAA,EACD;AACH;AACA,SAAS,aAAaA,SAAQ,aAAa,UAAU,MAAM,UAAU,IAAI;AACvE,QAAM,aAAa,aAAa,UAAU,WAAW,aAAa,WAAW,WAAW,aAAa,SAAS,WAAW,cAAc,WAAW,KAAK,kBAAkB,IAAI,GAAG,SAAS,CAAC,cAAc,CAAC,YAAY,SAAS,SAAS,mBAAmB,cAAc,SAAS,WAAW,IAAI,cAAc,QAAQ,aAAa,EAAE,SAAS,OAAAC,QAAO,KAAK,SAAS,aAAa,iBAAiB,cAAc,SAAS,MAAM,YAAYD,SAAQ,UAAU,WAAW,GAAG,aAAa;AAAA,IACrd,QAAQ,SAAS,QAAQ;AAAA,IACzB;AAAA,IACA,MAAM;AAAA,IACN,MAAM,SAAS,SAAS;AAAA,IACxB,OAAO,cAAc,iBAAiB,OAAO;AAAA,IAC7C;AAAA,IACA;AAAA,IACA,OAAAC;AAAA,IACA;AAAA,IACA;AAAA,IACA,aAAa,QAAQ;AAAA,IACrB,iBAAiB,QAAQ;AAAA,IACzB,iBAAiB,MAAM,QAAQ,eAAe,IAAI,gBAAgB,CAAC,IAAI;AAAA,IACvE;AAAA,IACA,WAAW;AAAA,IACX,QAAQ,QAAQ;AAAA,IAChB,OAAO,QAAQ;AAAA,IACf,gBAAgB,QAAQ;AAAA,IACxB,QAAQ,QAAQ;AAAA,EACjB;AACD,SAAO,mBAAmBD,SAAQ,aAAa,UAAU,EAAE;AAAA,IACzD,OAAO,UAAU;AAAA,IACjB,IAAI,OAAO;AAAA,IACX,IAAI,CAAC,QAAQ;AACX,UAAI,CAAC;AACH,eAAO;AACT,YAAM,UAAU,IAAI,WAAW,CAAE;AACjC,UAAI,QAAQ;AACV,eAAO,cAAc,QAAQ,CAAC,KAAK,QAAQ,CAAC,EAAE,WAAW,QAAQ,IAAI,CAAC,QAAQ,IAAI,QAAQ;AAC5F,YAAM,MAAM,cAAc,eAAe,eAAe,MAAM,cAAc,QAAQ,CAAC,KAAK,QAAQ,CAAC,EAAE,KAAK,QAAQ,IAAI,CAAC,QAAQ,IAAI,EAAE;AACrI,aAAO;AAAA,QACL,eAAe,IAAI;AAAA,QACnB;AAAA,QACA,CAAC,GAAG,GAAG;AAAA,MACR;AAAA,IACF,CAAA;AAAA,EACF;AACH;AACA,SAAS,QAAQA,SAAQ,aAAa,KAAK,IAAI,UAAU,IAAI;AAC3D,QAAM,WAAW,EAAE,CAAC,EAAE,GAAG,IAAG,GAAI,OAAO,OAAO,OAAO,EAAE,aAAa,MAAI,iBAAiB,KAAE,GAAI,OAAO;AACtG,SAAO,aAAaA,SAAQ,aAAa,UAAU,EAAE,WAAW,CAAC,QAAQ,EAAG,GAAE,IAAI;AACpF;AACA,MAAM,gBAAgB,CAACA,YAAWA,QAAO,OAAM,EAAG,YAAY,UAAUA,QAAO,SAAS,cAAc,UAAUA,QAAO,OAAM,EAAG,wBAAwB,MAAM,QAAQ,UAAU,CAACA,SAAQ,QAAQ,cAAcA,OAAM,KAAK,IAAI,WAAW,YAAYA,SAAQ,OAAO,CAAC,GAAG,WAAW,CAACA,SAAQ,QAAQ,cAAcA,OAAM,KAAK,IAAI,WAAW,YAAYA,SAAQ,QAAQ,CAAC,GAAG,QAAQ,CAACA,SAAQ,QAAQ,cAAcA,OAAM,KAAK,IAAI,WAAW,YAAYA,SAAQ,OAAO,EAAE,CAAC,GAAG,aAAa,CAACA,SAAQ,QAAQ,cAAcA,OAAM,KAAK,IAAI,WAAW,YAAYA,SAAQ,QAAQ,CAAC,GAAG,YAAY,CAACA,SAAQ,QAAQ,cAAcA,OAAM,KAAK,IAAI,WAAW,YAAYA,SAAQ,WAAW,EAAE,CAAC,GAAG,SAAS,CAACA,SAAQ,QAAQ,IAAI,WAAW,QAAQ,KAAK,QAAQA,SAAQ,GAAG,KAAK,SAASA,SAAQ,GAAG,KAAK,MAAMA,SAAQ,GAAG,KAAK,WAAWA,SAAQ,GAAG,KAAK,UAAUA,SAAQ,GAAG;AAC30B,SAAS,mBAAmBA,SAAQ,aAAa,SAAS;AACxD,QAAM,MAAM,QAAQ,OAAO,QAAQ,KAAKV,UAASU,QAAO,OAAM,GAAI,YAAY,OAAO,QAAQ,YAAY,MAAM,CAAC,OAAO,MAAM,EAAE,QAAQ,QAAQ,UAAU,KAAK,KAAK,KAAK,OAAOA,SAAQ,GAAG,IAAI,QAAQ;AACtM,MAAI,UAAU,QAAQ,UAAUV,QAAO,WAAW;AAClD,QAAM,MAAM,QAAQ,OAAOA,QAAO,mBAAmB,CAACA,QAAO,kBAAkB,QAAQ,GAAG,EAAE,KAAK,GAAG,IAAI,QAAQ,OAAOA,QAAO;AAC9H,MAAI,OAAO,QAAQ,QAAQ,SAAS,QAAQ,QAAQ,EAAE,KAAK,WAAW,GAAG,GAAG,GAAG,QAAQ,MAAK,IAAK,CAAC,OAAO,QAAQ,MAAM,EAAE,QAAQ,QAAQ,UAAU,KAAK,KAAK,KAAK,QAAQU,SAAQ,GAAG,GAAG;AACtL,UAAM,kBAAkB,QAAQ,mBAAmBV,QAAO;AAC1D,wBAAoB,UAAU,oBAAoB,UAAO,QAAQ,QAAQ,EAAE,iBAAiB,GAAG,QAAQ;AACvG,UAAM,oBAAoB,QAAQ,eAAeA,QAAO;AACxD,WAAO,oBAAoB,QAAQ,sBAAsB,mBAAmB,wCAAwC,uBAAuB,iBAAiB,GAAG,QAAQ,QAAQ;AAAA,MAC7K,aAAa,MAAM,QAAQ,iBAAiB,IAAI,kBAAkB,KAAK,GAAG,IAAI;AAAA,MAC9E,GAAG,QAAQ;AAAA,IACjB,IAAQ,MAAM,QAAQ,iBAAiB,KAAK,kBAAkB,SAAS;AAAA,IACnE,sBAAsB,mBAAmB,sBAAsB,aAAa,WAAW,SAAS,OAAI,6BAA8B,KAAI,QAAQ,oBAAoB,QAAQ,QAAQ,EAAE,GAAG,QAAQ,OAAO,iBAAiB,QAAQ,gBAAe,IAAK,QAAQ,gBAAgB,UAAO,QAAQ,QAAQ,EAAE,aAAa,SAAS,GAAG,QAAQ,MAAK,IAAK,UAAU,QAAQ,aAAa,cAAc,QAAQ,QAAQ,EAAE,WAAW,WAAW,GAAG,QAAQ;EACnb;AACE,QAAM,aAAa;AAAA,IACjBA;AAAA,IACA,OAAO,OAAO,CAAE,GAAE,SAAS;AAAA,MACzB,KAAK,QAAQU,SAAQ,KAAK,MAAM;AAAA,IACjC,CAAA;AAAA,EACL,GAAK,UAAU,IAAI;AAAA,IACf,CAAC,eAAe,YAAY,YAAYV,QAAO,SAAS,EAAE,UAAU,UAAU;AAAA,EAC/E;AACD,SAAO,QAAQ,SAAS,QAAQ,KAAK,iBAAiB,QAAQ,MAAM,CAAC,IAAI;AAC3E;AACA,SAAS,SAASU,SAAQ,aAAa,SAAS;AAC9C,SAAO,mBAAmBA,SAAQ,aAAa,OAAO,EAAE;AAAA,IACtD,OAAO,CAAC,UAAU,MAAM,SAAS,UAAU;AAAA,IAC3C,IAAI,CAAC,UAAU,MAAM,IAAI;AAAA,EAC1B;AACH;AACA,SAAS,YAAYA,SAAQ,WAAW,MAAM;AAC5C,QAAMV,UAASU,QAAO,OAAQ;AAC9B,MAAIV,QAAO,wBAAwB,GAAG;AACpC,mBAAeA,OAAM;AACrB,UAAM,eAAe,iBAAiBA,OAAM,GAAG,OAAO,SAAS,SAAS,GAAG,SAAS,IAAI,IAAI,KAAK;AACjG,WAAO,GAAG,YAAY,IAAI,IAAI,GAAG,QAAQ,YAAY,IAAI;AAAA,EAC7D;AACE,QAAM,UAAU,WAAWA,OAAM,GAAG,UAAU,IAAI,SAAS,IAAI,OAAO;AACtE,SAAO,QAAQ,SAAS,SAAS,GAAG,OAAO,IAAI,IAAI,KAAK,OAAO,GAAG,QAAQ,YAAY,IAAI;AAC5F;AACA,SAAS,QAAQU,SAAQ,KAAK,YAAY,OAAI;AAC5C,QAAM,EAAE,KAAK,WAAWA,QAAO,OAAQ;AACvC,SAAO,GAAG,YAAY,SAAS,GAAG,IAAI,IAAI,QAAQ,OAAO,EAAE,CAAC;AAC9D;AACA,SAAS,iBAAiB,QAAQ;AAChC,SAAO,CAAC,UAAU,IAAI,WAAW,CAAC,aAAa;AAC7C,UAAM,QAAQ,MAAM,SAAS,MAAM,kBAAkB,MAAM,CAAC;AAC5D,QAAI,UAAU,OAAO,SAAS;AAC5B,YAAO;AACP;AAAA,IACN;AACI,UAAM,eAAe,MAAM,UAAU,QAAQ;AAC7C,WAAO,OAAO,iBAAiB,SAAS,KAAK,GAAG,MAAM;AACpD,aAAO,oBAAoB,SAAS,KAAK,GAAG,aAAa,YAAa;AAAA,IACvE;AAAA,EACL,CAAG;AACH;AACA,MAAM,0BAA0B,CAAC,CAAC,WAAW;AAC7C,SAAS,kBAAkB,QAAQ;AACjC,MAAI;AACF,WAAO,IAAI,aAAa,QAAQ,UAAU,8BAA8B,YAAY;AACtF,QAAM,QAAQ,IAAI,MAAM,QAAQ,UAAU,4BAA4B;AACtE,SAAO,MAAM,OAAO,cAAc;AACpC;AACA,MAAM,mBAAmB,CAACV,YAAW;AACnC,MAAI,CAACA,QAAO,wBAAwB;AAClC,UAAM,IAAI,MAAM,yDAAyD;AAC3E,QAAM,EAAE,MAAM,OAAOA,QAAO,wBAAwB;AACpD,UAAQ,MAAI;AAAA,IACV,KAAK,WAAW;AACd,YAAM,WAAW,GAAG,MAAM,GAAG;AAC7B,UAAI,SAAS,WAAW;AACtB,cAAM,IAAI,MAAM,oDAAoD;AACtE,aAAO,aAAa,SAAS,CAAC,CAAC,aAAa,SAAS,CAAC,CAAC;AAAA,IAC7D;AAAA,IACI,KAAK;AACH,aAAO,aAAa,EAAE;AAAA,IACxB,KAAK;AACH,aAAO,oBAAoB,EAAE;AAAA,IAC/B,KAAK;AACH,aAAO,eAAe,EAAE;AAAA,IAC1B;AACE,YAAM,IAAI,MAAM,8BAA8B,KAAK,SAAU,CAAA,EAAE;AAAA,EACrE;AACA;AACA,SAAS,UAAUU,SAAQ,aAAa,SAAS;AAC/C,QAAM,WAAW,WAAWA,QAAO,OAAM,CAAE;AAC3C,SAAO,SAASA,SAAQ,aAAa;AAAA,IACnC,QAAQ;AAAA,IACR,KAAK,0BAA0B,QAAQ;AAAA,IACvC,MAAM;AAAA,EACV,CAAG;AACH;AACA,SAAS,WAAWA,SAAQ,aAAa,SAAS;AAChD,QAAM,WAAW,WAAWA,QAAO,OAAM,CAAE;AAC3C,SAAO,SAASA,SAAQ,aAAa;AAAA,IACnC,QAAQ;AAAA,IACR,KAAK,2BAA2B,QAAQ;AAAA,IACxC,MAAM;AAAA,EACV,CAAG;AACH;AACA,SAAS,WAAWA,SAAQ,aAAa,SAAS;AAChD,QAAM,WAAW,WAAWA,QAAO,OAAM,CAAE;AAC3C,SAAO,SAASA,SAAQ,aAAa;AAAA,IACnC,QAAQ;AAAA,IACR,KAAK,2BAA2B,QAAQ;AAAA,IACxC,MAAM;AAAA,EACV,CAAG;AACH;AACA,MAAM,6BAA6B;AAAA,EACjC;AAAA,EACA;AAAA,EACA,YAAYA,SAAQ,aAAa;AAC/B,SAAK,UAAUA,SAAQ,KAAK,eAAe;AAAA,EAC/C;AAAA;AAAA;AAAA;AAAA;AAAA,EAKE,SAAS,SAAS;AAChB,WAAO,UAAU,KAAK,SAAS,KAAK,cAAc,OAAO;AAAA,EAC7D;AAAA;AAAA;AAAA;AAAA;AAAA,EAKE,UAAU,SAAS;AACjB,WAAO,WAAW,KAAK,SAAS,KAAK,cAAc,OAAO;AAAA,EAC9D;AAAA;AAAA;AAAA;AAAA;AAAA,EAKE,UAAU,SAAS;AACjB,WAAO,WAAW,KAAK,SAAS,KAAK,cAAc,OAAO;AAAA,EAC9D;AACA;AACA,MAAM,mBAAmB;AAAA,EACvB;AAAA,EACA;AAAA,EACA,YAAYA,SAAQ,aAAa;AAC/B,SAAK,UAAUA,SAAQ,KAAK,eAAe;AAAA,EAC/C;AAAA;AAAA;AAAA;AAAA;AAAA,EAKE,SAAS,SAAS;AAChB,WAAO,cAAc,UAAU,KAAK,SAAS,KAAK,cAAc,OAAO,CAAC;AAAA,EAC5E;AAAA;AAAA;AAAA;AAAA;AAAA,EAKE,UAAU,SAAS;AACjB,WAAO,cAAc,WAAW,KAAK,SAAS,KAAK,cAAc,OAAO,CAAC;AAAA,EAC7E;AAAA;AAAA;AAAA;AAAA;AAAA,EAKE,UAAU,SAAS;AACjB,WAAO,cAAc,WAAW,KAAK,SAAS,KAAK,cAAc,OAAO,CAAC;AAAA,EAC7E;AACA;AACA,MAAM,uBAAuB;AAAA,EAC3B;AAAA,EACA;AAAA,EACA,YAAYA,SAAQ,aAAa;AAC/B,SAAK,UAAUA,SAAQ,KAAK,eAAe;AAAA,EAC/C;AAAA,EACE,OAAO,WAAW,MAAM,SAAS;AAC/B,WAAO,QAAQ,KAAK,SAAS,KAAK,cAAc,WAAW,MAAM,OAAO;AAAA,EAC5E;AACA;AACA,MAAM,aAAa;AAAA,EACjB;AAAA,EACA;AAAA,EACA,YAAYA,SAAQ,aAAa;AAC/B,SAAK,UAAUA,SAAQ,KAAK,eAAe;AAAA,EAC/C;AAAA,EACE,OAAO,WAAW,MAAM,SAAS;AAC/B,UAAM,cAAc,QAAQ,KAAK,SAAS,KAAK,cAAc,WAAW,MAAM,OAAO;AACrF,WAAO;AAAA,MACL,YAAY;AAAA,QACV,OAAO,CAAC,UAAU,MAAM,SAAS,UAAU;AAAA,QAC3C;AAAA,UACE,CAAC,UAAU,MAAM,KAAK;AAAA,QAChC;AAAA,MACA;AAAA,IACK;AAAA,EACL;AACA;AACA,SAAS,QAAQA,SAAQ,aAAa,WAAW,MAAM,OAAO,IAAI;AAChE,oBAAkB,SAAS;AAC3B,MAAI,OAAO,KAAK,WAAW;AAC3B,UAAQ,CAAC,KAAK,WAAW,OAAO,CAAC,MAAM;AACvC,QAAMV,UAASU,QAAO,OAAQ,GAAE,UAAU,gBAAgB,MAAM,IAAI,GAAG,EAAE,KAAK,OAAO,OAAO,aAAa,YAAY,UAAU,OAAQ,IAAG,SAAS,QAAQ;AAAA,IACzJ;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,EACD;AACD,SAAO,WAAW,MAAM,WAAW,OAAO,IAAI,MAAM,aAAa,OAAO,MAAM,MAAM,YAAY,OAAO,MAAM,mBAAmBA,SAAQ,aAAa;AAAA,IACnJ;AAAA,IACA,QAAQ;AAAA,IACR,SAAS,QAAQ,WAAW;AAAA,IAC5B,KAAK,oBAAoBV,SAAQ,SAAS;AAAA,IAC1C,SAAS,QAAQ,cAAc,EAAE,gBAAgB,QAAQ,YAAW,IAAK,CAAE;AAAA,IAC3E;AAAA,IACA;AAAA,EACJ,CAAG;AACH;AACA,SAAS,oBAAoBA,SAAQ,WAAW;AAC9C,QAAM,oBAAoB,cAAc,UAAU,WAAW;AAC7D,MAAIA,QAAO,wBAAwB,GAAG;AACpC,UAAM,EAAE,MAAM,OAAOA,QAAO,wBAAwB;AACpD,YAAQ,MAAI;AAAA,MACV,KAAK;AACH,cAAM,IAAI;AAAA,UACR;AAAA,QACD;AAAA,MACH,KAAK;AACH,eAAO,aAAa,EAAE,WAAW,iBAAiB;AAAA,MACpD,KAAK;AACH,eAAO,oBAAoB,EAAE;AAAA,MAC/B,KAAK;AACH,eAAO,eAAe,EAAE,WAAW,iBAAiB;AAAA,MACtD;AACE,cAAM,IAAI,MAAM,8BAA8B,KAAK,SAAU,CAAA,EAAE;AAAA,IACvE;AAAA,EACA;AACE,QAAM,WAAW,WAAWA,OAAM;AAClC,SAAO,UAAU,iBAAiB,IAAI,QAAQ;AAChD;AACA,SAAS,gBAAgB,MAAM,MAAM;AACnC,SAAO,OAAO,OAAO,OAAO,EAAE,gBAAgB,QAAQ,OAAO,OAAO;AAAA,IAClE;AAAA,MACE,UAAU,KAAK,qBAAqB,QAAK,SAAS,KAAK;AAAA,MACvD,aAAa,KAAK;AAAA,IACnB;AAAA,IACD;AAAA,EACD;AACH;AACA,IAAI,WAAW,CAAC,KAAK,cAAc,OAAO,KAAK,SAAS,EAAE,OAAO,OAAO,KAAK,GAAG,CAAC,EAAE,OAAO,CAAC,QAAQ,UAAU,OAAO,IAAI,IAAI,OAAO,IAAI,IAAI,IAAI,MAAM,UAAU,IAAI,IAAI,IAAI,IAAI,GAAG,SAAS,CAAA,CAAE;AAC7L,MAAM,OAAO,CAAC,KAAK,UAAU,MAAM,OAAO,CAAC,WAAW,UAAU,OAAO,IAAI,IAAI,IAAI,QAAQ,UAAU,IAAI,IAAI,IAAI,IAAI,IAAI,YAAY,CAAA,CAAE,GAAG,sBAAsB,MAAM,MAAM,OAAO,2BAAqB,EAAA,KAAA,CAAA1C,OAAAA,GAAA,CAAA,CAAC,EAAE;AAAA,EACzM,IAAI,CAAC,EAAE,SAAS,aAAY,MAAO,YAAY;AAAA,EAC/C,YAAY,CAAC;AACf;AACA,SAAS,+BAA+B;AACtC,SAAO,SAAS,QAAQ;AACtB,WAAO,OAAO;AAAA,MACZ,WAAW,CAAC,KAAK,WAAW,eAAe,wBAAwB,OAAO,GAAG,EAAE,MAAM,YAAW,CAAE,GAAG,MAAM,GAAG,EAAE,KAAK,SAAS,MAAM,MAAM,CAAC,CAAC,IAAI,WAAW,MAAM,GAAG,CAAC;AAAA,IACtK;AAAA,EACF;AACH;AACA,MAAM,iBAAiB,OAAO,kBAAkB;AAAA,EAC9C;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AACF,GAAG,iBAAiB;AAAA,EAClB,eAAe;AACjB;AACA,SAAS,QAAQ,OAAO,QAAQ,OAAO,CAAA,GAAI;AACzC,QAAM,EAAE,KAAK,OAAAqD,QAAO,iBAAiB,iBAAkB,IAAG,KAAK,UAAU,MAAM,KAAK,OAAO,mBAAmB,CAAC,kBAAkB,KAAK,GAAG,EAAE,KAAK,GAAG,IAAI,KAAK,KAAK,UAAU,EAAE,GAAG,SAAS,MAAM,cAAc,GAAG,OAAO,aAAa,KAAK,SAAS,eAAe,GAAG,KAAK,kBAAkB,EAAE,OAAO,QAAQ,SAAS,EAAE,KAAK,GAAG,WAAY,EAAA,CAAE,GAAG,MAAM,GAAG,GAAG,GAAG,YAAY,MAAM,UAAU,EAAE,CAAC;AAC9X,MAAI,IAAI,SAAS;AACf,WAAO,WAAW,MAAM,IAAI,MAAM,8BAA8B,CAAC;AACnE,QAAM,YAAY,QAAQ,SAAS,QAAQ,SAAS,CAAC,UAAU,GAAG,YAAY,CAAE;AAChF,SAAO,oBAAoB,UAAU,kBAAkB,OAAKA,WAAU,UAAU,UAAU;AAAA,IACxF,eAAe,UAAUA,MAAK;AAAA,EAC/B,IAAG,mBAAmB;AAAA;AAAA,KAEpB,OAAO,cAAc,OAAO,UAAU,UAAU,sBAAsB,GAAG,WAAW,GAAG,KAAK,IAAI,CAAC,iBAAiB,IAAI,aAAa,KAAK,SAAS,CAAC,CAAC;AAAA,KACnJ,SAAS,EAAE;AAAA,IACZ,6BAA8B;AAAA,IAC9B,OAAO,CAAC,UAAU,UAAU,SAAS,MAAM,IAAI,CAAC;AAAA,IAChD;AAAA,MACE,CAAC,WAAW;AAAA,QACV,MAAM,MAAM;AAAA,QACZ,GAAG,UAAU,QAAQ,MAAM,OAAO,CAAA;AAAA,MACnC;AAAA,IACP;AAAA,EACG;AACH;AACA,SAAS,kBAAkB,mBAAmBX,SAAQ;AACpD,SAAO;AAAA,IACL,OAAO,qBAAqB,aAAa,EAAE,WAAW,mBAAmB,GAAGA,YAAW;AAAA,EACxF;AACH;AACA,SAAS,mBAAmBA,SAAQ;AAClC,SAAO,CAAC,WAAW;AACjB,QAAI,QAAQ,UAAU;AACtB,UAAM,EAAE,WAAW,GAAG,YAAW,IAAKA,SAAQ,UAAU,OAAO;AAAA,MAC7D,IAAI,CAAC,UAAU;AACb,QAAAA,QAAO,UAAU,KAAK,MAAM,UAAU,MAAI,SAAS;AAAA,MAC3D,CAAO;AAAA,MACD,SAAS,MAAM;AACb,kBAAU,OAAI,SAAS;AAAA,MAC/B,CAAO;AAAA,MACD,MAAM,WAAW;AAAA,IAClB,GAAE,aAAa,IAAI,WAAW,CAAC,eAAe;AAC7C,iBAAW,WAAW;AAAA;AAAA,QAEpB;AAAA,MACR,GAAS,WAAW,SAAU;AAAA,IAC9B,CAAK;AACD,WAAO,MAAM,SAAS,UAAU;AAAA,EACjC;AACH;AACA,MAAM,qBAAqB;AAC3B,MAAM,WAAW;AAAA,EACf;AAAA,EACA,YAAYU,SAAQ;AAClB,SAAK,UAAUA;AAAA,EACnB;AAAA;AAAA;AAAA;AAAA,EAIE,OAAO;AAAA,IACL,gBAAgB;AAAA,IAChB,KAAK;AAAA,EACN,IAAG,IAAI;AACN,kBAAc,QAAQ,KAAK,QAAQ,OAAM,CAAE;AAC3C,UAAM;AAAA,MACJ,WAAW;AAAA,MACX,YAAY;AAAA,MACZ,OAAAC;AAAA,MACA;AAAA,MACA;AAAA,IACN,IAAQ,KAAK,QAAQ,OAAQ,GAAE,aAAa,YAAY,QAAQ,MAAM,EAAE;AACpE,QAAI,eAAe,OAAO,aAAa;AACrC,YAAM,IAAI;AAAA,QACR,4CAA4C,kBAAkB,yCAAyC,UAAU;AAAA,MAClH;AACH,QAAI,iBAAiB,CAACA,UAAS,CAAC;AAC9B,YAAM,IAAI;AAAA,QACR;AAAA,MACD;AACH,UAAM,OAAO,YAAY,KAAK,SAAS,aAAa,GAAG,MAAM,IAAI,IAAI,KAAK,QAAQ,OAAO,MAAM,KAAE,CAAC,GAAG,MAAM,QAAQ,mBAAmB,CAAC,kBAAkB,IAAI,EAAE,KAAK,GAAG,IAAI;AAC3K,WAAO,IAAI,aAAa,IAAI,OAAO,GAAG,GAAG,iBAAiB,IAAI,aAAa,IAAI,iBAAiB,MAAM;AACtG,UAAM,YAAY,CAAE;AACpB,qBAAiBA,WAAU,UAAU,UAAU;AAAA,MAC7C,eAAe,UAAUA,MAAK;AAAA,IAC/B,IAAG,iBAAiB,oBAAoB,UAAU,kBAAkB;AACrE,UAAM,MAAM,GAAG,IAAI,IAAI,KAAK,KAAK,UAAU,SAAS,CAAC,IAAI,WAAW,YAAY,IAAI,GAAG;AACvF,QAAI;AACF,aAAO;AACT,UAAM,SAAS,mBAAmB;AAAA;AAAA,OAE/B,OAAO,cAAc,OAAO,UAAU,UAAU,sBAAsB,GAAG,WAAW,GAAG,KAAK,IAAI,CAAC,iBAAiB,IAAI,aAAa,IAAI,MAAM,SAAS,CAAC,CAAC;AAAA,OACxJ;AAAA,MACD;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,IACD,CAAA,EAAE;AAAA,MACD,6BAA8B;AAAA,MAC9B,IAAI,CAAC,UAAU;AACb,YAAI,MAAM,SAAS,WAAW;AAC5B,gBAAM,EAAE,MAAM,GAAG,KAAI,IAAK;AAC1B,iBAAO,EAAE,GAAG,MAAM,MAAM,KAAK,KAAM;AAAA,QAC7C;AACQ,eAAO;AAAA,MACR,CAAA;AAAA,IACP,GAAO,YAAY,gBAAgB,KAAK;AAAA,MAClC,QAAQ;AAAA,MACR,MAAM;AAAA,MACN,aAAa,UAAU,kBAAkB,YAAY;AAAA,MACrD,SAAS,UAAU;AAAA,IACpB,CAAA,EAAE;AAAA,MACD,SAAS,MAAM,KAAK;AAAA,MACpB,WAAW,MAAM;AACf,cAAM,IAAI,gBAAgB,EAAE,WAAW,WAAU,CAAE;AAAA,MACpD,CAAA;AAAA,IACF,GAAE,cAAc,OAAO,WAAW,MAAM,EAAE;AAAA,MACzCC,SAAW,MAAM,YAAY,OAAO,GAAG,CAAC;AAAA,MACxC,kBAAkB;AAAA,QAChB,WAAW,CAAC,UAAU,MAAM,SAAS;AAAA,MACtC,CAAA;AAAA,IACF;AACD,WAAO,YAAY,IAAI,KAAK,WAAW,GAAG;AAAA,EAC9C;AACA;AACA,SAAS,gBAAgB,KAAK,MAAM;AAClC,SAAO,IAAI,WAAW,CAAC,aAAa;AAClC,UAAM,aAAa,IAAI,gBAAiB,GAAE,SAAS,WAAW;AAC9D,WAAO,MAAM,KAAK,EAAE,GAAG,MAAM,QAAQ,WAAW,OAAQ,CAAA,EAAE;AAAA,MACxD,CAAC,aAAa;AACZ,iBAAS,KAAK,QAAQ,GAAG,SAAS,SAAU;AAAA,MAC7C;AAAA,MACD,CAAC,QAAQ;AACP,eAAO,WAAW,SAAS,MAAM,GAAG;AAAA,MAC5C;AAAA,IACA,GAAO,MAAM,WAAW,MAAO;AAAA,EAC/B,CAAG;AACH;AACA,MAAM,cAA8B,oBAAI,IAAK;AAC7C,MAAM,yBAAyB;AAAA,EAC7B;AAAA,EACA;AAAA,EACA,YAAYF,SAAQ,aAAa;AAC/B,SAAK,UAAUA,SAAQ,KAAK,eAAe;AAAA,EAC/C;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAOE,OAAO,MAAM,SAAS;AACpB,WAAO,QAAQ,KAAK,SAAS,KAAK,cAAc,OAAO,MAAM,OAAO;AAAA,EACxE;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAOE,KAAK,MAAM,SAAS;AAClB,WAAO,QAAQ,KAAK,SAAS,KAAK,cAAc,SAAS,MAAM,OAAO;AAAA,EAC1E;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAME,OAAO,MAAM;AACX,WAAO,QAAQ,KAAK,SAAS,KAAK,cAAc,UAAU,IAAI;AAAA,EAClE;AAAA;AAAA;AAAA;AAAA,EAIE,OAAO;AACL,WAAO,SAAS,KAAK,SAAS,KAAK,cAAc;AAAA,MAC/C,KAAK;AAAA,MACL,KAAK;AAAA,IACX,CAAK;AAAA,EACL;AACA;AACA,MAAM,eAAe;AAAA,EACnB;AAAA,EACA;AAAA,EACA,YAAYA,SAAQ,aAAa;AAC/B,SAAK,UAAUA,SAAQ,KAAK,eAAe;AAAA,EAC/C;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAOE,OAAO,MAAM,SAAS;AACpB,WAAO,cAAc,WAAW,KAAK,QAAQ,OAAQ,CAAA,GAAG;AAAA,MACtD,QAAQ,KAAK,SAAS,KAAK,cAAc,OAAO,MAAM,OAAO;AAAA,IAC9D;AAAA,EACL;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAOE,KAAK,MAAM,SAAS;AAClB,WAAO,cAAc,WAAW,KAAK,QAAQ,OAAQ,CAAA,GAAG;AAAA,MACtD,QAAQ,KAAK,SAAS,KAAK,cAAc,SAAS,MAAM,OAAO;AAAA,IAChE;AAAA,EACL;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAME,OAAO,MAAM;AACX,WAAO,cAAc,WAAW,KAAK,QAAQ,OAAQ,CAAA,GAAG,cAAc,QAAQ,KAAK,SAAS,KAAK,cAAc,UAAU,IAAI,CAAC;AAAA,EAClI;AAAA;AAAA;AAAA;AAAA,EAIE,OAAO;AACL,WAAO,cAAc,WAAW,KAAK,QAAQ,OAAQ,CAAA,GAAG;AAAA,MACtD,SAAS,KAAK,SAAS,KAAK,cAAc,EAAE,KAAK,aAAa,KAAK,KAAM,CAAA;AAAA,IAC1E;AAAA,EACL;AACA;AACA,SAAS,QAAQA,SAAQ,aAAa,QAAQ,MAAM,SAAS;AAC3D,SAAO,cAAc,WAAWA,QAAO,OAAQ,CAAA,GAAG,QAAQ,IAAI,GAAG,SAASA,SAAQ,aAAa;AAAA,IAC7F;AAAA,IACA,KAAK,aAAa,IAAI;AAAA,IACtB,MAAM;AAAA,IACN,KAAK;AAAA,EACT,CAAG;AACH;AACA,MAAM,yBAAyB;AAAA,EAC7B;AAAA,EACA;AAAA,EACA,YAAYA,SAAQ,aAAa;AAC/B,SAAK,UAAUA,SAAQ,KAAK,eAAe;AAAA,EAC/C;AAAA,EACE,KAAK,SAAS;AACZ,kBAAc,YAAY,KAAK,QAAQ,OAAM,CAAE;AAC/C,UAAM,MAAM,SAAS,mBAAmB,QAAK,mCAAmC;AAChF,WAAO,SAAS,KAAK,SAAS,KAAK,cAAc,EAAE,KAAK;AAAA,EAC5D;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAME,QAAQ,YAAY;AAClB,WAAO,cAAc,YAAY,KAAK,QAAQ,OAAM,CAAE,GAAG,SAAS,KAAK,SAAS,KAAK,cAAc,EAAE,KAAK,aAAa,UAAU,IAAI;AAAA,EACzI;AACA;AACA,MAAM,eAAe;AAAA,EACnB;AAAA,EACA;AAAA,EACA,YAAYA,SAAQ,aAAa;AAC/B,SAAK,UAAUA,SAAQ,KAAK,eAAe;AAAA,EAC/C;AAAA,EACE,KAAK,SAAS;AACZ,kBAAc,YAAY,KAAK,QAAQ,OAAM,CAAE;AAC/C,UAAM,MAAM,SAAS,mBAAmB,QAAK,mCAAmC;AAChF,WAAO,cAAc,SAAS,KAAK,SAAS,KAAK,cAAc,EAAE,IAAG,CAAE,CAAC;AAAA,EAC3E;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAME,QAAQ,YAAY;AAClB,WAAO,cAAc,YAAY,KAAK,QAAQ,OAAQ,CAAA,GAAG;AAAA,MACvD,SAAS,KAAK,SAAS,KAAK,cAAc,EAAE,KAAK,aAAa,UAAU,GAAI,CAAA;AAAA,IAC7E;AAAA,EACL;AACA;AACA,MAAM,oBAAoB;AAAA,EACxB;AAAA,EACA;AACF,GAAG,uBAAuB,CAAC,aAAa,cAAc,YAAY,aAAa,aAAa,SAAS,IAAI,WAAW,WAAW;AAC/H,SAAS,wBAAwB,IAAI;AAAA,EACnC;AAAA,EACA;AAAA,EACA,UAAAD;AACF,GAAG;AACD,MAAI,eAAeA,UAAS,KAAK;AAC/B,UAAM,YAAY,qBAAqB,aAAa,SAAS;AAC7D,WAAO,uBAAuB,WAAWA,SAAQ,GAAG;AAAA,EACxD;AACE,MAAIA,UAAS,KAAK;AAChB,UAAM,UAAU,UAAUA,UAAS,GAAG,GAAG,YAAY,YAAYA,UAAS,GAAG;AAC7E,QAAI,CAAC,WAAW,CAAC;AACf,YAAM,IAAI;AAAA,QACR,KAAK,EAAE;AAAA,MACR;AACH,QAAI,WAAW;AACb,UAAI;AACF,cAAM,IAAI;AAAA,UACR,KAAK,EAAE,yCAAyCA,UAAS,GAAG,+CAA+C,SAAS;AAAA,QACrH;AACH,YAAM,iBAAiB,iBAAiBA,UAAS,GAAG;AACpD,UAAI,mBAAmB;AACrB,cAAM,IAAI;AAAA,UACR,KAAK,EAAE,yCAAyCA,UAAS,GAAG,mDAAmD,SAAS,mDAAmD,cAAc;AAAA,QAC1L;AAAA,IACT;AACI,WAAOA,UAAS;AAAA,EACpB;AACE,MAAI;AACF,WAAO,qBAAqB,aAAa,SAAS;AACpD,QAAM,IAAI,MAAM,KAAK,EAAE,kEAAkE;AAC3F;AACA,MAAM,UAAU,CAAC,kBAAkB,iBAAiB;AAClD,MAAI,OAAO,oBAAoB,YAAY,qBAAqB,SAAS,eAAe,oBAAoB,cAAc,mBAAmB;AAC3I,UAAM,EAAE,YAAY,kBAAiB,GAAI,WAAW,CAAA,EAAI,IAAG;AAC3D,WAAO,CAAC,WAAW,UAAU,YAAY;AAAA,EAC7C;AACE,SAAO,CAAC,kBAAiB,GAAI,CAAE,GAAE,gBAAgB;AACnD,GAAG,gBAAgB,CAAC,kBAAkB,iBAAiB;AACrD,QAAM,CAAC,WAAW,UAAU,OAAO,IAAI,QAAQ,kBAAkB,YAAY,GAAG,gBAAgB;AAAA,IAC9F,GAAG;AAAA,IACH,aAAa,SAAS,eAAe;AAAA,EACtC;AACD,SAAO,EAAE,QAAQ;AAAA,IACf,YAAY;AAAA,IACZ;AAAA,IACA,UAAU;AAAA,EACX,GAAE,QAAS;AACd;AACA,MAAM,yBAAyB;AAAA,EAC7B;AAAA,EACA;AAAA,EACA,YAAYC,SAAQ,aAAa;AAC/B,SAAK,UAAUA,SAAQ,KAAK,eAAe;AAAA,EAC/C;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EA4BE,IAAI,EAAE,UAAW,GAAE,SAAS;AAC1B,WAAO;AAAA,MACL,KAAK;AAAA,MACL,KAAK;AAAA,MACL,cAAc,SAAS;AAAA,MACvB;AAAA,IACD;AAAA,EACL;AAAA,EACE,OAAO,kBAAkB,cAAc;AACrC,UAAM,EAAE,QAAQ,QAAS,IAAG,cAAc,kBAAkB,YAAY,GAAG,EAAE,WAAW,SAAQ,IAAK;AACrG,WAAO,QAAQ,KAAK,SAAS,KAAK,cAAc,QAAQ,OAAO,EAAE;AAAA,MAC/DG,IAAM,CAAC,kBAAkB;AAAA,QACvB,GAAG;AAAA,QACH;AAAA,QACA;AAAA,MACR,EAAQ;AAAA,IACH;AAAA,EACL;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAcE,KAAK,EAAE,WAAW,MAAK,GAAI,SAAS;AAClC,UAAM,aAAa;AAAA,MACjB,YAAY;AAAA,MACZ;AAAA,MACA;AAAA,IACD;AACD,WAAO,QAAQ,KAAK,SAAS,KAAK,cAAc,YAAY,OAAO;AAAA,EACvE;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAmBE,QAAQ,EAAE,UAAW,GAAE,SAAS;AAC9B,UAAM,gBAAgB;AAAA,MACpB,YAAY;AAAA,MACZ;AAAA,IACD;AACD,WAAO,QAAQ,KAAK,SAAS,KAAK,cAAc,eAAe,OAAO;AAAA,EAC1E;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAgBE,QAAQ,EAAE,UAAW,GAAE,SAAS;AAC9B,UAAM,gBAAgB;AAAA,MACpB,YAAY;AAAA,MACZ;AAAA,IACD;AACD,WAAO,QAAQ,KAAK,SAAS,KAAK,cAAc,eAAe,OAAO;AAAA,EAC1E;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAcE,UAAU,EAAE,UAAW,GAAE,SAAS;AAChC,UAAM,kBAAkB;AAAA,MACtB,YAAY;AAAA,MACZ;AAAA,IACD;AACD,WAAO,QAAQ,KAAK,SAAS,KAAK,cAAc,iBAAiB,OAAO;AAAA,EAC5E;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAiBE,SAAS,EAAE,WAAW,UAAS,GAAI,SAAS;AAC1C,UAAM,iBAAiB;AAAA,MACrB,YAAY;AAAA,MACZ;AAAA,MACA;AAAA,IACD;AACD,WAAO,QAAQ,KAAK,SAAS,KAAK,cAAc,gBAAgB,OAAO;AAAA,EAC3E;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAgBE,WAAW,EAAE,UAAW,GAAE,SAAS;AACjC,UAAM,mBAAmB;AAAA,MACvB,YAAY;AAAA,MACZ;AAAA,IACD;AACD,WAAO,QAAQ,KAAK,SAAS,KAAK,cAAc,kBAAkB,OAAO;AAAA,EAC7E;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAcE,OAAO,EAAE,UAAW,GAAE,SAAS;AAC7B,UAAM,eAAe;AAAA,MACnB,YAAY;AAAA,MACZ;AAAA,IACD;AACD,WAAO,QAAQ,KAAK,SAAS,KAAK,cAAc,cAAc,OAAO;AAAA,EACzE;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAaE,eAAe,EAAE,UAAW,GAAE,SAAS;AACrC,WAAO,qBAAqB,KAAK,SAAS,KAAK,cAAc,WAAW,OAAO;AAAA,EACnF;AACA;AACA,MAAM,eAAe;AAAA,EACnB;AAAA,EACA;AAAA,EACA,YAAYH,SAAQ,aAAa;AAC/B,SAAK,UAAUA,SAAQ,KAAK,eAAe;AAAA,EAC/C;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EA2BE,IAAI,EAAE,UAAW,GAAE,SAAS;AAC1B,WAAO;AAAA,MACL;AAAA,QACE,KAAK;AAAA,QACL,KAAK;AAAA,QACL,cAAc,SAAS;AAAA,QACvB;AAAA,MACR;AAAA,IACK;AAAA,EACL;AAAA,EACE,MAAM,OAAO,kBAAkB,cAAc;AAC3C,UAAM,EAAE,QAAQ,QAAS,IAAG,cAAc,kBAAkB,YAAY,GAAG,EAAE,WAAW,SAAQ,IAAK;AACrG,WAAO,EAAE,GAAG,MAAM;AAAA,MAChB,QAAQ,KAAK,SAAS,KAAK,cAAc,QAAQ,OAAO;AAAA,IAC9D,GAAO,WAAW,SAAU;AAAA,EAC5B;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAcE,KAAK,EAAE,WAAW,MAAK,GAAI,SAAS;AAClC,UAAM,aAAa;AAAA,MACjB,YAAY;AAAA,MACZ;AAAA,MACA;AAAA,IACD;AACD,WAAO,cAAc,QAAQ,KAAK,SAAS,KAAK,cAAc,YAAY,OAAO,CAAC;AAAA,EACtF;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAmBE,QAAQ,EAAE,UAAW,GAAE,SAAS;AAC9B,UAAM,gBAAgB;AAAA,MACpB,YAAY;AAAA,MACZ;AAAA,IACD;AACD,WAAO,cAAc,QAAQ,KAAK,SAAS,KAAK,cAAc,eAAe,OAAO,CAAC;AAAA,EACzF;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAgBE,QAAQ,EAAE,UAAW,GAAE,SAAS;AAC9B,UAAM,gBAAgB;AAAA,MACpB,YAAY;AAAA,MACZ;AAAA,IACD;AACD,WAAO,cAAc,QAAQ,KAAK,SAAS,KAAK,cAAc,eAAe,OAAO,CAAC;AAAA,EACzF;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAcE,UAAU,EAAE,UAAW,GAAE,SAAS;AAChC,UAAM,kBAAkB;AAAA,MACtB,YAAY;AAAA,MACZ;AAAA,IACD;AACD,WAAO,cAAc,QAAQ,KAAK,SAAS,KAAK,cAAc,iBAAiB,OAAO,CAAC;AAAA,EAC3F;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAiBE,SAAS,EAAE,WAAW,UAAS,GAAI,SAAS;AAC1C,UAAM,iBAAiB;AAAA,MACrB,YAAY;AAAA,MACZ;AAAA,MACA;AAAA,IACD;AACD,WAAO,cAAc,QAAQ,KAAK,SAAS,KAAK,cAAc,gBAAgB,OAAO,CAAC;AAAA,EAC1F;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAgBE,WAAW,EAAE,UAAW,GAAE,SAAS;AACjC,UAAM,mBAAmB;AAAA,MACvB,YAAY;AAAA,MACZ;AAAA,IACD;AACD,WAAO,cAAc,QAAQ,KAAK,SAAS,KAAK,cAAc,kBAAkB,OAAO,CAAC;AAAA,EAC5F;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAcE,OAAO,EAAE,UAAW,GAAE,SAAS;AAC7B,UAAM,eAAe;AAAA,MACnB,YAAY;AAAA,MACZ;AAAA,IACD;AACD,WAAO,cAAc,QAAQ,KAAK,SAAS,KAAK,cAAc,cAAc,OAAO,CAAC;AAAA,EACxF;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAaE,eAAe,EAAE,UAAW,GAAE,SAAS;AACrC,WAAO,cAAc,qBAAqB,KAAK,SAAS,KAAK,cAAc,WAAW,OAAO,CAAC;AAAA,EAClG;AACA;AACA,MAAM,sBAAsB;AAAA,EAC1B;AAAA,EACA;AAAA,EACA,YAAYA,SAAQ,aAAa;AAC/B,SAAK,UAAUA,SAAQ,KAAK,eAAe;AAAA,EAC/C;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAME,QAAQ,IAAI;AACV,WAAO;AAAA,MACL,KAAK;AAAA,MACL,KAAK;AAAA,MACL,EAAE,KAAK,UAAU,EAAE,GAAE;AAAA,IACtB;AAAA,EACL;AACA;AACA,MAAM,YAAY;AAAA,EAChB;AAAA,EACA;AAAA,EACA,YAAYA,SAAQ,aAAa;AAC/B,SAAK,UAAUA,SAAQ,KAAK,eAAe;AAAA,EAC/C;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAME,QAAQ,IAAI;AACV,WAAO;AAAA,MACL,SAAS,KAAK,SAAS,KAAK,cAAc;AAAA,QACxC,KAAK,UAAU,EAAE;AAAA,MAClB,CAAA;AAAA,IACF;AAAA,EACL;AACA;AACA,MAAM,uBAAuB;AAAA,EAC3B;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA;AAAA;AAAA;AAAA,EAIA;AAAA,EACA;AAAA;AAAA;AAAA;AAAA,EAIA,SAAS;AAAA,EACT,YAAY,aAAaV,UAAS,eAAe;AAC/C,SAAK,OAAOA,OAAM,GAAG,KAAK,eAAe,aAAa,KAAK,SAAS,IAAI,uBAAuB,MAAM,KAAK,YAAY,GAAG,KAAK,WAAW,IAAI,yBAAyB,MAAM,KAAK,YAAY,GAAG,KAAK,OAAO,IAAI,WAAW,IAAI,GAAG,KAAK,WAAW,IAAI,yBAAyB,MAAM,KAAK,YAAY,GAAG,KAAK,QAAQ,IAAI,sBAAsB,MAAM,KAAK,YAAY,GAAG,KAAK,QAAQ;AAAA,MACrX,QAAQ,IAAI,6BAA6B,MAAM,KAAK,YAAY;AAAA,IACtE,GAAO,KAAK,WAAW,IAAI,yBAAyB,MAAM,KAAK,YAAY;AAAA,EAC3E;AAAA;AAAA;AAAA;AAAA,EAIE,QAAQ;AACN,WAAO,IAAI,uBAAuB,KAAK,cAAc,KAAK,OAAM,CAAE;AAAA,EACtE;AAAA,EACE,OAAO,WAAW;AAChB,QAAI,cAAc;AAChB,aAAO,EAAE,GAAG,KAAK,cAAe;AAClC,QAAI,KAAK,iBAAiB,KAAK,cAAc,qBAAqB;AAChE,YAAM,IAAI;AAAA,QACR;AAAA,MACD;AACH,WAAO,KAAK,gBAAgB,WAAW,WAAW,KAAK,iBAAiB,CAAE,CAAA,GAAG;AAAA,EACjF;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAME,WAAW,WAAW;AACpB,UAAM,aAAa,KAAK,OAAQ;AAChC,WAAO,IAAI,uBAAuB,KAAK,cAAc;AAAA,MACnD,GAAG;AAAA,MACH,GAAG;AAAA,MACH,OAAO;AAAA,QACL,GAAG,WAAW,SAAS,CAAE;AAAA,QACzB,GAAG,OAAO,WAAW,SAAS,YAAY,EAAE,SAAS,UAAU,MAAK,IAAK,WAAW,SAAS,CAAA;AAAA,MACrG;AAAA,IACA,CAAK;AAAA,EACL;AAAA,EACE,MAAM,OAAO,QAAQ,SAAS;AAC5B,WAAO;AAAA,MACL;AAAA,MACA,KAAK;AAAA,MACL,KAAK,cAAc;AAAA,MACnB;AAAA,MACA;AAAA,MACA;AAAA,IACD;AAAA,EACL;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAOE,YAAY,IAAI,SAAS;AACvB,WAAO,aAAa,MAAM,KAAK,cAAc,IAAI,OAAO;AAAA,EAC5D;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAUE,aAAa,KAAK,SAAS;AACzB,WAAO,cAAc,MAAM,KAAK,cAAc,KAAK,OAAO;AAAA,EAC9D;AAAA,EACE,OAAOS,WAAU,SAAS;AACxB,WAAO,QAAQ,MAAM,KAAK,cAAcA,WAAU,UAAU,OAAO;AAAA,EACvE;AAAA,EACE,kBAAkBA,WAAU,SAAS;AACnC,WAAO,mBAAmB,MAAM,KAAK,cAAcA,WAAU,OAAO;AAAA,EACxE;AAAA,EACE,gBAAgBA,WAAU,SAAS;AACjC,WAAO,iBAAiB,MAAM,KAAK,cAAcA,WAAU,OAAO;AAAA,EACtE;AAAA,EACE,cAAc;AAAA,IACZ,UAAAA;AAAA,IACA;AAAA,IACA;AAAA,EACD,GAAE,SAAS;AACV,UAAM,oBAAoB,wBAAwB,iBAAiB;AAAA,MACjE,UAAAA;AAAA,MACA;AAAA,MACA;AAAA,IACD,CAAA,GAAG,kBAAkB,EAAE,GAAGA,WAAU,KAAK,qBAAqB,qBAAqB,eAAe,eAAeA,UAAS,GAAG;AAC9H,WAAO;AAAA,MACL;AAAA,MACA,KAAK;AAAA,MACL;AAAA,MACA;AAAA,MACA;AAAA,IACD;AAAA,EACL;AAAA,EACE,OAAO,WAAW,SAAS;AACzB,WAAO,QAAQ,MAAM,KAAK,cAAc,WAAW,OAAO;AAAA,EAC9D;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EA6BE,eAAe,EAAE,WAAW,YAAW,GAAI,OAAO,SAAS;AACzD,UAAM,oBAAoB,qBAAqB,aAAa,SAAS;AACrE,WAAO,gBAAgB,MAAM,KAAK,cAAc,mBAAmB,OAAO,OAAO;AAAA,EACrF;AAAA,EACE,eAAe;AAAA,IACb,UAAAA;AAAA,IACA;AAAA,IACA;AAAA,EACD,GAAE,SAAS;AACV,UAAM,oBAAoB,wBAAwB,kBAAkB;AAAA,MAClE,UAAAA;AAAA,MACA;AAAA,MACA;AAAA,IACD,CAAA,GAAG,kBAAkB,EAAE,GAAGA,WAAU,KAAK,kBAAmB;AAC7D,WAAO,gBAAgB,MAAM,KAAK,cAAc,iBAAiB,OAAO;AAAA,EAC5E;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAsBE,iBAAiB,EAAE,WAAW,YAAW,GAAI,SAAS;AACpD,UAAM,YAAY,aAAa,aAAa,SAAS;AACrD,WAAO,kBAAkB,MAAM,KAAK,cAAc,WAAW,aAAa,OAAO;AAAA,EACrF;AAAA,EACE,OAAO,YAAY,SAAS;AAC1B,WAAO,QAAQ,MAAM,KAAK,cAAc,YAAY,OAAO;AAAA,EAC/D;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAQE,MAAM,WAAW,YAAY;AAC3B,WAAO,IAAI,gBAAgB,WAAW,YAAY,IAAI;AAAA,EAC1D;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAME,YAAY,YAAY;AACtB,WAAO,IAAI,sBAAsB,YAAY,IAAI;AAAA,EACrD;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAOE,OAAO,YAAY,SAAS;AAC1B,WAAO,QAAQ,MAAM,KAAK,cAAc,YAAY,OAAO;AAAA,EAC/D;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAME,QAAQ,SAAS;AACf,WAAO,SAAS,MAAM,KAAK,cAAc,OAAO;AAAA,EACpD;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAOE,OAAO,KAAK,WAAW;AACrB,WAAO,QAAQ,MAAM,KAAK,SAAS;AAAA,EACvC;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAOE,WAAW,WAAW,MAAM;AAC1B,WAAO,YAAY,MAAM,WAAW,IAAI;AAAA,EAC5C;AACA;AACA,MAAM,aAAa;AAAA,EACjB;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA;AAAA;AAAA;AAAA,EAIA;AAAA;AAAA;AAAA;AAAA,EAIA;AAAA,EACA;AAAA;AAAA;AAAA;AAAA,EAIA,SAAS;AAAA,EACT,YAAY,aAAaT,UAAS,eAAe;AAC/C,SAAK,OAAOA,OAAM,GAAG,KAAK,eAAe,aAAa,KAAK,SAAS,IAAI,aAAa,MAAM,KAAK,YAAY,GAAG,KAAK,WAAW,IAAI,eAAe,MAAM,KAAK,YAAY,GAAG,KAAK,OAAO,IAAI,WAAW,IAAI,GAAG,KAAK,WAAW,IAAI,eAAe,MAAM,KAAK,YAAY,GAAG,KAAK,QAAQ,IAAI,YAAY,MAAM,KAAK,YAAY,GAAG,KAAK,QAAQ;AAAA,MAC7U,QAAQ,IAAI,mBAAmB,MAAM,KAAK,YAAY;AAAA,IACvD,GAAE,KAAK,WAAW,IAAI,eAAe,MAAM,KAAK,YAAY,GAAG,KAAK,aAAa,IAAI,uBAAuB,aAAaA,OAAM;AAAA,EACpI;AAAA;AAAA;AAAA;AAAA,EAIE,QAAQ;AACN,WAAO,IAAI,aAAa,KAAK,cAAc,KAAK,OAAM,CAAE;AAAA,EAC5D;AAAA,EACE,OAAO,WAAW;AAChB,QAAI,cAAc;AAChB,aAAO,EAAE,GAAG,KAAK,cAAe;AAClC,QAAI,KAAK,iBAAiB,KAAK,cAAc,qBAAqB;AAChE,YAAM,IAAI;AAAA,QACR;AAAA,MACD;AACH,WAAO,KAAK,cAAc,KAAK,WAAW,OAAO,SAAS,GAAG,KAAK,gBAAgB,WAAW,WAAW,KAAK,iBAAiB,CAAE,CAAA,GAAG;AAAA,EACvI;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAME,WAAW,WAAW;AACpB,UAAM,aAAa,KAAK,OAAQ;AAChC,WAAO,IAAI,aAAa,KAAK,cAAc;AAAA,MACzC,GAAG;AAAA,MACH,GAAG;AAAA,MACH,OAAO;AAAA,QACL,GAAG,WAAW,SAAS,CAAE;AAAA,QACzB,GAAG,OAAO,WAAW,SAAS,YAAY,EAAE,SAAS,UAAU,MAAK,IAAK,WAAW,SAAS,CAAA;AAAA,MACrG;AAAA,IACA,CAAK;AAAA,EACL;AAAA,EACE,MAAM,OAAO,QAAQ,SAAS;AAC5B,WAAO;AAAA,MACL;AAAA,QACE;AAAA,QACA,KAAK;AAAA,QACL,KAAK,cAAc;AAAA,QACnB;AAAA,QACA;AAAA,QACA;AAAA,MACR;AAAA,IACK;AAAA,EACL;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAOE,YAAY,IAAI,SAAS;AACvB,WAAO,cAAc,aAAa,MAAM,KAAK,cAAc,IAAI,OAAO,CAAC;AAAA,EAC3E;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAUE,aAAa,KAAK,SAAS;AACzB,WAAO,cAAc,cAAc,MAAM,KAAK,cAAc,KAAK,OAAO,CAAC;AAAA,EAC7E;AAAA,EACE,OAAOS,WAAU,SAAS;AACxB,WAAO;AAAA,MACL,QAAQ,MAAM,KAAK,cAAcA,WAAU,UAAU,OAAO;AAAA,IAC7D;AAAA,EACL;AAAA,EACE,kBAAkBA,WAAU,SAAS;AACnC,WAAO;AAAA,MACL,mBAAmB,MAAM,KAAK,cAAcA,WAAU,OAAO;AAAA,IAC9D;AAAA,EACL;AAAA,EACE,gBAAgBA,WAAU,SAAS;AACjC,WAAO;AAAA,MACL,iBAAiB,MAAM,KAAK,cAAcA,WAAU,OAAO;AAAA,IAC5D;AAAA,EACL;AAAA,EACE,cAAc;AAAA,IACZ,UAAAA;AAAA,IACA;AAAA,IACA;AAAA,EACD,GAAE,SAAS;AACV,UAAM,oBAAoB,wBAAwB,iBAAiB;AAAA,MACjE,UAAAA;AAAA,MACA;AAAA,MACA;AAAA,IACD,CAAA,GAAG,kBAAkB,EAAE,GAAGA,WAAU,KAAK,qBAAqB,qBAAqB,eAAe,eAAeA,UAAS,GAAG;AAC9H,WAAO;AAAA,MACL;AAAA,QACE;AAAA,QACA,KAAK;AAAA,QACL;AAAA,QACA;AAAA,QACA;AAAA,MACR;AAAA,IACK;AAAA,EACL;AAAA,EACE,OAAO,WAAW,SAAS;AACzB,WAAO,cAAc,QAAQ,MAAM,KAAK,cAAc,WAAW,OAAO,CAAC;AAAA,EAC7E;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EA6BE,eAAe,EAAE,WAAW,YAAW,GAAI,OAAO,SAAS;AACzD,UAAM,oBAAoB,qBAAqB,aAAa,SAAS;AACrE,WAAO;AAAA,MACL,gBAAgB,MAAM,KAAK,cAAc,mBAAmB,OAAO,OAAO;AAAA,IAC3E;AAAA,EACL;AAAA,EACE,eAAe;AAAA,IACb,UAAAA;AAAA,IACA;AAAA,IACA;AAAA,EACD,GAAE,SAAS;AACV,UAAM,oBAAoB,wBAAwB,kBAAkB;AAAA,MAClE,UAAAA;AAAA,MACA;AAAA,MACA;AAAA,IACD,CAAA,GAAG,kBAAkB,EAAE,GAAGA,WAAU,KAAK,kBAAmB;AAC7D,WAAO;AAAA,MACL,gBAAgB,MAAM,KAAK,cAAc,iBAAiB,OAAO;AAAA,IAClE;AAAA,EACL;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAsBE,iBAAiB,EAAE,WAAW,YAAW,GAAI,SAAS;AACpD,UAAM,YAAY,aAAa,aAAa,SAAS;AACrD,WAAO;AAAA,MACL,kBAAkB,MAAM,KAAK,cAAc,WAAW,aAAa,OAAO;AAAA,IAC3E;AAAA,EACL;AAAA,EACE,OAAO,YAAY,SAAS;AAC1B,WAAO,cAAc,QAAQ,MAAM,KAAK,cAAc,YAAY,OAAO,CAAC;AAAA,EAC9E;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAQE,MAAM,YAAY,YAAY;AAC5B,WAAO,IAAI,MAAM,YAAY,YAAY,IAAI;AAAA,EACjD;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAME,YAAY,YAAY;AACtB,WAAO,IAAI,YAAY,YAAY,IAAI;AAAA,EAC3C;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAQE,OAAO,YAAY,SAAS;AAC1B,WAAO,cAAc,QAAQ,MAAM,KAAK,cAAc,YAAY,OAAO,CAAC;AAAA,EAC9E;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAQE,QAAQ,SAAS;AACf,WAAO,cAAc,SAAS,MAAM,KAAK,cAAc,OAAO,CAAC;AAAA,EACnE;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAWE,YAAY,UAAU,MAAM,SAAS;AACnC,WAAO,cAAc,aAAa,MAAM,KAAK,cAAc,UAAU,MAAM,OAAO,CAAC;AAAA,EACvF;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAOE,OAAO,KAAK,WAAW;AACrB,WAAO,QAAQ,MAAM,KAAK,SAAS;AAAA,EACvC;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAOE,WAAW,WAAW,MAAM;AAC1B,WAAO,YAAY,MAAM,WAAW,IAAI;AAAA,EAC5C;AACA;AACA,SAAS,0BAA0B,gBAAgB,kBAAkB;AACnE,SAAO,EAAE,WAAW,kBAAkB,cAAc,GAAG,cAAc,CAACT,YAAW;AAC/E,UAAM,kBAAkB,kBAAkB,cAAc;AACxD,WAAO,IAAI;AAAA,MACT,CAAC,SAAS,gBAAgB,cAAc,iBAAiB;AAAA,QACvD,cAAc;AAAA,QACd,YAAYA,QAAO;AAAA,QACnB,YAAYA,QAAO;AAAA,QACnB,GAAG;AAAA,MACX,CAAO;AAAA,MACDA;AAAA,IACD;AAAA,EACL,EAAK;AACL;AAMA,IAAI,gBAAgB,CAAE;AACjB,MAAC,MAAM,0BAA0B,eAAe,YAAY,GAA8B,eAAe,IAAI;AChgF3G,MAAM,YAAY;AAAA,EACvB,EAAC,IAAI,MAAM,OAAO,UAAS;AAAA,EAC3B,EAAC,IAAI,MAAM,OAAO,SAAQ;AAAA,EAC1B,EAAC,IAAI,MAAM,OAAO,SAAQ;AAAA,EAC1B,EAAC,IAAI,MAAM,OAAO,UAAS;AAAA,EAC3B,EAAC,IAAI,MAAM,OAAO,UAAS;AAAA,EAC3B,EAAC,IAAI,MAAM,OAAO,qBAAoB;AAAA,EACtC,EAAC,IAAI,MAAM,OAAO,UAAS;AAAA,EAC3B,EAAC,IAAI,MAAM,OAAO,WAAU;AAAA,EAC5B,EAAC,IAAI,MAAM,OAAO,SAAQ;AAAA,EAC1B,EAAC,IAAI,MAAM,OAAO,SAAQ;AAAA,EAC1B,EAAC,IAAI,MAAM,OAAO,QAAO;AAAA,EACzB,EAAC,IAAI,MAAM,OAAO,UAAS;AAAA,EAC3B,EAAC,IAAI,MAAM,OAAO,SAAQ;AAAA,EAC1B,EAAC,IAAI,MAAM,OAAO,aAAY;AAAA,EAC9B,EAAC,IAAI,MAAM,OAAO,YAAW;AAAA,EAC7B,EAAC,IAAI,MAAM,OAAO,aAAY;AAAA,EAC9B,EAAC,IAAI,MAAM,OAAO,UAAS;AAAA,EAC3B,EAAC,IAAI,MAAM,OAAO,QAAO;AAAA,EACzB,EAAC,IAAI,MAAM,OAAO,SAAQ;AAAA,EAC1B,EAAC,IAAI,MAAM,OAAO,YAAW;AAAA,EAC7B,EAAC,IAAI,MAAM,OAAO,WAAU;AAAA,EAC5B,EAAC,IAAI,MAAM,OAAO,UAAS;AAAA,EAC3B,EAAC,IAAI,MAAM,OAAO,QAAO;AAAA,EACzB,EAAC,IAAI,MAAM,OAAO,YAAW;AAAA,EAC7B,EAAC,IAAI,MAAM,OAAO,SAAQ;AAAA,EAC1B,EAAC,IAAI,MAAM,OAAO,YAAW;AAAA,EAC7B,EAAC,IAAI,MAAM,OAAO,UAAS;AAAA,EAC3B,EAAC,IAAI,MAAM,OAAO,qBAAoB;AAAA,EACtC,EAAC,IAAI,MAAM,OAAO,WAAU;AAAA,EAC5B,EAAC,IAAI,MAAM,OAAO,WAAU;AAC9B;AC3BA,MAAM,QAAQ,QAAQ,IAAI;AAE1B,MAAM,SAAS,aAAa;AAAA,EAC1B,WAAW;AAAA,EACX,SAAS;AAAA,EACT,QAAQ;AAAA,EACR,YAAY;AAAA,EACZ;AACF,CAAC;AAGM,MAAM,UAAU,qBAAqB,OAAO,EAAC,SAAS,YAAW;AAI7D,UAAA,IAAI,aAAa,OAAO;AACzB,UAAA,IAAI,WAAW,KAAK;AAE5B,QAAM,oBAAoB,UAAU,OAAO,CAAQ,SAAA,KAAK,OAAO,IAAI;AAEnE,aAAW,YAAY,mBAAmB;AAClC,UAAA,OAAO,MAAM,OAAO,UAAU;AAAA,MAClC,UAAU;AAAA,MACV,YAAY,MAAM,KAAK;AAAA,MACvB,gBAAgB,EAAC,WAAW,SAAQ;AAAA,MACpC,cAAc,EAAC,IAAI,MAAM,OAAO,UAAS;AAAA,MACzC,YAAY;AAAA,MACZ,YAAY;AAAA,IAAA,CACb;AAEO,YAAA,IAAI,mBAAmB,MAAM,KAAK,GAAG,OAAO,SAAS,KAAK,EAAE;AAAA,EAAA;AAExE,CAAC;", "x_google_ignoreList": [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65, 66, 67, 68, 69, 70, 71, 72, 73, 74, 75, 76, 77, 78, 79, 80, 81, 82, 83, 84, 85]}