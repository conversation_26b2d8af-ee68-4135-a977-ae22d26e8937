import { defineType, defineField } from 'sanity';
import { CupSodaIcon} from 'lucide-react'

export default defineType({
  name: 'customer',
  title: 'Customer',
  icon: CupSodaIcon,
  type: 'document',
  fields: [
    defineField({
      name: 'name',
      title: 'Name',
      type: 'string',
      description: 'Full name of the customer.',
      validation: (Rule) => Rule.required(),
    }),
    defineField({
      name: 'email',
      title: 'Email',
      type: 'email',
      description: 'Email address of the customer.',
      validation: (Rule) => Rule.required(), // Validate it's an email
    }),
    defineField({
      name: 'phoneNumber',
      title: 'Phone Number',
      type: 'string',
      description: 'Contact phone number of the customer.',
    }),
    defineField({
      name: 'country',
      title: 'Country',
      type: 'string',
      description: 'Country of residence of the customer.',
    }),
    defineField({
      name: 'preferences',
      title: 'Preferences',
      type: 'array',
      of: [{ type: 'string' }],
      description: 'List of customer preferences, e.g., preferred activities or destinations.',
    }),
    defineField({
      name: 'bookingHistory',
      title: 'Booking History',
      type: 'array',
      of: [{ type: 'reference', to: [{ type: 'booking' }] }],
      description: 'References to all bookings made by this customer.',
    }),
  ],
  preview: {
    select: {
      title: 'name',
      subtitle: 'email',
    },
  },
});
