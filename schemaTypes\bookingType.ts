import { BaggageClaim } from 'lucide-react'
import { defineField, defineType } from 'sanity'

export default defineType({
  name: 'booking',
  title: 'Booking',
  icon: BaggageClaim,
  type: 'document',
  fields: [
    defineField({
      name: 'customer',
      title: 'Customer',
      type: 'reference',
      to: [{type: 'customer'}],
      description: 'The customer who made the booking.',
      validation: (Rule) => Rule.required(),
    }),
    defineField({
      name: 'package',
      title: 'Safari Package',
      type: 'reference',
      to: [{type: 'safariPackage'}],
      description: 'The selected safari package for the booking.',
      validation: (Rule) => Rule.required(),
    }),
    defineField({
      name: 'travelers',
      title: 'Number of Travelers',
      type: 'number',
      description: 'The total number of travelers for this booking.',
      validation: (Rule) => Rule.required().min(1),
    }),
    defineField({
      name: 'otherTravelersDetails',
      title: 'Other Travelers Details',
      type: 'array',
      of: [
        {
          type: 'object',
          fields: [
            defineField({
              name: 'title',
              title: 'Title',
              type: 'string',
              options: {
                list: [
                  {title: 'Mr', value: 'mr'},
                  {title: 'Mrs', value: 'mrs'},
                  {title: 'Ms', value: 'ms'},
                ],
              },
              validation: (Rule) => Rule.required(),
            }),
            defineField({
              name: 'firstName',
              title: 'First Name',
              type: 'string',
              validation: (Rule) => Rule.required(),
            }),
            defineField({
              name: 'lastName',
              title: 'Last Name',
              type: 'string',
              validation: (Rule) => Rule.required(),
            }),
          ],
        },
      ],
      description: 'Details of other travelers on the trip.',
    }),
    defineField({
      name: 'travelDates',
      title: 'Travel Dates',
      type: 'object',
      fields: [
        defineField({
          name: 'startDate',
          title: 'Start Date',
          type: 'datetime',
          validation: (Rule) => Rule.required(),
        }),
        defineField({
          name: 'endDate',
          title: 'End Date',
          type: 'datetime',
          validation: (Rule) => Rule.required(),
        }),
      ],
      description: 'The start and end dates of the trip.',
    }),
    defineField({
      name: 'totalPrice',
      title: 'Total Price',
      type: 'number',
      description: 'The total price of the booking.',
      validation: (Rule) => Rule.required().min(0),
    }),
    defineField({
      name: 'status',
      title: 'Status',
      type: 'string',
      options: {
        list: [
          {title: 'Confirmed', value: 'confirmed'},
          {title: 'Pending', value: 'pending'},
          {title: 'Cancelled', value: 'cancelled'},
          {title: 'Completed', value: 'completed'},
        ],
        layout: 'radio',
      },
      description: 'The current status of the booking.',
      validation: (Rule) => Rule.required(),
    }),
  ],
  preview: {
    select: {
      customer: 'customer.name',
      tourPackage: 'package.title',
      travelers: 'travelers',
      startDate: 'travelDates.startDate',
      endDate: 'travelDates.endDate',
      totalPrice: 'totalPrice',
      status: 'status',
      media: 'package.mainImage',
    },
    prepare({customer, tourPackage, travelers, startDate, endDate, totalPrice, status, media}) {
      return {
        title: `${customer} - ${tourPackage}`,
        subtitle: `Travelers: ${travelers}, Dates: ${startDate} - ${endDate}, Price: $${totalPrice}, Status: ${status}`,
        description: `Customer: ${customer}, Package: ${tourPackage}, Travelers: ${travelers}, Dates: ${startDate} - ${endDate}, Price: $${totalPrice}, Status: ${status}`,
        media: media,
      }
    },
  },
})
